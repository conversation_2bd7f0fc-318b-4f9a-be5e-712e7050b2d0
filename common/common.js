import {
	mapActions,
	mapState,
	mapMutations,
	mapGetters
} from "vuex";
import {
	getDw,
	scanCode,
	configWX
} from "@/common/wechat-util.js"
const Plugin = Object.create(null);
Plugin.install = function(Vue, options) {
	Vue.mixin({
		data() {
			return {}
		},
		computed: {
			...mapState({
				pxToRpxRate: state => state.systemInfo.pxToRpxRate,
				menuButtonLeft: state => state.systemInfo.menuButtonLeft,
				menuButtonTop: state => state.systemInfo.menuButtonTop,
				menuButtonWidth: state => state.systemInfo.menuButtonWidth,
				statusNavBarHeight: state => state.systemInfo.statusNavBarHeight,
				wHeight: state => state.systemInfo.windowHeight,
				user: state => state.user,
				isLogin: state => state.isLogin,
				uId: state => state.user.id || '',
				// isVip: state => state.user.level > 10,
				tColor: state => state.system.color,
				fontColor: state => state.system.fontColor,
				// url: state => state.system.attachurl,
				system: state => state.system,
				isIpx: state => state.isIpx,
				sl: state => '$',
				// dw: state => state.system.custom.hbmc,
				// pm: state => state.system.pm,
			}),
			...mapState('dndc', ['smConfig','fxsInfo','fxsSq','latLng','regionId']),
			onImgurl() {
				return getApp().globalData.onImgurl
			},
			isDev() {
				return getApp().globalData.siteInfo.isDev
			}
		},
		methods: {
			...mapActions(["getSystem", "getLoginInfo", "refreshUser", "getLayout","setPayInfo"]),
			...mapActions('dndc', ['getSmConfig', 'getAddInfo','getFxzx','getFxSq','getRegionId']),
			...mapMutations('dndc', ["setCityInfo", 'setAddInfo']),
			go(type,url) {
				// console.log(option)
				switch (type) {
					case undefined:
					case 'navigateTo':
						uni.navigateTo({  //正常跳转
							url: url
						})
						break;
					case 'redirectTo': //关闭当前跳转
						uni.redirectTo({
							url: url
						})
						break;
					case 'switchTab': //跳转tab
						uni.switchTab({
							url: url
						})
						break;
					case 'reLaunch': //关闭所有跳转
						uni.reLaunch({
							url: url
						})
						break;
					case 'navigateBack': //返回上一页
						uni.navigateBack({
							delta: 1
						})
						break;
					case 'back':
						if (getCurrentPages().length > 1 && !url) {
							uni.navigateBack({
								delta: 1
							})
						} else {
							uni.reLaunch({
								url: url
							})
						}
						break;
				}
			},
			getConfigWX() {
				return configWX()
			},
			async goUrl(url) {
				let type = 'navigateTo'
				let path = {
					type:1,//1非首页页面   2上述四个值
					url:''
				}
				let noFound = false
				if(!url.params) {
					console.log('未找到绑定url')
					return
				}
				switch(url.params){
					case 'platform': //平台页面跳转
					//商户入驻外链
					if (url.name.id == 'distribution') {
						await this.getFxzx()
						if(this.fxsInfo.open==2){
							this.util.message('未开启分销商功能', 3)
							return
						}
						await this.getFxSq()
						if (this.fxsSq) {
							let i = this.fxsSq.state
							if (i == 1) {
								this.util.message('请等待平台审核您的申请', 3)
							} else if (i == 3) {
								this.util.message('您的申请已被拒绝', 3)
							} else if (i == 2) {
								this.go('navigateTo','/yb_o2ov2/order/invitation/fxzx')
							}
						} else {
							this.go('navigateTo','/yb_o2ov2/order/invitation/sqfx')
						}
						return
					}
					
					if(url.name.id=='merchantsMoveIn'){
						this.checkLogin()
						if (url.name.url) {
							uni.navigateTo({ 
								url: '/yb_o2ov2/my/partnership/apply-for-joining?src=' + encodeURIComponent(JSON.stringify(url.name.url))
							})
						}
					}
					if(url.name.id=='regionalAgent'){
						this.checkLogin()
						if (url.name.url) {
							uni.navigateTo({ 
								url: '/yb_o2ov2/my/partnership/person-apply?src=' + encodeURIComponent(JSON.stringify(url.name.url))
							})
						}
					}
					path = getPlatformUrl(url.name.id,this)
					break;
					case 'outCategory': //外卖商品分类
					let pid = +url.name.pid || url.name.id // 一级分类pid为0 查询id
					let id = url.name.id
					path.url = `/yb_o2ov2/home/<USER>/category-store?pid=${pid}&id=${id}`
					break;
					case 'storeList': //商店连接
					path.url = `/yb_o2ov2/home/<USER>
					break;
					case 'customPage':
						path.url = `/yb_o2ov2/order/other/custom?pid=${url.name.id}`
						break;
					case 'appjump': //小程序跳转
						url = url.name;
						path.type = 5
						break;
					default:
					noFound = true
					console.log('未找到页面路径')
					break;
				}
				if(noFound) return
				if(path.type === 1){//直接跳转
					this.go(type,path.url)
				}else if(path.type === 2){//先跳转到/yb_o2ov2/index/index在调用页面内的函数changeTab
					let pages = getCurrentPages();
					// console.log(pages[1])
					if(pages[0].__route__==='yb_o2ov2/index/index'&&pages[1]===undefined){
						pages[0].$vm.changeTab(url)
					}else{
						this.go(type,`/yb_o2ov2/index/index?changeTab=${JSON.stringify(url)}`)
					}
				}else if (path.type == 5) {
					uni.navigateToMiniProgram({
						appId: url.appId,
						path: url.path,
						complete(res) {
							// console.log(res)
						}
					})
					}
				if (process.env.NODE_ENV === 'development') {
					console.log(url, type)
				}
				
				function getPlatformUrl(id,that){
					//目前四个特殊值作为首页 index首页 myOrder帳戶订单 member个人中心 payVip付费会员卡
					let path = {
						type:1,//1非首页页面   2上述四个值
						url:''
					}
					if(id=='myAddress' || 
					id=='balanceRecharge' || 
					id=='myCoupon' || 
					id=='myHb' || 
					id=='mySc' || 
					id=='myComment' || 
					id=='integralShop'|| 
					id=='signIndex' || 
					id=='liveList' || 
					id=='oldWithNew'){
						that.checkLogin()
					}
					switch(id){
						case 'index':
							path.type = 2
						break;
						
						case 'myOrder':
							path.type = 2
						break;
						
						case 'member':
							path.type = 2
						break;
						
						case 'payVip':
							path.type = 2
						break;
						
						case 'allCat'://全部銷售類別
							path.url = '/yb_o2ov2/home/<USER>/all-category-list'
						break;
						
						case 'helpCenter'://帮助中心
							path.url = '/yb_o2ov2/my/help-and-support/index'
						break;
						
						case 'myAddress'://帳戶地址
							path.url = '/yb_o2ov2/my/address/index'
						break;
						
						case 'balanceRecharge'://充值中心
							path.url = '/yb_o2ov2/my/wallet/recharge'
						break;
						
						case 'myCoupon'://帳戶代金券
							path.url = '/yb_o2ov2/my/cash-coupon/store-coupon'
						break;
						
						case 'myHb'://帳戶红包
							path.url = '/yb_o2ov2/my/cash-coupon/platform-coupon'
						break;
						
						case 'mySc'://帳戶收藏
							path.url = '/yb_o2ov2/my/collection'
						break;
						
						case 'myComment'://帳戶评论
							path.url = '/yb_o2ov2/my/comments'
						break;
						case 'integralShop'://积分商城
							path.url = '/yb_o2ov2/my/integral/integral-mall'
						break;
						case 'signIndex'://签到中心
							path.url = '/yb_o2ov2/my/signin/index'
						break;
						case 'information'://资讯中心
							path.url = '/yb_o2ov2/my/information/information-center'
						break;
						
						// case 'merchantsMoveIn'://商户入驻
						// 	path.url = '/yb_o2ov2/my/partnership/apply-for-joining'
						// break;
						
						// case 'regionalAgent'://区域代理
						// 	path.url = '/yb_o2ov2/my/partnership/person-apply'
						// break;
						
						case 'aboutUs'://关于我们
							path.url = '/yb_o2ov2/my/other/gywmtwo'
						break;
						
						case 'contactCustomer'://联系我们
							path.url = '/yb_o2ov2/my/other/kf'
						break;
						
						case 'liveList'://直播列表
							path.url = '/yb_o2ov2/my/live/index'
							break;
							
						case 'oldWithNew'://老带新
							path.url = '/yb_o2ov2/my/old-take-new/index'
							break;
							
						case 'reserve':
							path.url = '/yb_o2ov2/shop/reserve/index'
							break;
						case 'queuing':
							path.url = '/yb_o2ov2/shop/lineup/pdqh'
							break;
						case 'collect':
							path.url = '/yb_o2ov2/order/jd'
							break;
						case 'storage':
							path.url = '/yb_o2ov2/shop/storage/index'
							break;
						
						default:
							console.log('请检查连接url')
						break;
					}
					return path
				}
			},
			requestSM(type) {
				return new Promise(async (resolve, reject) => {
					// #ifdef  MP-WEIXIN
					await this.getSmConfig()
					uni.requestSubscribeMessage({
						tmplIds: this.smConfig[type],
						complete: (res) => {
							if (res.errMsg == 'requestSubscribeMessage:ok') {
								if (JSON.stringify(res).indexOf('reject') !== -1) {
									reject()
								} else {
									resolve()
								}
							} else {
								resolve()
							}
							// console.log(res, JSON.stringify(res).indexOf('reject'))
						}
					})
					// #endif	
					// #ifndef  MP-WEIXIN
					resolve()
					// #endif	
				});
			},
			getSjgd(v) {
				return parseInt((v * 2 * this.util.getSb().rate)) //375px/750rpx=h/(拖拽式400px*2)
			},
			//增加访问记录
			addFwjl(obj) {
				this.util.request({
					'url': this.api.fwjl,
					method: 'POST',
					data: {
						storeId: obj.storeId,
						moduleName: obj.origin,
					},
					is_login: 0
				})
			},
			async qkdw() {
				getApp().globalData.gdlocation = null
				this.setAddInfo(null)
				this.setCityInfo({})
			},
			async getLocInfo(){
				await this.qkdw()
				let lc = await getDw({
					type: 'wgs84'
				})
				await this.getAddInfo(lc)
				await this.getRegionId(lc)
				// let setRegionId = uni.getStorageSync('setRegionId')
				// if(!setRegionId.id){
				// 	await this.getRegionId(lc)
				// }
			},
			getImgS(src) {
				return src ? src.indexOf('http') > -1 ? src : this.url + src : ''
			},
			async checkLogin() {
				if (!this.isLogin) {
					try {
						// await this.util.modal('请先进行登录');
						this.go('navigateTo',`/yb_o2ov2/my/login`)
					} catch (e) {
						return false
					}
				} else {
					return true
				}
			}
		}
	})
}
export default Plugin;
