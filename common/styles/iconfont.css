@font-face {
  font-family: 'iconfont';  /* Project id 2706923 */
  src: url('//at.alicdn.com/t/font_2706923_3vdw6nyb1fs.woff2?t=1642400980348') format('woff2'),
       url('//at.alicdn.com/t/font_2706923_3vdw6nyb1fs.woff?t=1642400980348') format('woff'),
       url('//at.alicdn.com/t/font_2706923_3vdw6nyb1fs.ttf?t=1642400980348') format('truetype');
}

.iconfont {
	font-family: "iconfont" !important;
	font-size: 32rpx;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	display: inline-block;
	color: #000;
	vertical-align: middle;
	line-height: 1;
	margin-top: 2rpx;
}
.iconposition:before {
	content: "\e613";
}
.iconpositionfill:before {
	content: "\e601";
}
.iconremind:before {
	content: "\e668";
} 
.iconsearch:before {
	content: "\e663";
}
.iconnotice:before {
	content: "\e7b5";
} 
.iconother:before {
	content: "\e67e";
} 
.iconnoselect:before {
	content: "\e623";
} 
.iconselect:before {
	content: "\e617";
} 
.iconphone:before {
	content: "\e664";
} 
.icontakeout:before {
	content: "\e622";
} 
.icontime:before {
	content: "\e66c";
} 
.icontaketalk:before {
	content: "\e675";
} 
.iconlike:before {
	content: "\e64a";
}
.iconback:before {
	content: "\e67a";
}
.iconinto:before {
	content: "\e67c";
}
.iconred:before {
	content: "\e66a";
}
.icongps:before {
	content: "\ec32";
}
.iconedit:before {
	content: "\e618";
}
.iconeditfill:before {
	content: "\ec7c";
}
.iconstar:before {
	content: "\e709";
}
.iconplus:before {
	content: "\e62a";
}
.iconminus:before {
	content: "\e608";
}
.icondelete:before {
	content: "\e61c";
}
.iconset:before {
	content: "\e60b";
}
.iconbeans:before {
	content: "\e603";
}
.iconmobilephone:before {
	content: "\e62f";
}
.iconwechat:before {
	content: "\e64f";
}
.iconpassword:before {
	content: "\e850";
}
.icontriangle:before {
	content: "\e65a";
}
.icontcollection:before {
	content: "\e8b9";
}
.icontcollectionfill:before {
	content: "\e600";
}
.icontcamera:before {
	content: "\e61d";
}
.icontbighorn:before {
	content: "\e611";
}
.icontchat1:before {
	content: "\e620";
}
.icontchat2:before {
	content: "\e626";
}
.icontchat3:before {
	content: "\e6cc";
}
.icontplay:before {
	content: "\ea82";
}
/* 联系商店 */
.iconcontact:before {
	content: "\e69c";
}
/* 再来一单 */
.iconagainorder:before {
	content: "\e6db";
}
/* 评价得金豆 */
.iconevaluationget:before {
	content: "\e61a";
}
/* 致电商店 */
.iconcallmerchant:before {
	content: "\e610";
}
/* 申请退款 */
.iconapplyrefund:before {
	content: "\e6a6";
}
/* 取消订单 */
.iconcancelorder:before {
	content: "\e627";
}


/* 我的地址 */
.iconmyaddress:before {
	content: "\e628";
}

/* 商店入驻 */
.iconbusinessstationed:before {
	content: "\e630";
}

/* 城市代理 */
.iconcityproxy:before {
	content: "\e605";
}

/* 帮助中心 */
.iconhelpcenter:before {
	content: "\e643";
}

/* 我的评论 */
.iconmycomments:before {
	content: "\e60a";
}

.icon-shouye:before {
  content: "\e62e";
}

.icon-shuaxin:before {
  content: "\ec08";
}

.icon-sousuo:before {
  content: "\e606";
}

.icon-xiaoxi2:before {
  content: "\e61de";
} 
.iconspzk:before {
  content: "\e87c";
}
/* 编辑 */
.iconbianji:before {
  content: "\e644";
} 
.iconwd:before {
  content: "\e619";
} 
.icondd:before {
  content: "\e645";
} 
.icongx:before {
  content: "\e604";
} 
.iconwgx:before {
  content: "\e602";
}
.icongou:before {
  content: "\ebe6";
} 




