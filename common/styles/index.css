@import './weui.css';
@import './uniui.css';
@import './iconfont.css';

page {
	height: 100%;
	font-size: 28rpx;
	color: #333;
	/* background: #EFF3F6; */
	background: #F5F5F5;
	/* overflow-x: hidden; */
}

view,
scroll-view,
swiper,
movable-view,
icon,
text,
progress,
button,
checkbox,
form,
input,
label,
picker,
picker-view,
radio,
slider,
switch,
textarea,
navigator,
audio,
image,
video,
map,
canvas,
contact-button {
	box-sizing: border-box;
}


/* 去除button自带的边框 */

::-webkit-scrollbar {
	display: none;
}

button::after {
	border: none;
}

.uni-page-head {
	z-index: 9999 !important;
}

.container {
	width: 100%;
	height: auto;
}


/*底部导航样式*/

.navbar {
	position: fixed;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 115rpx;
	background: #fff;
	color: #555;
	z-index: 2001;
	border-top: 1rpx solid rgba(0, 0, 0, 0.1);
}

.navbargator {
	height: 100%;
	width: 1%;
}

.navbar-view {
	width: 100%;
	height: 100%;
}

.navbar .navbar-icon {
	width: 36rpx;
	height: 36rpx;
}

.navbar .navbar-text {
	font-size: 22rpx;
	text-align: center;
	text-overflow: ellipsis;
	white-space: nowrap;
	overflow: hidden;
	margin-top: 8rpx;
}

/** 自定义导航栏的样式 **/
.page-navigation-bar {
	position: relative;
	display: flex;
	width: 100vw;
	height: 104rpx;
	justify-content: center;
	align-items: center;
	box-sizing: border-box;
	flex-shrink: 0;

	font-size: 28rpx;
	color: #333;
	font-weight: 400;
	background-color: #FFCC00;
}

.go-icon {
	position: absolute;
	display: flex;
	width: 100rpx;
	align-items: center;
	padding-left: 30rpx;
	box-sizing: border-box;
	left: 0;
	top: 0;
	bottom: 0;
}

/** 列表无数据 **/
.no-data {
	display: flex;
	justify-content: center;
	align-items: center;
	padding-bottom: 20rpx;

	font-weight: 400;
	font-size: 24rpx;
	color: #333333;
	line-height: 28rpx;
}

/*+-*/

.reducecon {
	height: 60rpx;
	overflow: hidden;
	transition: all 0.3s linear;
	opacity: 0;
	/* width:0; */
	transform: rotate(180deg);
	transform: translateX(50rpx);
}

.reducecon.active {
	opacity: 1;
	min-width: 100rpx;
	transform: rotate(0);
}

.cartc {
	width: 60rpx;
	height: 60rpx;
}

.cartggc {
	height: 60rpx;
}

.cartadd {
	background: #C2C2C2;
	position: relative;
	width: 40rpx;
	height: 40rpx;
	border-radius: 10rpx;
	padding: 0;
	margin: 0;
}

/* .cartadd::after {
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	content: "";
	height: 5rpx;
	width: 20rpx;
	background: #fff;
	display: block;
	border-radius: 10rpx;
}

.cartadd::before {
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	content: "";
	height: 20rpx;
	width: 5rpx;
	background: #fff;
	display: block;
	border-radius: 10rpx;
} */

.cartdec {
	background: #fff;
	position: relative;
	width: 40rpx;
	height: 40rpx;
	border-radius: 10rpx;
	padding: 0;
	border: 2rpx solid #C2C2C2;
	margin: 0;
}

.cartdecab {
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	height: 5rpx;
	width: 18rpx;
	background: #fff;
	display: block;
	border-radius: 10rpx;
}

/* .cartdec::after {
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	content: "";
	height: 5rpx;
	width: 20rpx;
	background: #F6B37F;
	display: block;
	border-radius: 10rpx;
} */


/*底部button*/

.foot-btnc {
	position: fixed;
	width: 100%;
	left: 0;
	bottom: 0;
	padding: 30rpx;
	background: transparent;
	z-index: 10;
}

.foot-btn {
	height: 92rpx;
	background: #fff;
	border-radius: 48rpx;
	font-size: 34rpx;
	color: #fff;
	line-height: 92rpx;
}

.foot-btn3 {
	height: 80rpx;
	border-radius: 15rpx;
	font-size: 32rpx;
	color: #fff;
	line-height: 80rpx;
}

/*固定底部区域*/
.bgf {
	background: #fff;
	height: 40rpx;
	width: 100%;
	position: fixed;
	left: 0;
	bottom: 0;
	z-index: 2000;
}

/*授权按钮*/
.sqbtn {
	border: none;
	padding: 0;
	margin: 0;
	display: flex;
	justify-content: center;
	align-items: center;
	background: transparent;
	border-radius: 0;
	font-size: inherit;
	color: inherit;
}

.sqbtn::after {
	border: none;
}

.sharebtn {
	position: absolute;
	width: 100%;
	height: 100%;
	left: 0;
	top: 0;
	margin: 0;
	padding: 0;
	line-height: inherit;
	border: none;
	background: none;
	color: inherit;
	display: inline-block;
}

.sharebtn:after {
	display: none;
}

/*使用按钮去掉原生样式*/
.clearbtn {
	line-height: normal;
	font-size: inherit;
	padding: 0;
	margin: 0;
}

.tcbtn {
	border-radius: 0px;
}

.tcbtn:first-child::after {
	display: none;
}

.tcbtn::after {
	content: " ";
	position: absolute;
	left: 0;
	top: 0;
	width: 1px;
	bottom: 0;
	border-left: 1px solid #ebedf0;
	transform-origin: 0 0;
	transform: scaleX(.5);
}

/*固定顶部*/

.fixedtop {
	position: fixed;
	top: 0;
	width: 100%;
	z-index: 9;
}

.xqyfixedtop {
	position: fixed;
	/* top: var(--window-top); */
	top: 0;
	width: 100%;
	z-index: 9;
}

.tabht {
	height: 96rpx;
}

/* 相对定位以及绝对定位 */
.dis-in {
	display: inline-block;
}

.dis-b {
	display: block;
}

.dis-n {
	display: none;
}

.ws-n {
	white-space: nowrap
}

.posi-a {
	position: absolute;
	z-index: 1;
}

.p-a-r0 {
	position: absolute;
	z-index: 1;
	right: 0;
}

.posi-r {
	position: relative;
	z-index: 1;
}

.posi-s {
	position: sticky;
	z-index: 1;
}

.p-a {
	position: absolute;
}

.p-r {
	position: relative;
}

.p-f {
	position: fixed;
}

.posi-f {
	position: fixed;
	z-index: 1;
}

.p-a-xc {
	left: 50%;
	transform: translateX(-50%);
}

.p-a-yc {
	top: 50%;
	transform: translateY(-50%);
}

.p-a-c {
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}

.p-c {
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
}

.bgfix {
	width: 100%;
	height: 100%;
	position: fixed !important;
	overflow: hidden;
}

.ft0 {
	top: 0;
}

.t0 {
	top: 0;
}

.l0 {
	left: 0;
}

.b0 {
	bottom: 0;
}

.r0 {
	right: 0;
}

.z9999 {
	z-index: 9999;
}

.z2001 {
	z-index: 2001;
}

.udlr {
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	margin: auto;
}

.hidecanvas {
	position: fixed;
	bottom: -100%;
	left: -100%;
	z-index: -99;
	visibility: hidden;
}

/*flex布局*/

.flex {
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
}

.f-row {
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-flex-direction: row;
	flex-direction: row;
}

.f-col {
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	-webkit-box-orient: vertical;
	-webkit-flex-direction: column;
	flex-direction: column;
}


/*占比*/

.f-g-0 {
	min-width: 0;
	flex-grow: 0;
	flex-shrink: 0;
}

.f-g-1 {
	min-width: 0;
	flex-grow: 1;
	flex-shrink: 1;
}

.f-1 {
	flex: 1;
	min-width: 0;
}

.f-s-1 {
	flex-shrink: 1;
}

.f-s-0 {
	flex-shrink: 0;
}

.f-b-25 {
	flex-basis: 25%;
}

/*居中*/

.f-c {
	display: flex;
	flex-direction: row;
	justify-content: center;
	align-items: center;
}

.f-c-c {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
}

.f-x-c {
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	-webkit-box-pack: center;
	-webkit-justify-content: center;
	-ms-flex-pack: center;
	justify-content: center;
}

.f-y-c {
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	-webkit-box-align: center;
	-webkit-align-items: center;
	-ms-flex-align: center;
	-ms-grid-row-align: center;
	align-items: center;
}

.f-c-xc {
	display: flex;
	flex-direction: column;
	justify-content: center;
}

.f-c-ac {
	display: flex;
	flex-direction: column;
	align-items: center;
}


/*wrap*/

.f-w {
	flex-wrap: wrap;
}

.f-raw {
	display: flex;
	flex-direction: row;
	align-items: center;
	flex-wrap: wrap;
}



/*end*/

.f-c-e {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
}

.f-e-c {
	display: flex;
	justify-content: flex-end;
	flex-direction: column;
}

.f-s {
	display: flex;
	align-items: flex-start;
}

.f-s-ac {
	display: flex;
	align-items: flex-start;
	align-items: center;
}

.f-c-s {
	display: flex;
	flex-direction: column;
	align-items: flex-start;
}

.f-y-e {
	display: flex;
	align-items: flex-end;
}

.f-x-e {
	display: flex;
	justify-content: flex-end;
}

.f-sh {
	display: flex;
	align-items: stretch;
}

/*space-betwee*/

.f-bt {
	display: flex;
	justify-content: space-between;
}

.f-x-bt {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.f-e-bt {
	display: flex;
	justify-content: space-between;
	align-items: flex-end;
}

.f-y-bt {
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.f-x-ad {
	display: flex;
	justify-content: space-around;
}

.f-x-c-sa {
	display: flex;
	align-items: center;
	justify-content: space-around;
}

.f-y-ad {
	display: flex;
	flex-direction: column;
	justify-content: space-around;
}

/**/
.f-a-b {
	align-items: baseline;
}

/*块状文字*/

.text-btnf {
	font-size: 26rpx;
	text-align: center;
	padding: 7rpx 18rpx;
	border-radius: 10rpx;
	color: #fff;
	border: 1rpx solid transparent;
}

.text-btn {
	font-size: 26rpx;
	text-align: center;
	padding: 7rpx 18rpx;
	border-radius: 10rpx;
	color: #333;
	border: 1rpx solid #eee;
}

.obtn {
	min-width: 165rpx;
	padding: 0 20rpx;
	height: 60rpx;
	font-size: 26rpx;
	border: 0.6Px solid #ddd;
	border-radius: 60rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	margin-left: 25rpx;
}

.rbtn {
	padding: 15rpx 35rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	margin: 0;
	border-radius: 35rpx;
	font-size: 30rpx;
	line-height: normal;
}

/*  宽度样式  */

.w100 {
	width: 100%;
}

.w50 {
	width: 50%;
}

.w33 {
	width: 33.33%;
}

.w70 {
	width: 70%;
}

.w80 {
	width: 80%;
}

.h100 {
	height: 100%;
}

.mh100 {
	min-height: 100%;
}

.mvh100 {
	min-height: 100vh;
}

.h0 {
	height: 0;
}

.wh {
	width: 100%;
	height: 100%;
}

.w100v {
	width: 100vw;
}

.h100v {
	height: 100vh;
}

.w500 {
	width: 500rpx;
}

.wh60 {
	width: 60rpx;
	height: 60rpx;
}

/* 文字排列方式 */

.t-l {
	text-align: left;
}

.t-r {
	text-align: right;
}

.t-c {
	text-align: center;
}

.t-d-l {
	text-decoration: line-through;
}

.l-n {
	line-height: normal;
}

.l-h1 {
	line-height: 1.0;
}

.wbba {
	word-break: break-all
}

/* 字体大小 */

.f14 {
	font-size: 14rpx;
}

.f16 {
	font-size: 16rpx;
}

.f18 {
	font-size: 18rpx;
}

.f20 {
	font-size: 20rpx;
}

.f22 {
	font-size: 22rpx;
}

.f24 {
	font-size: 24rpx;
}

.f26 {
	font-size: 26rpx;
}

.f28 {
	font-size: 28rpx;
}

.f30 {
	font-size: 30rpx;
}

.f32 {
	font-size: 32rpx;
}

.f34 {
	font-size: 34rpx;
}

.f36 {
	font-size: 36rpx;
}

.f38 {
	font-size: 38rpx;
}

.f40 {
	font-size: 40rpx;
}

.f42 {
	font-size: 42rpx;
}

.f44 {
	font-size: 44rpx;
}

.f46 {
	font-size: 46rpx;
}

.f48 {
	font-size: 48rpx;
}

.f50 {
	font-size: 50rpx;
}

.f56 {
	font-size: 56rpx;
}

.f60 {
	font-size: 60rpx;
}

.f70 {
	font-size: 70rpx;
}


/* 字体是否加粗 */

.wei {
	font-weight: bold;
}

.nowei {
	font-weight: normal;
}

.wei4 {
	font-weight: 400;
}

.wei6 {
	font-weight: 600;
}


/* 细线边框 */

.b-t-e,
.b-b-e {
	position: relative;
}

.b-t-e:before {
	content: '';
	position: absolute;
	border-top: 1px solid #eee;
	-webkit-transform: scaleY(0.5);
	transform: scaleY(0.5);
	top: 0;
	left: 0;
	right: 0;
}

.b-b-e:after {
	content: '';
	position: absolute;
	border-bottom: 1px solid #eee;
	-webkit-transform: scaleY(0.5);
	transform: scaleY(0.5);
	bottom: 0;
	left: 0;
	right: 0;
}

.b-r-f {
	border-right: 1rpx solid #fff;
}

/* 右边一半边框 */
.b-r-d {
	position: relative;
}

.b-r-d::after {
	content: '';
	position: absolute;
	width: 1px;
	background: #C0C0C0;
	height: 100%;
	-webkit-transform: scale(0.5, 0.5);
	transform: scale(0.5, 0.5);
	top: 0%;
	right: 0;
}

.b-r-d:last-child::after {
	display: none;
}

/* 右边边框 */
.b-re {
	border-right: 1px solid #eee;
}

.b-re:last-child {
	border: none;
}

.b-be {
	border-bottom: 1px solid #eee;
}

.b-be:last-child {
	border: none;
}

/* 全边框 */
.b-d {
	border: 0.5px solid #ddd;
}

.b-f5 {
	border: 1px solid #F5F5F5;
}

.br-h {
	position: relative;
}

.br-h::after {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	width: 200%;
	height: 200%;
	border: 1px solid #ddd;
	-webkit-transform-origin: 0 0;
	transform-origin: 0 0;
	-webkit-transform: scale(.5, .5);
	transform: scale(.5, .5);
}

.b-n {
	border: none;
}

/*border-radius*/

.bs20 {
	border-radius: 20rpx;
	overflow: hidden;
}

.bs0 {
	border-radius: 0;
}

.bs5 {
	border-radius: 5rpx;
	overflow: hidden;
}

.bs10 {
	border-radius: 10rpx;
	overflow: hidden;
}

.bs15 {
	border-radius: 15rpx;
	overflow: hidden;
}

.bs30 {
	border-radius: 30rpx;
	overflow: hidden;
}

.bs30-nh {
	border-radius: 30rpx;
}

.bs60 {
	border-radius: 60rpx;
	overflow: hidden;
}

.bs2000 {
	border-radius: 20rpx 20rpx 0 0;
	overflow: hidden;
}

.bs3000 {
	border-radius: 30rpx 30rpx 0 0;
	overflow: hidden;
}

.bs0030 {
	border-radius: 0 0 30rpx 30rpx;
	overflow: hidden;
}

.bstl30 {
	border-top-left-radius: 30rpx;
}

.bsbr30 {
	border-bottom-right-radius: 30rpx;
}

.bsf {
	border-radius: 50%;
	overflow: hidden;
}

.o-h {
	overflow: hidden;
}

.o-x-s {
	overflow-x: scroll;
}

.o-x-s::-webkit-scrollbar {
	display: none;
}

.o-y-s {
	overflow-y: scroll;
	-webkit-overflow-scrolling: touch;
}

.o-a {
	overflow: auto;
}

.o-v {
	overflow: visible;
}

/* 文字颜色 */

.c9 {
	color: #999;
}

.grayColor {
	color: #88888a;
}

,


.c7 {
	color: #777;
}

.c6 {
	color: #666;
}

.c3 {
	color: #333;
}

.c0 {
	color: #000;
}

.cf {
	color: #fff;
}

.cd {
	color: #ddd;
}

.ce {
	color: #eee;
}

.cef {
	color: #efefef;
}

.cb {
	color: #bbb;
}

.crb {
	color: #FA463D;
}

.cf7 {
	color: #fead77;
}

.cfa {
	color: #ff4d3a;
}

.cf5f {
	color: #FF5F2F;
}

.cf5f2 {
	color: #ff474a;
}

.cf5 {
	color: #ff0025;
}

.ce5 {
	color: #e5e5e5;
}

.cf70 {
	color: #FF7F00;
}

.c8 {
	color: #888;
}

.cb2 {
	color: #b2b2b2;
}

/*基础padding*/
.p13 {
	padding: 10rpx 30rpx;
}

.p23 {
	padding: 20rpx 30rpx;
}

.p230 {
	padding: 20rpx 30rpx 0;
}

.p253 {
	padding: 25rpx 30rpx;
}

.p025 {
	padding: 0 25rpx;
}

.p3 {
	padding: 30rpx;
}

.p43 {
	padding: 40rpx 30rpx;
}

.p330 {
	padding: 30rpx 30rpx 0rpx;
}

.p332 {
	padding: 30rpx 30rpx 20rpx;
}

.p032 {
	padding: 0 30rpx 20rpx;
}

.p033 {
	padding: 0 30rpx 30rpx;
}

.p2 {
	padding: 20rpx;
}

.p1 {
	padding: 10rpx;
}

.p10 {
	padding: 10rpx 0;
}

.p15 {
	padding: 15rpx 0;
}

.p20 {
	padding: 20rpx 0;
}

.p30 {
	padding: 30rpx 0;
}

.p01 {
	padding: 0 10rpx;
}

.p02 {
	padding: 0 20rpx;
}

.p03 {
	padding: 0 30rpx;
}

.p04 {
	padding: 0 40rpx;
}

.p05 {
	padding: 0 50rpx;
}

.p32 {
	padding: 30rpx 20rpx;
}

.pb20 {
	padding-bottom: 20rpx;
}

.pb30 {
	padding-bottom: 30rpx;
}

.pb40 {
	padding-bottom: 40rpx;
}

.pb10 {
	padding-bottom: 10rpx;
}

.pb0 {
	padding-bottom: 0rpx;
}

.pb115 {
	padding-bottom: 115rpx;
}

.pb130 {
	padding-bottom: 190rpx;
}

.pt10 {
	padding-top: 10rpx;
}

.pt20 {
	padding-top: 20rpx;
}

.pt30 {
	padding-top: 30rpx;
}

.pt40 {
	padding-top: 40rpx;
}

.pt60 {
	padding-top: 60rpx;
}

.pt80 {
	padding-top: 80rpx;
}

.pt90 {
	padding-top: 90rpx;
}

.pt105 {
	padding-top: 105rpx;
}

.pr0 {
	padding-right: 0;
}

.pr40 {
	padding-right: 40rpx;
}

.pl10 {
	padding-left: 10rpx;
}

.pl20 {
	padding-left: 20rpx;
}

.pl40 {
	padding-left: 40rpx;
}

.p-3-10 {
	padding: 3rpx 10rpx;
}

.p-5-10 {
	padding: 5rpx 10rpx;
}

.p-5-20 {
	padding: 5rpx 20rpx;
}

.p-5-15 {
	padding: 5rpx 15rpx;
}

.p-10-15 {
	padding: 10rpx 15rpx;
}

.p-8-20 {
	padding: 8rpx 20rpx;
}

.p-10-20 {
	padding: 10rpx 20rpx;
}

.p-10-30 {
	padding: 10rpx 30rpx;
}

.p-13-20 {
	padding: 13rpx 20rpx;
}

.p-15-20 {
	padding: 15rpx 20rpx;
}

.p-15-30 {
	padding: 15rpx 30rpx;
}

.p-18-90 {
	padding: 18rpx 90rpx;
}

.p-15-30-0 {
	padding: 15rpx 30rpx 0;
}

.p-30-30-0 {
	padding: 30rpx 30rpx 0;
}

.p3133 {
	padding: 30rpx 10rpx 30rpx 30rpx;
}

.p21 {
	padding: 20rpx 10rpx;
}

.p0 {
	padding: 0;
}


/* magin边距 */
.mla {
	margin: 0 auto;
}

.m2 {
	margin: 20rpx;
}

.m1 {
	margin: 10rpx;
}

.m3 {
	margin: 30rpx;
}

.m23 {
	margin: 20rpx 30rpx;
}

.m1110 {
	margin: 10rpx 10rpx 10rpx 0;
}

.mt15 {
	margin-top: 15rpx;
}

.mt20 {
	margin-top: 20rpx;
}

.mt30 {
	margin-top: 30rpx;
}

.mt40 {
	margin-top: 40rpx;
}

.mt60 {
	margin-top: 60rpx;
}

.mt80 {
	margin-top: 80rpx;
}

.mt90 {
	margin-top: 90rpx;
}

.mb15 {
	margin-bottom: 15rpx;
}

.mb20 {
	margin-bottom: 20rpx;
}

.mb30 {
	margin-bottom: 30rpx;
}

.mb40 {
	margin-bottom: 40rpx;
}

.mb50 {
	margin-bottom: 50rpx;
}

.mb60 {
	margin-bottom: 60rpx;
}

.ml20 {
	margin-left: 20rpx;
}

.ml30 {
	margin-left: 30rpx;
}

.ml40 {
	margin-left: 40rpx;
}

.ml50 {
	margin-left: 50rpx;
}

.ml0 {
	margin-left: 0rpx;
}

.ml5 {
	margin-left: 5rpx;
}

.ml10 {
	margin-left: 10rpx;
}

.ml15 {
	margin-left: 15rpx;
}

.mr5 {
	margin-right: 5rpx;
}

.mr15 {
	margin-right: 15rpx;
}

.mr20 {
	margin-right: 20rpx;
}

.mr30 {
	margin-right: 30rpx;
}

.mr50 {
	margin-right: 50rpx;
}

.mr60 {
	margin-right: 60rpx;
}

.mr10 {
	margin-right: 10rpx;
}

.mt5 {
	margin-top: 5rpx;
}

.mt8 {
	margin-top: 8rpx;
}

.mt10 {
	margin-top: 10rpx;
}

.mb10 {
	margin-bottom: 10rpx;
}

.m10 {
	margin: 10rpx 0;
}

.m20 {
	margin: 20rpx 0;
}

.m30 {
	margin: 30rpx 0;
}

.m2302 {
	margin: 20rpx 30rpx 0 20rpx;
}

.m01 {
	margin: 0 10rpx;
}

.m02 {
	margin: 0 20rpx;
}

.m03 {
	margin: 0 30rpx;
}

.ma {
	margin: auto;
}

.mt0 {
	margin-top: 0;
}

.ma0 {
	margin: auto 0;
}

/* 背景为白色 */

.bf {
	background: #fff;
}

.bfa {
	background: #fafafa;
}

.bf5 {
	background: #f5f5f5;
}

.bf6 {
	background: #f6f6f6;
}

.bf7 {
	background: #f7f7f7;
}

.bec {
	background-color: #ECECEC;
}

.bfc {
	background: #FAFBFC;
}

.be {
	background: #eee;
}

.bf6 {
	background: #F6F6F6;
}

.bf9 {
	background: #f9f9f9;
}

.bd6 {
	background: #D6D6D6;
}

.bdd {
	background: #DDDDDD;
}

.b3 {
	background: #333;
}

.bg0 {
	background: #000;
}

.b05 {
	background-color: rgba(0, 0, 0, 0.5);
}

.bt {
	background: transparent;
}

.br {
	background: #f00;
}

.bf5f {
	background: #FF5F2F;
}

.bf5f2 {
	background: #ff474a;
}

.bf25 {
	background: #ff0025;
}

.bb {
	background: #2d95ff;
}

.bg {
	background: #15c42d;
}

.by {
	background: #ffb71c;
}

.be6 {
	background: #EFF3F6;
}

.bf2f {
	background: #f2f2f2;
}

.b00 {
	background: #07C160;
}

.bef {
	background: #EF371F;
}

.bf0a {
	background: #FF5B0A;
}

.b-l-f4ee {
	background: linear-gradient(90deg, #f83144 0%, #ed4e6e 300%);
}

.b-l-f0f0 {
	background: linear-gradient(to right, #ff8200, #fd5b00);
}

.b-l-fdf3 {
	background: linear-gradient(to right, #f8c10d, #ff9503);
}

/*减*/

/*取消*/
.b-l-qx {
	background: linear-gradient(to right, #ddd, #ccc);
}

/*阴影*/

.b-s {
	box-shadow: 0rpx 4rpx 20rpx 0rpx rgba(221, 221, 221, 0.8);
}

.b-s-3 {
	box-shadow: 0rpx 0rpx 8rpx 5rpx #eee;
}

.b-s-4 {
	box-shadow: 0 0 13rpx rgba(0, 0, 0, 0.12);
}

.b-s-F9B2B5 {
	box-shadow: 0rpx 20rpx 18rpx -10rpx #f9b2b5;
}

.b-s-FBCB05 {
	box-shadow: 0rpx 20rpx 18rpx -15rpx #fbcb05;
}

.b-s-1 {
	box-shadow: 0rpx 3rpx 8rpx 3rpx rgba(221, 221, 221, 0.6);
}

.b-s-2 {
	box-shadow: 0rpx 8rpx 28rpx 5rpx rgba(221, 221, 221, 0.8);
}


/* 多行超出显示省略号 */

.t-o-e {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.t-o-e2 {
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
}

.t-o-e3 {
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 3;
}

.t-o-e4 {
	overflow: hidden;
	text-overflow: ellipsis;
	word-break: break-all;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 4;
}


/*旋转角度*/

.r90 {
	transform: rotateZ(90deg);
}

.xzdw {
	transform: rotateZ(180deg);
	transition: 0.3s;
}

/*伪类相关*/

.cell {
	position: relative;
}

.cell:after {
	content: '';
	position: absolute;
	border-bottom: 1px solid #eee;
	-webkit-transform: scaleY(0.5);
	transform: scaleY(0.5);
	bottom: 0;
	right: 0;
	left: 30rpx;
}

.cell:last-child:after {
	display: none;
}

.la154::after {
	left: 154rpx;
}

.la124::after {
	left: 156rpx;
}

/*vant 1px边框*/
[class*='hl']::after {
	position: absolute;
	box-sizing: border-box;
	content: ' ';
	pointer-events: none;
	top: -50%;
	right: -50%;
	bottom: -50%;
	left: -50%;
	border: 0 solid #ebedf0;
	-webkit-transform: scale(0.5);
	transform: scale(0.5);
}

.hlt,
.hlr,
.hlb,
.hll {
	position: relative;
}

.hlt::after {
	border-top-width: 1px;
}

.hlr::after {
	border-right-width: 1px;
}

.hlb::after {
	border-bottom-width: 1px;
}

.hll::after {
	border-left-width: 1px;
}

/*满减*/
.yhic {
	width: 36rpx;
	height: 36rpx;
	border-radius: 8rpx;
	margin-right: 13rpx;
}

.itemcon {
	width: 75rpx;
	height: 32rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 20rpx;
	border-radius: 5rpx;
	border: 1px solid #fff;
}

.popsf {
	border-color: #D0D7E1;
	background: #F0F3F6;
	color: #8395B1;
}

.pomj {
	border-color: #F5D8D6;
	background: #FFF4F2;
	color: #CE7B77;
}

.poxk {
	border-color: #D6E5CB;
	background: #EBFAE7;
	color: #8E9E7E;
}

.pozk {
	border-color: #F4E3C2;
	background: #F4E3C2;
	color: #333;
}

.hongbao {
	padding: 3rpx 10rpx;
	color: #fff;
	background-image: linear-gradient(106deg, #ff7a01, #ff3c00);
}

.btnhc {
	opacity: 0.7;
}

/*会员价*/
.vipl {
	/* 	color: #F4E3C2;
	font-size: 20rpx;
	padding: 3rpx 10rpx;
	border-radius: 5rpx 0 0 5rpx;
	background: linear-gradient(to right, #2A2A2F, #2A2A2F); */
	color: #333;
	font-size: 20rpx;
	padding: 3rpx 10rpx;
	border-radius: 0 5rpx 5rpx 0;
	background: #F4E3C2;
}

.vipimg {
	width: 24rpx;
	height: 24rpx;
}

.vipr {
	/* color: #333;
	font-size: 20rpx;
	padding: 3rpx 10rpx;
	background: #F4E3C2;
	border-radius: 0 5rpx 5rpx 0; */
	color: #F4E3C2;
	font-size: 20rpx;
	padding: 3rpx 10rpx;
	background: linear-gradient(to right, #2A2A2F, #2A2A2F);
	border-radius: 5rpx 0 0 5rpx;
}

/*回到顶部*/
.hddb {
	position: fixed;
	right: 20rpx;
	bottom: 145rpx;
	width: 80rpx;
	height: 80rpx;
	text-align: center;
	line-height: 80rpx;
	background: black;
	opacity: 0.6;
	color: #fff;
	border-radius: 50%;
	font-size: 27rpx;
	padding: 0;
}

.rowxx {
	transition: 0.3s;
}

.rowshow {
	transform: rotate(-180deg);
}

.bgzz {
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
	background-color: #000;
	opacity: .5;
}

.bggray {
	-webkit-filter: grayscale(100%);
	filter: grayscale(100%);
}

.op0 {
	opacity: 0;
}

/* #ifdef MP-ALIPAY */
label {
	display: block;
}

/* #endif */
.imgha {
	max-width: 100%;
	display: block;
}

.imghac {
	/* max-width: 100%;
	display: block; */
	font-size: 0;
}

/**/
.animated.bounceIn {
	animation-duration: .75s;
}

@keyframes bounceIn {

	from,
	20%,
	40%,
	60%,
	80%,
	to {
		animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
	}

	0% {
		opacity: 0;
		transform: scale3d(.3, .3, .3);
	}

	20% {
		transform: scale3d(1.1, 1.1, 1.1);
	}

	40% {
		transform: scale3d(.9, .9, .9);
	}

	60% {
		opacity: 1;
		transform: scale3d(1.03, 1.03, 1.03);
	}

	80% {
		transform: scale3d(.97, .97, .97);
	}

	to {
		opacity: 1;
		transform: scale3d(1, 1, 1);
	}
}

.bounceIn {
	animation-name: bounceIn;
}

.sphdir {
	padding-left: 10rpx;
	border-left: 0.5px solid #eee;
}

.sptjc {
	padding: 8rpx 10rpx;
	line-height: 20rpx;
}

.iconspzk {
	margin-right: 6rpx;
}