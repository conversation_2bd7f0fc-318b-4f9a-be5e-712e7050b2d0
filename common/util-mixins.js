import {
	mapState,
	mapActions,
} from "vuex";
import utils from '@/common/utils.js'
export const pagingLoad = {
	data(){
		return {
		
			request:''
		}
	},
	methods:{
		// refresh(){
		// 	this.list = []
		// 	this.params.page = 1
		// 	this.fetchData()
		// },
		// async fetchData(type,method='GET'){
		// 	console.log(this.requestUrl)
		// 	this.status='loading'
		// 	await this.util.request({
		// 			'url': this.requestUrl,
		// 			method: method||'GET',
		// 			data: this.params
		// 		})
		// 	this.status='loadmore'
		// },
		// nextPage(){
		// 	if(this.status === 'loading'){return}
		// 	this.params.page++
		// 	this.fetchData('nextPage')
		// }
	}
};
// 触底刷新
export const sljz = {
	data() {
		return {
			dataList: [],
			bfList: [],
			isget: false,
			mygd: false,
		}
	},
	onReachBottom: utils.debounce(function(e) {
		this.loadStatus = 'loading';
		// 模拟数据加载
		setTimeout(() => {
			//this.addRandomData();
			this.loadStatus = 'loadmore';
		}, 1000)
	}, 300),
}
export const utilMixins = {
	computed: {
		...mapState({}),
	},
	methods: {
		timeToDate(num, fmt) {
			return utils.timeToDate(num, fmt)
		},
		dateToTime(date) {
			return utils.dateToTime(date)
		},
		getSingleImg(url) {
			return url.indexOf('http') > -1 ? url : this.url + url
		},
		snText(t, n) {
			return t && t.length > n ? t.substring(0, n) + '...' : t
		},
		blxs(v, n = 2) {
			return Number(Number(v).toFixed(n))
		},
		payName(type) {
			let n = ''
			switch (+type) {
				case 1:
					n = "微信付款";
					break;
				case 2:
					n = "付款宝付款";
					break;
				case 3:
					n = "百度付款";
					break;
				case 5:
					n = "余额付款";
					break;
			}
			return n
		},
		cTR(c) {
			return utils.colorToRGB(c)
		},
		cTRld(c, l) {
			return utils.ldColor(c, l)
		},
		cTRjjld(c, l) {
			return utils.jjldColor(c, l)
		},
	}
};
