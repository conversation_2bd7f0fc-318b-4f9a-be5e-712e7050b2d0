let platform = ''
// #ifdef  MP-WEIXIN
platform = 'mini'
// #endif
// #ifdef  MP-ALIPAY
platform = 'ali'
// #endif
// #ifdef  MP-BAIDU
platform = 'baidu'
// #endif
// #ifdef  MP-TOUTIAO
platform = 'toutiao'
// #endif
// #ifdef  H5
platform = 'h5'
// 判断是否为公众号模拟器环境
const isWechat = () => {
	return String(navigator.userAgent.toLowerCase().match(/MicroMessenger/i)) == "micromessenger";
}
platform = isWechat() ? 'weChat' : 'h5'
// #endif
let t = 'channelApi/',
	api = {
		storeList: t + 'shop/store-list',
		shopConfig: t + 'shop/pay-config',
		supplierConfig: t + 'supplier/pay-config',
		platform,
		myCollection: t + 'shop/collection-store-list', //收藏商户
		balanceDetails: t + 'member/balance-details', //余额明细
		integralDetails: t + 'member/integral-details', //积分明细
		recommendGood: t + 'shop/recommend-good', //推荐商品
		storeRecommend: t + 'shop/recommend-list', // 推荐商店
		CoordinateToAdd: t + 'config/coordinate-to-add', // 坐标转地址
		getNearby: t + 'address/index', // 附近地址
		getSwiper: t + 'carousel/list', //首页轮播图
		CategoryListAll: t + 'goods-cat/list', // 商品全部分类
		CategoryListSec: t + 'goods-cat/get-cat', // 商品二级分类
		orderEvaluate: t + 'order/evaluate', // 订单评价
		evaluateList: t + 'order/evaluate-list', //商店评价列表
		storeEvaluate: t + 'order/store-evaluate-list',
		myEvaluateList: t + 'order/my-evaluate-list', //用户评价列表
		delEvaluate: t + 'order/del-evaluate', //用户自删评价
		rcommendGood: t + 'shop/recommend-good', //发现好菜
		getStoreSwiper: t + 'goods-cat/get-store', //大牌甄选shop/lenovo?keyword=茶
		lenovo: t + 'shop/lenovo', //关键词联想
		shopCity: t + 'shop/shop-city', //城市选择
		saveGoodsZan: t + 'member/save-goods-zan', //商品点赞
		regionSave: t + 'region/region-save', //城市代理人申请
		orderMuster: t + 'order/order-muster', //是否参与新客立减
		supplierorderMuster: t + 'order-supplier/order-muster', //是否参与新客立减
		getGoods: t + 'good/get-goods', //获取商户商品 分页
		config: t + 'config/config',
		login: t + 'login/login', //获取code
		chatRecord: t + 'member/chat-record', //聊天记录
		getChatList: t + 'member/get-chat-list', //消息列表
		getEmojiList: 'common/common/get-emoji-list', //emoji
		jm: t + 'login/decrypt',
		xgyh: t + 'login/save-user',
		pay: t + 'pay/pay',
		supplierpay: t + 'pay-supplier/pay',
		shopGoodsInfo: t + 'good/get-product-list', //获取商户商品信息
		getStoreInfo: t + 'good/get-store', //获取商户信息
		qtsjjh: t + 'shop/window-collection',
		WxSign: t + 'WxSign',
		WxUpload: t + 'WxUpload',
		layout: t + 'config/get-drag-row', //拖拽数据
		saveShopCar: t + 'order/save-shop-car',
		wdgwc: t + 'order/get-car-list',
		getCartList: t + 'order/get-car-list', //获取购物车商品
		clearCart: t + 'order/del-car',
		goodsDetail: t + 'good/get-good-detail',
		saveUserAddress: t + 'member/save-user-address',
		getMyAddress: t + 'member/my-address',
		getsupplierMyAddress: t + 'member/my-supplier-address',
		regionList: t + 'region/region',
		suppliercategoryList: t + 'good-supplier/get-category',
		scshdz: t + 'member/del-user-address',
		receivingAddress: t + 'member/receiving-address',
		supplierAddress: t + 'member/receiving-supplier-address',
		orderGetDelivery: t + 'order/get-delivery',
		storeTime: t + 'order/store-time',
		supplierTime: t + 'order-supplier/store-time',
		reserveTime: t + 'reserve/appointment-options',
		submitOrder: t + 'order/save-order',
		orderLis: t + 'order/order-list',
		ddlb: t + 'order/order-list',
		supplierOrder: t + 'order-supplier/order-list',
		orderSet: t + 'order/order-set',
		wmddqx: t + 'order/cancel-order',
		supplierCancel: t + 'order-supplier/cancel-order',
		supplierrefund: t + 'order-supplier/refund',
		wmddcd: t + 'order/reminder',
		supplierWmddcd: t + 'order-supplier/reminder',
		wmddtk: t + 'order/refund',
		wmddsh: t + 'order/receiving',
		supplierWmddsh: t + 'order-supplier/receiving',
		orderInfo: t + 'order/order-info',
		supplierOrderInfo: t + 'order-supplier/order-info',
		wmmbxx: t + 'config/template-list',
		supplierDelOrder: t + 'order-supplier/del-order',
		zjdp: t + 'shop/lately-store',
		zbtdz: t + 'config/coordinate-to-add',
		bzzx: t + 'member/get-helps-list',
		bzxq: t + 'member/get-helps-detail',
		getGoodsSpecs: t + 'good/get-good-oper',
		getSupplierGoodsSpecs: t + 'good-supplier/get-good-oper',
		ptgg: t + 'config/get-article-list',
		fwjl: t + 'cloud/visit-collect',
		wmxdbd: t + 'order/order-muster',
		xcxm: t + 'config/get-code',
		tzssp: t + 'good/good-rank-type',
		zblb: t + 'config/get-mini-live',
		lqzx: t + 'coupon/coupon-list',
		lqyhq: t + 'coupon/receive-coupon',
		yhqxq: t + 'coupon/coupon-info',
		wdyhq: t + 'coupon/my-coupon',
		kyyhq: t + 'coupon/available-coupon',
		czgz: t + 'member/recharge-rule',
		czxd: t + 'member/recharge-order',
		qd: t + 'sign-in/sign',
		bzqd: t + 'sign-in/week-sign-record',
		wdqdsj: t + 'sign-in/my-sign',
		wdqdjl: t + 'sign-in/my-sign-record',
		jfsplb: t + 'integral-shop/goods-list',
		jffl: t + 'integral-shop/goods-type-list',
		jfspxq: t + 'integral-shop/goods-info',
		wdshdz: t + 'member/my-address',
		invoice: t + 'member/invoice',
		carrier: t + 'member/carrier',
		orderEvaluateList: t + 'order/evaluate-list',
		orderEvaluate: t + 'order/evaluate',
		orderEvaluateDel: t + 'order/del-evaluate',
		supplierOrderEvaluate: t + 'order-supplier/supplier-evaluate',
		supplierOrderEvaluateDel: t + 'order-supplier/del-evaluate',
		supplierList: t + 'supplier/supplier-list',
		jfscztlb: t + 'integral-shop/self-list',
		jfspxd: t + 'integral-shop/save-integral-order',
		jfdd: t + 'integral-shop/my-integral-order',
		jfddxq: t + 'integral-shop/integral-order-info',
		jfqrsh: t + 'integral-shop/modify-order',
		jfmx: t + 'member/integral-details',
		xkzx: t + 'member/new',
		zfyl: t + 'order/pay-politely',
		scyl: t + 'member/collection',
		sctp: t + 'config/upload',
		zxlb: t + 'config/information',
		zxxq: t + 'config/information-info',
		lqtcyhq: t + 'shop/receive-window-coupon',
		cssj: t + 'shop/get-city',
		dwcs: t + 'shop/coordinate-to-add',
		scjk: t + 'member/save-collection',
		zanStore: t + 'member/save-store-zan',
		dplbsc: t + 'shop/collection-store-list',
		dplbss: t + 'shop/search-store',
		sytchj: t + 'member/window',
		lqfqb: t + 'member/receive-issue-coupons',
		qbxq: t + 'roll-bag/roll-bag-info',
		qbxd: t + 'roll-bag/roll-bag-order',
		qbgmjl: t + 'roll-bag/order-list',
		dhm: t + 'member/exchange-code',
		spss: t + 'good/select-good',
		syxd: t + 'cashier/save-cashier',
		kcxd: t + 'in-store/save-fast-order',
		kclb: t + 'in-store/my-fast-order',
		kcddcz: t + 'in-store/refund-fast',
		kcddxq: t + 'in-store/fast-order-info',
		dmlb: t + 'cashier/my-cashier',
		dmxq: t + 'cashier/cashier-order-info',
		zqqcm: t + 'order/meal-code',
		smjhm: t + 'in-store/get-code-info',
		tsczxq: t + 'in-store/table-info',
		tspdbxp: t + 'in-store/shop-car-require',
		tsxdd: t + 'in-store/save-in-store-order',
		tsdd: t + 'in-store/my-in-store-order',
		tsddxq: t + 'in-store/in-store-order-info',
		tsgwc: t + 'in-store/my-in-store-car',
		tsqxdd: t + 'in-store/close-in-order',
		tsjc: t + 'in-store/add-food',
		zzsj: t + 'order/production-time',
		hydj: t + 'vip-card/level-list',
		hykk: t + 'vip-card/receive-card',
		jhhyk: t + 'vip-card/activate-membership',
		hykcs: t + 'vip-card/member-card-parameter',
		hykczz: t + 'member/growth-details',
		ffhykqy: t + 'pay-vip/rights-list',
		ffhysj: t + 'pay-vip/vip-info',
		ffhyxd: t + 'pay-vip/vip-order',
		vipData: t + 'pay-vip/vip-data',
		vipmyOrder: t + 'pay-vip/my-order',
		vipCouponOrder: t + 'pay-vip/vip-coupon-order',
		owni: t + 'old-with-new/index',
		ownrank: t + 'old-with-new/rank',
		ownilist: t + 'old-with-new/invitation-list',
		orderRep: t + 'order/replace-order',
		memberBW: t + 'member/bonus-withdrawal',
		memberWL: t + 'member/withdrawal-list',
		//
		wmfl: t + 'config/type-list',
		carlist: t + 'goods-cat/list',
		sjlb: t + 'shop/store-list',
		ggjh: t + 'config/ad-list',
		distributionAD: t + 'distribution/apply-distribution',
		distributionGO: t + 'distribution/get-offline',
		distributionIndex: t + 'distribution/index',
		distributionDO: t + 'distribution/distribution-order',
		myCollection: t + 'member/my-collection',
		getCoupon: t + 'old-with-new/get-coupon',
		vipsc: t + 'pay-vip/get-store-coupon',
		vipcpe: t + 'pay-vip/coupon-exchange',
		gsp: t + 'config/get-super-power',
		yyxx: t + 'reserve/appointment-options',
		yyxd: t + 'reserve/save-appointment-order',
		yyts: t + 'reserve/appointment-tables',
		wdyy: t + 'reserve/my-appointment-order',
		yyxq: t + 'reserve/appointment-order-info',
		qxyy: t + 'reserve/operation-appointment-order',
		pdcz: t + 'queuing/queuing-type',
		pdxd: t + 'queuing/take-number',
		pdxq: t + 'queuing/take-number-info',
		qxpd: t + 'queuing/modify-take-number',
		pdlb: t + 'queuing/my-take-number',
		ispop: t + 'dividend/is-pop',
		divreceive: t + 'dividend/receive',
		divinfo: t + 'dividend/info',
		divlist: t + 'dividend/receive-list',
		collectList: t + 'member/collect-list',
		getStoreConfig: t + 'config/get-store-config',
		bcjc: t + 'wine-storage/save-storage',
		jcfl: t + 'wine-storage/storage-brand',
		jclb: t + 'wine-storage/storage-list',
		jcqj: t + 'wine-storage/save-take-record',
		jclqlb: t + 'wine-storage/storage-take-list',
		payPlugin: t + 'shop/pay-plugin',
		specialOffer: t + 'shop/special-offer',
		getLastWithdraw: t + 'member/get-last-withdraw',
		cancelTogether: t + 'order/cancel-together', //取消拼单
		bigenTogether: t + 'order/bigen-together', //开始拼单
		modifyTogetherStatus: t + 'order/modify-together-status', //修改拼单状态
		getTogetherStatus: t + 'order/get-together-status', //获取拼单状态


		// 食材采买模块接口
		goodsupplier: t + 'good-supplier/get-product-list', //食品列表
		shopdetail: t + 'good-supplier/get-store', //店铺详情
		suppliergoodDetail: t + 'good-supplier/get-good-detail',
		suppliergoodZan: t + 'good-supplier/save-goods-zan',
		savesuppliercar: t + 'order-supplier/save-supplie-car', //加入购物车
		getsuppliercar: t + 'order-supplier/get-car-list', // 获取购物车
		delsuppliercar: t + 'order-supplier/del-car', //清空购物车
		saveorder: t + 'order-supplier/save-order', //下单

		lineLogin: t + 'site/line-login', //line登录
		ucoinlog: t + 'member/ucoin-log', //U币记录
	};
export default api