/**
 * LINE Pay 工具类
 * 参考文档: https://developers-pay.line.me/zh/online/implement-basic-payment#request-payment
 */

class LinePay {
  constructor() {
    // LINE Pay 配置
    this.config = {
      // 沙盒环境
      sandbox: {
        baseUrl: 'https://sandbox-api-pay.line.me',
        channelId: 'YOUR_CHANNEL_ID', // 需要替换为实际的Channel ID
        channelSecret: 'YOUR_CHANNEL_SECRET', // 需要替换为实际的Channel Secret
      },
      // 生产环境
      production: {
        baseUrl: 'https://api-pay.line.me',
        channelId: 'YOUR_CHANNEL_ID', // 需要替换为实际的Channel ID
        channelSecret: 'YOUR_CHANNEL_SECRET', // 需要替换为实际的Channel Secret
      }
    };
    
    // 当前环境
    this.isProduction = process.env.NODE_ENV === 'production';
    this.currentConfig = this.isProduction ? this.config.production : this.config.sandbox;
  }

  /**
   * 生成LINE Pay签名
   * @param {string} body - 请求体
   * @param {string} channelSecret - Channel Secret
   * @returns {string} 签名
   */
  generateSignature(body, channelSecret) {
    const crypto = require('crypto');
    const signature = crypto
      .createHmac('sha256', channelSecret)
      .update(body)
      .digest('base64');
    return signature;
  }

  /**
   * 发送LINE Pay API请求
   * @param {string} method - HTTP方法
   * @param {string} path - API路径
   * @param {object} data - 请求数据
   * @returns {Promise<object>} 响应数据
   */
  async requestLinePayAPI(method, path, data = {}) {
    const url = `${this.currentConfig.baseUrl}${path}`;
    const body = JSON.stringify(data);
    const nonce = Date.now().toString();
    const signature = this.generateSignature(body, this.currentConfig.channelSecret);

    const headers = {
      'Content-Type': 'application/json',
      'X-LINE-ChannelId': this.currentConfig.channelId,
      'X-LINE-Authorization-Nonce': nonce,
      'X-LINE-Signature': signature,
    };

    try {
      const response = await fetch(url, {
        method,
        headers,
        body: method !== 'GET' ? body : undefined,
      });

      const result = await response.json();
      
      if (result.returnCode === '0000') {
        return {
          success: true,
          data: result,
        };
      } else {
        return {
          success: false,
          error: result.returnMessage || 'LINE Pay API请求失败',
          code: result.returnCode,
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error.message || '网络请求失败',
      };
    }
  }

  /**
   * 创建付款请求
   * @param {object} paymentData - 付款数据
   * @returns {Promise<object>} 响应结果
   */
  async createPaymentRequest(paymentData) {
    const data = {
      amount: paymentData.amount,
      currency: paymentData.currency,
      orderId: paymentData.orderId,
      packages: paymentData.packages,
      redirectUrls: paymentData.redirectUrls,
    };

    return await this.requestLinePayAPI('POST', '/v3/payments/request', data);
  }

  /**
   * 确认付款
   * @param {string} transactionId - 交易ID
   * @param {object} confirmData - 确认数据
   * @returns {Promise<object>} 响应结果
   */
  async confirmPayment(transactionId, confirmData) {
    const data = {
      amount: confirmData.amount,
      currency: confirmData.currency,
    };

    return await this.requestLinePayAPI('POST', `/v3/payments/${transactionId}/confirm`, data);
  }

  /**
   * 查询付款詳情
   * @param {string} transactionId - 交易ID
   * @returns {Promise<object>} 响应结果
   */
  async getPaymentDetails(transactionId) {
    return await this.requestLinePayAPI('GET', `/v3/payments/requests/${transactionId}`);
  }

  /**
   * 退款
   * @param {string} transactionId - 交易ID
   * @param {object} refundData - 退款数据
   * @returns {Promise<object>} 响应结果
   */
  async refundPayment(transactionId, refundData) {
    const data = {
      refundAmount: refundData.refundAmount,
      currency: refundData.currency,
    };

    return await this.requestLinePayAPI('POST', `/v3/payments/${transactionId}/refund`, data);
  }

  /**
   * 验证回调签名
   * @param {string} body - 请求体
   * @param {string} signature - 签名
   * @returns {boolean} 验证结果
   */
  verifyCallbackSignature(body, signature) {
    const expectedSignature = this.generateSignature(body, this.currentConfig.channelSecret);
    return signature === expectedSignature;
  }

  /**
   * 格式化金额（转换为分）
   * @param {number} amount - 金额
   * @returns {number} 格式化后的金额
   */
  formatAmount(amount) {
    return Math.round(Number(amount) * 100);
  }

  /**
   * 构建商品包数据
   * @param {object} orderInfo - 订单信息
   * @returns {array} 商品包数组
   */
  buildPackages(orderInfo) {
    return [
      {
        id: "1",
        amount: this.formatAmount(orderInfo.money),
        products: [
          {
            id: orderInfo.orderId || "PRODUCT_001",
            name: orderInfo.storeName || "商品",
            imageUrl: orderInfo.imageUrl || "https://store.example.com/images/product.jpg",
            quantity: 1,
            price: this.formatAmount(orderInfo.money),
          },
        ],
      },
    ];
  }

  /**
   * 构建重定向URL
   * @param {string} orderId - 订单ID
   * @param {string} baseUrl - 基础URL
   * @returns {object} 重定向URL对象
   */
  buildRedirectUrls(orderId, baseUrl) {
    return {
      confirmUrl: `${baseUrl}/index.php/channelApi/pay/line-pay-callback?orderId=${orderId}`,
      cancelUrl: `${baseUrl}/h5/#/yb_o2ov2/order/order-info?orderId=${orderId}`,
    };
  }
}

export default LinePay; 