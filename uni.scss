/**
 * 这里是uni-app内置的常用样式变量123
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */

/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* #ifndef APP-NVUE */
@import 'uview-ui/theme.scss';
view {
	font-family: Source Han Sans CN;
}
.custom-u-tabs {
	/deep/.u-tab-item {
		background: $uni-bg-color-grey;
		font-size: 24rpx !important;
		height: 48rpx !important;
		line-height: 48rpx !important;
		margin-right: 20rpx !important;
		border-radius: 10rpx;
	}
}

.footer-btn-fiexd{
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	position: fixed;
	bottom: 0rpx;
	left: 0rpx;
	right: 0rpx;
	height: 100rpx;
	background-color: #FFFFFF;
	z-index: 1;
	box-shadow: 0 -1rpx 10rpx 0 rgba(0,0,0,0.1);
}

.flex {
	display: flex;
	display: -webkit-flex;
	display: -moz-flex;
	display: -ms-flex;
	display: -o-flex;
}

.flex-auto {
	flex: 1;
}

.flex-direction-row {
	flex-direction: row;
}

.flex-direction-column {
	flex-direction: column;
}

.flex-align-center {
	align-items: center;
	-webkit-align-items: center;
	-o-align-items: center;
	-ms-align-items: center;
	-moz-align-items: center;
}

.flex-align-start {
	align-items: flex-start;
	-webkit-align-items: flex-start;
	-moz-align-items: flex-start;
	-ms-align-items: flex-start;
	-o-align-items: flex-start;
}

.flex-align-end {
	align-items: flex-end;
	-webkit-align-items: -webkit-flex-end;
	-moz-align-items: -moz-flex-end;
	-o-align-items: -o-flex-end;
	-ms-align-items: -ms-flex-end;
}

.flex-wrap {
	flex-wrap: wrap;
	-webkit-flex-wrap: wrap;
	-moz-flex-wrap: wrap;
	-ms-flex-wrap: wrap;
	-o-flex-wrap: wrap;
}

.justify-space-between {
	justify-content: space-between;
	-webkit-justify-content: space-between;
	-moz-justify-content: space-between;
	-ms-justify-content: space-between;
	-o-justify-content: space-between;
}

.justify-space-center {
	justify-content: center;
	-webkit-justify-content: center;
	-moz-justify-content: center;
	-ms-justify-content: center;
	-o-justify-content: center;
}
.justify-space-end {
	justify-content: flex-end;
	-webkit-justify-content: flex-end;
	-moz-justify-content: flex-end;
	-ms-justify-content: flex-end;
	-o-justify-content: flex-end;
}
/* #endif */

/* 颜色变量 */

/* 行为相关颜色 */
$uni-color-primary: #007aff;
$uni-color-success: #4cd964;
$uni-color-warning: #f0ad4e;
$uni-color-error: #dd524d;

/* 文字基本颜色 */
$uni-text-color: #333; //基本色
$uni-text-color-inverse: #fff; //反色
$uni-text-color-grey: #999; //辅助灰色，如加载更多的提示信息
$uni-text-color-placeholder: #808080;
$uni-text-color-disable: #c0c0c0;

/* 背景颜色 */
$uni-bg-color: #ffffff;
$uni-bg-color-grey: #f8f8f8;
$uni-bg-color-hover: #f1f1f1; //点击状态颜色
$uni-bg-color-mask: rgba(0, 0, 0, 0.4); //遮罩颜色

/* 边框颜色 */
$uni-border-color: #c8c7cc;

/* 尺寸变量 */

/* 文字尺寸 */
$uni-font-size-sm: 24rpx;
$uni-font-size-base: 28rpx;
$uni-font-size-lg: 32rpx;

/* 图片尺寸 */
$uni-img-size-sm: 40rpx;
$uni-img-size-base: 52rpx;
$uni-img-size-lg: 80rpx;

/* Border Radius */
$uni-border-radius-sm: 4rpx;
$uni-border-radius-base: 6rpx;
$uni-border-radius-lg: 12rpx;
$uni-border-radius-circle: 50%;

/* 水平间距 */
$uni-spacing-row-sm: 10px;
$uni-spacing-row-base: 20rpx;
$uni-spacing-row-lg: 30rpx;

/* 垂直间距 */
$uni-spacing-col-sm: 8rpx;
$uni-spacing-col-base: 16rpx;
$uni-spacing-col-lg: 24rpx;

/* 透明度 */
$uni-opacity-disabled: 0.3; // 组件禁用态的透明度

/* 文章场景相关 */
$uni-color-title: #2c405a; // 文章标题颜色
$uni-font-size-title: 40rpx;
$uni-color-subtitle: #555555; // 二级标题颜色
$uni-font-size-subtitle: 36rpx;
$uni-color-paragraph: #3f536e; // 文章段落颜色
$uni-font-size-paragraph: 30rpx;

/* 自定义样式 */
/*字体颜色 */
$font-my-color-main: rgb(236, 22, 19);
$font-my-color-0: #000;
$font-my-color-3: #333;
$font-my-color-6: #666;
$font-my-color-8: #888;
$font-my-color-9: #999;
$font-my-color-a: #aaa;
$font-my-color-b: #bbb;
$font-my-color-c: #ccc;
$font-my-color-d: #ddd;
$font-my-color-f1: #f1f1f1;
$font-my-color-f2: #f2f2f2;
/*字体大小 */
$font-my-size-22: 22rpx;
$font-my-size-23: 23rpx;
$font-my-size-24: 24rpx;
$font-my-size-26: 26rpx;
$font-my-size-28: 28rpx;
$font-my-size-30: 30rpx;
$font-my-size-32: 32rpx;
$font-my-size-34: 34rpx;
$font-my-size-36: 36rpx;
$font-my-size-38: 38rpx;
$font-my-size-40: 40rpx;
$font-my-size-42: 42rpx;
$font-my-size-44: 44rpx;
$font-my-size-46: 46rpx;
$font-my-size-48: 48rpx;
$font-my-size-50: 50rpx;
$font-my-size-52: 52rpx;
$font-my-size-54: 54rpx;

/* #ifdef H5 */
$page-full-screen: 100%;
uni-page-body {
	height: 100%;
}
/* #endif */

/*#ifdef APP-PLUS*/
$page-full-screen: 100vh;


/*#endif*/
