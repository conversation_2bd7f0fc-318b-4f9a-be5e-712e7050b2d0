{"name": "mg_uniapp", "version": "1.0.0", "description": "", "main": "main.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/orangeflash/uni-app.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/orangeflash/uni-app/issues"}, "homepage": "https://github.com/orangeflash/uni-app#readme", "dependencies": {"@googlemaps/js-api-loader": "^1.16.8", "html5-qrcode": "^2.3.8", "jsqr": "^1.4.0", "lodash": "^4.17.20", "node-sass": "^9.0.0", "qrcode": "^1.5.4", "vue-i18n": "^11.1.3", "weixin-js-sdk": "^1.4.0-test"}}