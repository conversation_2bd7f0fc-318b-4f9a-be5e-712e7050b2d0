import Vue from 'vue'
import App from './App'
import store from './store'
import util from './common/we7/util';
import api from './common/api';
import common from './common/common' //全局混入
// 自定义全局下拉刷新组件
import MescrollUni from './mescroll-uni/mescroll-uni.vue';
// 引入空布局组件
import MescrollEmpty from '@/mescroll-uni/components/mescroll-empty.vue';
// 加载
import Load from "@/components/Load.vue"

import i18n from "@/locales/index.js"
// import TabBar from '@/components/common/tabbar.vue'
// import MgImg from '@/components/common/mg-img.vue'
// import MgCell from '@/components/common/mg-cell.vue'
//自定义属性
// Vue.prototype.$store = store
Vue.prototype.util = util
Vue.prototype.api = api
// Vue.prototype._ = _
Vue.config.productionTip = false

Vue.use(common);
Vue.use(i18n)
Vue.component('mescroll-uni',MescrollUni)
Vue.component('mescroll-empty',MescrollEmpty)
Vue.component('Load',Load)
// Vue.component('tab-bar', TabBar)
// Vue.component('mg-img', MgImg)
// Vue.component('mg-cell', MgCell)
App.mpType = 'app'
// 引入全局uView
import uView from 'uview-ui';
Vue.use(uView);
const {log} = console 
Vue.prototype.LOG = log

const app = new Vue({
	i18n,
	log,
	store,
	...App
})
app.$mount()
