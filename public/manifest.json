{"name": "Union", "short_name": "Union", "description": "Union - 优联网H5应用", "start_url": "./", "scope": "./", "display": "standalone", "orientation": "portrait", "background_color": "#ffffff", "theme_color": "#FFCC00", "lang": "zh-CN", "icons": [{"src": "/icon/sys_icon3.png", "sizes": "72x72", "type": "image/png", "purpose": "any"}, {"src": "/icon/sys_icon3.png", "sizes": "96x96", "type": "image/png", "purpose": "any"}, {"src": "/icon/sys_icon3.png", "sizes": "128x128", "type": "image/png", "purpose": "any"}, {"src": "/icon/sys_icon3.png", "sizes": "144x144", "type": "image/png", "purpose": "any"}, {"src": "/icon/sys_icon3.png", "sizes": "152x152", "type": "image/png", "purpose": "any"}, {"src": "/icon/sys_icon3.png", "sizes": "192x192", "type": "image/png", "purpose": "any"}, {"src": "/icon/sys_icon3.png", "sizes": "384x384", "type": "image/png", "purpose": "any"}, {"src": "/icon/sys_icon3.png", "sizes": "512x512", "type": "image/png", "purpose": "any"}, {"src": "/icon/sys_icon3.png", "sizes": "192x192", "type": "image/png", "purpose": "maskable"}, {"src": "/icon/sys_icon3.png", "sizes": "512x512", "type": "image/png", "purpose": "maskable"}]}