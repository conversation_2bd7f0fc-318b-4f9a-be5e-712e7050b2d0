<template>
	<view>
		<view id="mainPage" :style="{height:MainPageHeight+'rpx'}">
			<PageHome v-show="showPage==='initIndex'" ref="initIndex"/>
			<PageVip :isVip="isVip" v-show="showPage==='initVip'" ref="initVip" />
			<PageOrder v-show="showPage==='initOrder'" ref="initOrder" />
			<PageMy v-show="showPage==='initMy'" ref="initMy" />
		</view>
		<!-- tab -->
		<TabBar ref="tabbar" @changeTab="changeTab"></TabBar>
		<!-- <Load :show="showLoad"></Load> -->
		<!-- <view v-if="authorization">
			<Authorization :show="authorization"></Authorization>
		</view> -->
		<!-- 开屏广告 -->
		<block v-if="layout.open&&layout.open[0]">
			<OpenAdv ref="openadv" :styles="layout.open[0].styles"></OpenAdv>
		</block>
		<block v-else>
			<Load :show="showLoad"></Load>
		</block>
		</block>
		<!-- 弹窗优惠券 -->
		<!-- 1、商店优惠券2、平台优惠券3、新人专享弹窗 -->
		<tcyhq type="1" @close='sjhbshow=false' :co='sjhbInfo' v-model="sjhbshow" :color='tColor'></tcyhq>
		<tcyhq type="2" @close='pthbshow=false' :co='pthbInfo' v-model="pthbshow" :color='tColor'></tcyhq>
		<tcyhq type="3" @close='xrzxshow=false' :co='xrzxInfo' v-model="xrzxshow" :color='tColor'></tcyhq>
		<block v-if="currency.yszz && currency.yszz==1">
			<yszc :value="fwxyshow" @close='fwxyshow=false'></yszc>
		</block>
	</view>
</template>
<script>
	import {
		mapState,
		mapActions
	} from 'vuex'
	import utils from '@/common/utils.js'
	import PageHome from './component/PageHome.vue'
	import PageVip from './component/PageVip.vue'
	import PageOrder from './component/PageOrder.vue'
	import PageMy from './component/PageMy.vue'
	import Authorization from "@/components/Authorization.vue"
	import OpenAdv from "@/components/OpenAdv.vue"
	import TabBar from "@/components/TabBar.vue"
	import tcyhq from '@/components/template/tcyhq.vue'
	import yszc from './component/yszc.vue'
	
	export default {
		components: {
			PageHome,
			PageVip,
			PageOrder,
			PageMy,
			Authorization,
			OpenAdv,
			TabBar,
			tcyhq,
			yszc
		},
		data() {
			return {
				showPage:'',
				custom:true,
				animationIndex:false,  // 动画效果开关
				animationVip:false, // 动画效果开关
				animationOrder:false, // 动画效果开关
				animationMy:false, // 动画效果开关
				// 是否初次加载 
				initIndex:false,  
				initVip:false, 
				initOrder:false, 
				initMy:false, 
				navBarHeight:0,
				isVip:false,
				showLoad:true,
				authorization:true, //用户协议
				sjhbInfo: '',
				sjhbshow: false,
				pthbInfo: '',
				pthbshow: false,
				xrzxInfo: '',
				xrzxshow: false,
				fwxyshow: false,
			};
		},
		async onLoad(options) {
			this.query = options
			if (options && options.scene) {
				this.storeId = ''
				this.ldxId = decodeURIComponent(options.scene).split(',')[0]
				this.ldxType = decodeURIComponent(options.scene).split(',')[1]
			} 
			await this.init(options.changeTab)
			if(!uni.getStorageSync('isYszc')){
				this.fwxyshow = true
			}
			//console.log('yszz',uni.getStorageSync('isYszc'),this.currency)
			// this.go('navigateTo',`/yb_o2ov2/my/integral/integral-mall`)
			// this.changeTab({name:{id:'payVip'},category:'平台首页'})
		},
		onShow() {
			if (this.uId) {
				this.refreshUser({
					nomask: 1,
					get: 1,
					now: 1,
				})
			}
		},
		onReady() {
		
		},
		onTabItemTap : function(e) {
			console.log(e);
			// e的返回格式为json对象： {"index":0,"text":"首页","pagePath":"pages/index/index"}
		},
		computed: {
			...mapState({
				pxToRpxRate:state=>state.systemInfo.pxToRpxRate,
				MainPageHeight:state=>state.systemInfo.MainPageHeight,
				layout:state=>state.layout.index.body,
				storeSet: state => state.config.storeSet,
				currency: state => state.config.currency,
				user: state => state.user,
			})
		},
		watch:{
		},
		methods: {
			...mapActions(["setSystemInfo","getConfig"]),
			async init(changeTab){
				this.showLoad = true
				// 获取平台系统设置
				// this.getSystem()
				// 获取手机屏幕px转化rpx比
				// this.GetPxToRpxRate()
				// 判断是否登录 在此后的请求 全部加上userID
				// this.getLoginInfo({
				// 	inviteId: this.ldxId,
				// 	type: this.ldxType
				// })
				// 获取支付 订单 外卖配置
				// await this.setConfig()
				//获取首页拖拽布局
				await this.getLayout()
				// 获取定位信息 用于加载店铺信息
				// await this.getLocInfo()
				// 加载首页
				if(this.storeSet.storeModel==='1'){
					this.go('redirectTo',`/yb_o2ov2/home/<USER>
					return
				}
				if(changeTab){//从其他页面跳转过来
					this.changeTab(JSON.parse(changeTab))
				}else{
					await this.$refs.tabbar.changeTab(0)
				}
				this.showLoad = false
				if(this.$refs.openadv){
					this.$refs.openadv.open = true
				}
				setTimeout(() => {
					this.addFwjl({
						storeId: this.storeSet.selectStore,
						origin: '1'
					})
				}, 1000)
				this.getXrzx()
				
			},
			async setConfig(){
				 this.getConfig({
					name: 'payConfig',
					api: this.api.config,
					data: { ident: 'payConfig' }
				})
				 this.getConfig({
					name:'orderSet',
					api:this.api.orderSet,
					data:{}
				})
				await this.getConfig({
					name:'currency',
					api:this.api.config,
					data:{ident: 'currency'}
				})
				await this.getConfig({
					name: 'storeSet',
					api: this.api.config,
					data: { ident: 'storeSet' }
				})
			},
			async GetPxToRpxRate(){
				if(!this.pxToRpxRate){
					await this.setSystemInfo()
				}
			},
			async changeTab(urlItem){
				
				//每个页面需要一个init函数 用来做初始化
				let _this = this
				async function load(init){
					_this.showPage = init
					// 无init方法的组件不执行
					if(_this[init]) return
					// 初次加载页面
					if(!_this.$refs[init].init) return
					await _this.$refs[init].init()
				}
				if(urlItem.category !== '平台首页') return this.goUrl(urlItem)
				//点击初始化 需要第一次点击后加载页面请求 TODO
				let homeUrl = this.layout.menu[0].styles.imgUrl
				
				let tabIndex = ''
				switch(urlItem.name.id){
					case 'index': //首页
						tabIndex = homeUrl.findIndex(t=>t.url.name.id===urlItem.name.id)
						await load('initIndex')
						break;
					case 'payVip'://月卡
						tabIndex = homeUrl.findIndex(t=>t.url.name.id===urlItem.name.id)
						await load('initVip')
						// console.log(this.user.isVip)
						this.isVip = this.user.isVip
						break;
					case 'myOrder'://订单
						tabIndex = homeUrl.findIndex(t=>t.url.name.id===urlItem.name.id)
						await load('initOrder')
						break;
					case 'member'://个人中心
						tabIndex = homeUrl.findIndex(t=>t.url.name.id===urlItem.name.id)
						await load('initMy')
						break;
				}
				this.$refs.tabbar.animationIndex = tabIndex
			},
			async getXrzx() {
				let res = await this.util.request({
					'url': this.api.sytchj,
					'data':{
						couponType:1
					}
				})
				if (res.data.newCoupon) {
					this.xrzxInfo = res.data.newCoupon.data
					setTimeout(() => {
						this.xrzxshow = true
					}, 1000)
				}
				if (res.data.windowCoupons) {
					if(res.data.windowCoupons.couponType=='1'){
						this.pthbInfo = res.data.windowCoupons
						setTimeout(() => {
							this.pthbshow = true
						}, 2000)
					}else if(res.data.windowCoupons.couponType=='2'){
						this.sjhbInfo = res.data.windowCoupons
						setTimeout(() => {
							this.sjhbshow = true
						}, 2000)
					}
				}
			},
		},
		onShareAppMessage() {
			// let p = `yb_o2o/index/index?userId=${this.uId}`
			return this.util.mpShare({
				t: this.system.forwardTitle,
				i: this.getImgS(this.system.forwardIcon),
				// p,
			})
		},
		onShareTimeline(e) {
			// console.log("点击分享pyq", e)
			return {
				title: this.system.shareTitle,
				imageUrl: this.getImgS(this.system.shareIcon),
			}
		},
	};
</script>
<style lang="scss" scoped>
	#mainPage{
		overflow: hidden;
	}
</style>
