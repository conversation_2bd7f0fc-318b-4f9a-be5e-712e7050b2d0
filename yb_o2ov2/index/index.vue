<template>
	<scroll-view class="page" scroll-y @scrolltolower="onPullUpBottom">
		<view class="page-body">
			<view class="heander">
				<view class="location" @click="go('navigateTo','/yb_o2ov2/home/<USER>/index')">
					<image src="/static/home/<USER>" class="location-icon"></image>
					<text v-if="locationInfo.maddress">{{locationInfo.maddress}}</text>
					<text v-else>{{$t('home.location')}}</text>
					<image src="/static/home/<USER>" class="arrow-icon"></image>
				</view>
				<view class="search-box">
					<view class="search">
						<input class="input" v-model="searchKeyWord" :placeholder="$t('home.input_placeholder')"
							@confirm="toSearch" />
						<view class="button" @click="toSearch">{{$t('home.search_text')}}</view>
					</view>
					<view class="scan" @click="scanCode()">
						<image src="/static/home/<USER>" class="icon"></image>
					</view>
				</view>
				<!-- <view class="swiper-box"
					:class="{'swiper-pl':swiperCurrent != swiperList.length-1 || swiperList.length==1}">
					<swiper class="swiper" :previous-margin="swiperStyle.left+'rpx'"
						:next-margin="swiperStyle.right+'rpx'" circular autoplay @change="evenChange">
						<swiper-item class="swiper-item" v-for="(item,index) in swiperList" :key="index">
							<image :src="item.url" class="swiper-item-iamge"></image>
						</swiper-item>
					</swiper>
				</view> -->
				<view class="swiper-box swiper-pl">
					<swiper class="swiper"  circular autoplay @change="evenChange">
						<swiper-item class="swiper-item" v-for="(item,index) in swiperList" :key="index" @click="handleUrl(item.link)">
							<image :src="item.url" class="swiper-item-iamge"></image>
						</swiper-item>
					</swiper>
				</view>
			</view>

			<view class="category-box">
				<view class="category-title">
					<text>{{$t('home.category_title')}}</text>
					<image class="category-icon" src="/static/home/<USER>" mode=""></image>
				</view>

				<scroll-view class="scroll-view" scroll-x>
					<view class="category-item" v-for="(item,index) in categoryList" :key="item.id"
						@click="handleCategory(item)">
						<view class="item-top">
							<svg class="svg-icon" viewBox="0 0 90 60" xmlns="http://www.w3.org/2000/svg">
								<g id="Rectangle 5985">
									<g id="Rectangle 5985_2">
										<path id="Rectangle 5985_3"
											d="M0 10C0 4.47715 4.47715 0 10 0H80C85.5228 0 90 4.47715 90 10V47.5338C90 53.9313 84.0758 58.6835 77.8307 57.2957L49.3386 50.9641C46.4811 50.3291 43.5189 50.3291 40.6614 50.9641L12.1693 57.2957C5.92415 58.6835 0 53.9313 0 47.5338V10Z"
											:fill="category_item_bg_color(index)" />
									</g>
								</g>
							</svg>
							<image :src="item.icon" class="category-image"></image>
						</view>
						<view class="label">{{item.name}}</view>
						<view class="item-icon-box">
							<image class="itme-icon" src="/static/home/<USER>" mode=""></image>
						</view>
					</view>
				</scroll-view>
			</view>
			<view class="shop-box">
				<shop-list v-for="item in list" :key="item.id" :item="item"></shop-list>
				<view class="no-data">--{{$t('common.no_data')}}--</view>
			</view>
		</view>
		<TabBar :current="0"></TabBar>
	</scroll-view>
</template>

<script>
	import {
		mapState,
	} from 'vuex'
	import TabBar from "@/components/TabBar.vue"
	import shopList from "./component/shopList.vue"
	import jsQR from 'jsqr'

	export default {
		components: {
			TabBar,
			shopList,
		},
		computed: {
			...mapState('dndc', ['addInfo']),
			category_item_bg_color() {
				return function(index) {
					let remainder = index % 4
					let color = ''
					switch (remainder) {
						case 0:
							color = '#FFC900'
							break;
						case 1:
							color = '#76CB0B'
							break;
						case 2:
							color = '#F89203'
							break;
						case 3:
							color = '#009D5E'
							break;
					}
					return color
				}
			},
			swiperStyle() {
				if (this.swiperList.length == 1) {
					return {
						left: 0,
						right: 0
					}
				} else if (this.swiperList.length - 1 == this.swiperCurrent) {
					return {
						left: 120,
						right: 0
					}
				} else {
					return {
						left: 0,
						right: 120
					}
				}
			}
		},
		data() {
			return {
				categoryList: [],
				swiperCurrent: 0,
				swiperList: [],
				list: [],
				pageDate: {
					page: 1,
					size: 15
				},
				flag: true,
				searchKeyWord: '',
				locationInfo: {
					maddress: '',
					latitude: 0,
					longitude: 0
				}
			}
		},
		async onLoad() {
			// 优先从本地存储加载位置信息
			const savedLocation = uni.getStorageSync('locationInfo');
			if (savedLocation) {
				this.locationInfo = JSON.parse(savedLocation);
				console.log('使用本地存储的位置信息:', this.locationInfo);
			} else {
				// 如果没有本地存储的位置信息，则获取新位置
				this.getLocation();
			}
			
			this.getSwiper();
			this.getCategory();
			this.getList()
		},
		methods: {
			handleUrl(link) {
				if (!link) return;
				
				// 如果是外部链接
				if (link.startsWith('http://') || link.startsWith('https://')) {
					// #ifdef H5
					window.open(link, '_blank');
					// #endif
					
					// #ifdef APP-PLUS
					plus.runtime.openURL(link);
					// #endif
					
					return;
				}
				
				// 如果是内部页面链接
				if (link.startsWith('/')) {
					this.go('navigateTo', link);
					return;
				}
				
				// 如果是商品链接
				if (link.includes('product')) {
					const productId = link.split('product/')[1];
					this.go('navigateTo', `/yb_o2ov2/home/<USER>
					return;
				}
				
				// 如果是店铺链接
				if (link.includes('store')) {
					const storeId = link.split('store/')[1];
					this.go('navigateTo', `/yb_o2ov2/home/<USER>
					return;
				}
				
				// 如果是分类链接
				if (link.includes('category')) {
					const categoryId = link.split('category/')[1];
					this.go('navigateTo', `/yb_o2ov2/home/<USER>/index?id=${categoryId}`);
					return;
				}
				
				// 其他情况，直接跳转
				this.go('navigateTo', link);
			},
			scanCode() {
				// #ifdef H5
				// 创建扫码容器
				const scanContainer = document.createElement('div');
				scanContainer.style.position = 'fixed';
				scanContainer.style.top = '0';
				scanContainer.style.left = '0';
				scanContainer.style.width = '100%';
				scanContainer.style.height = '100%';
				scanContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
				scanContainer.style.zIndex = '9999';
				document.body.appendChild(scanContainer);

				// 创建视频元素
				const video = document.createElement('video');
				video.style.width = '300px';
				video.style.height = '300px';
				video.style.position = 'absolute';
				video.style.top = '50%';
				video.style.left = '50%';
				video.style.transform = 'translate(-50%, -50%)';
				video.style.objectFit = 'cover';
				scanContainer.appendChild(video);

				// 创建扫码框
				const scanFrame = document.createElement('div');
				scanFrame.style.position = 'absolute';
				scanFrame.style.top = '50%';
				scanFrame.style.left = '50%';
				scanFrame.style.transform = 'translate(-50%, -50%)';
				scanFrame.style.width = '300px';
				scanFrame.style.height = '300px';
				scanFrame.style.border = '2px solid #fff';
				scanFrame.style.boxSizing = 'border-box';
				scanContainer.appendChild(scanFrame);

				// 创建关闭按钮
				const closeBtn = document.createElement('button');
				closeBtn.textContent = '关闭';
				closeBtn.style.position = 'absolute';
				closeBtn.style.bottom = '50px';
				closeBtn.style.left = '50%';
				closeBtn.style.transform = 'translateX(-50%)';
				closeBtn.style.padding = '10px 20px';
				closeBtn.style.backgroundColor = '#fff';
				closeBtn.style.border = 'none';
				closeBtn.style.borderRadius = '5px';
				closeBtn.style.cursor = 'pointer';
				scanContainer.appendChild(closeBtn);

				// 创建canvas元素用于图像处理
				const canvas = document.createElement('canvas');
				const context = canvas.getContext('2d');

				// 请求摄像头权限
				navigator.mediaDevices.getUserMedia({ video: { facingMode: 'environment' } })
					.then(stream => {
						video.srcObject = stream;
						video.play();

						// 开始扫描
						const scanInterval = setInterval(() => {
							if (video.readyState === video.HAVE_ENOUGH_DATA) {
								canvas.width = video.videoWidth;
								canvas.height = video.videoHeight;
								context.drawImage(video, 0, 0, canvas.width, canvas.height);
								
								// 获取图像数据
								const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
								
								const result = this.detectQRCode(imageData);
								if (result) {
									clearInterval(scanInterval);
									stream.getTracks().forEach(track => track.stop());
									scanContainer.remove();
									this.handleScanResult(result);
								}
							}
						}, 100);

						// 关闭按钮事件
						closeBtn.onclick = () => {
							clearInterval(scanInterval);
							stream.getTracks().forEach(track => track.stop());
							scanContainer.remove();
						};
					})
					.catch(err => {
						console.error('摄像头访问失败：', err);
						this.util.message('无法访问摄像头');
						scanContainer.remove();
					});
				// #endif

				// #ifdef APP-PLUS
				// uni.scanCode({
				// 	success: (res) => {
				// 		console.log('扫码结果：', res);
				// 		if (res.result) {
				// 			this.util.message('扫码成功');
				// 			console.log(res.result, '扫码结果');
							
				// 			// 处理扫码结果
				// 			this.handleScanResult(res.result);
				// 		}
				// 	},
				// 	fail: (err) => {
				// 		console.error('扫码失败：', err);
				// 		this.util.message('扫码失败');
				// 	}
				// });
				// #endif
			},

			// 二维码检测函数
			detectQRCode(imageData) {
				try {
					const code = jsQR(imageData.data, imageData.width, imageData.height, {
						inversionAttempts: "dontInvert",
					});
					return code ? code.data : null;
				} catch (error) {
					console.error('二维码检测错误：', error);
					return null;
				}
			},

			handleScanResult(result) {
				  try {
					const res = JSON.parse(result);
					if (res.type !== 'instoreTable') {
						return this.util.message('扫码错误', 3);
					}
					this.util.message('扫码成功', 3);
					this.go('navigateTo', `/yb_o2ov2/home/<USER>
				} catch (e) {
					this.util.message('无效的二维码内容', 3);
				}
			},
			onPullUpBottom() {
				this.getList()
			},
			evenChange(even) {
				if (even && even.detail) {
					this.swiperCurrent = even.detail.current
				}
			},
			async getCategory() {
				let {
					data
				} = await this.util.request({
					url: this.api.CategoryListAll,
					is_login: 0
				})
				this.categoryList = data
				// const params={
				// 	name:'异业店铺',
				// 	type:1,
				// 	icon:''
				// }
				// this.categoryList.unshift(params)
			},
			async getList() {
				console.log(this.flag)
				if (!this.flag) return
				this.flag = false
				const params = {
					page: this.pageDate.page,
					size: this.pageDate.size,
					lat: this.locationInfo.latitude,
					lng: this.locationInfo.longitude
				}
				console.log('获取店铺', params)
				let {
					data
				} = await this.util.request({
					url: this.api.storeRecommend,
					data: params,
					is_login: 0
				})
				this.list = this.list.concat(data.list)
				if (data && data.count > this.list.length) {
					this.pageDate.page++
					this.flag = true
				}
			},
			async getSwiper() {
				const {
					data
				} = await this.util.request({
					url: this.api.getSwiper,
					is_login: 0
				})
				this.swiperList=data.image
				console.log(this.swiperList);
				
			},
			handleCategory(item) {
				const category = encodeURIComponent(JSON.stringify({
					id: item.id,
					name: item.name,
					type: item.type
				}))
				this.go('navigateTo', `/yb_o2ov2/home/<USER>/index?item=${category}`)
			},
			toSearch() {
				this.go('navigateTo', `/yb_o2ov2/home/<USER>
			},
			// 获取位置信息
			getLocation() {
				// #ifdef H5
				if (navigator.geolocation) {
					navigator.geolocation.getCurrentPosition(
						(position) => {
							const { latitude, longitude } = position.coords;
							this.locationInfo = {
								...this.locationInfo,
								latitude,
								longitude
							};
							this.getAddressFromCoordinates(latitude, longitude);
						},
						(error) => {
							console.error('获取位置失败:', error);
							this.util.message(this.$t('address.address_error'), 3);
						},
						{
							enableHighAccuracy: true,
							timeout: 5000,
							maximumAge: 0
						}
					);
				} else {
					this.util.message('您的浏览器不支持地理定位');
				}
				// #endif

				// #ifdef APP-PLUS
				uni.getLocation({
					type: 'gcj02',
					success: (res) => {
						this.locationInfo = {
							...this.locationInfo,
							latitude: res.latitude,
							longitude: res.longitude
						};
						this.getAddressFromCoordinates(res.latitude, res.longitude);
					},
					fail: (err) => {
						console.error('获取位置失败:', err);
						this.util.message(this.$t('address.address_error'), 3);
					}
				});
				// #endif
			},

			// 根据经纬度获取详细地址
			async getAddressFromCoordinates(latitude, longitude) {
				try {
					const language = sessionStorage.getItem('language') || 'zh-TW';
					const response = await fetch(
						`https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=AIzaSyBTUfOCvsKnvX2H9xr_bbYsho1PzJWQV6c&language=zh-${language}`
					);
					const data = await response.json();
					
					if (data.status === 'OK' && data.results.length > 0) {
						let address = data.results[0].formatted_address;
						// 移除邮政编码
						address = address.replace(/\s*邮政编码:\s*\d+/g, '').trim();
						
						this.locationInfo = {
							...this.locationInfo,
							maddress: address
						};
						
						// 保存到本地存储
						uni.setStorageSync('locationInfo', JSON.stringify(this.locationInfo));
					} else {
						console.warn('未找到地址信息:', data.status);
					}
				} catch (error) {
					console.error('获取地址信息失败:', error);
					this.util.message('获取地址信息失败');
				}
			},
			
			// 清除位置信息
			clearLocationInfo() {
				this.locationInfo = {
					maddress: '',
					latitude: 0,
					longitude: 0
				};
				uni.removeStorageSync('locationInfo');
				console.log('位置信息已清除');
			}
		}
	}
</script>

<style lang="scss" scoped>
	.page {
		display: flex;
		width: 100vw;
		height: 100vh;
		flex-direction: column;

		.page-body {
			flex: 1;
			display: flex;
			flex-direction: column;
			width: 100%;

			.heander {
				width: 100%;
				height: 456rpx;
				background-image: url('../../static/home/<USER>');
				background-size: cover;
				background-repeat: no-repeat;
				background-position: center;

				.location {
					display: flex;
					height: 80rpx;
					justify-content: flex-start;
					align-items: center;
					padding: 0 30rpx;
					box-sizing: border-box;

					font-weight: 500;
					font-size: 30rpx;
					color: #333333;
					line-height: 35rpx;

					.location-icon {
						width: 30rpx;
						height: 30rpx;
						margin-right: 2rpx;
					}

					.arrow-icon {
						width: 24rpx;
						height: 24rpx;
						margin-left: 4rpx;
					}
				}

				.search-box {
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding: 0 30rpx;
					box-sizing: border-box;
					margin: 30rpx 0;

					.search {
						display: flex;
						width: 590rpx;
						height: 80rpx;
						align-items: center;
						background: #FFFFFF;
						border-radius: 10rpx;
						padding-left: 30rpx;
						box-sizing: border-box;

						.input {
							flex: 1;
							height: 100%;
							background-color: #FFFFFF;
							font-weight: 400;
							font-size: 24rpx;
							color: #333;
							line-height: 28rpx;

							&::placeholder {
								font-weight: 400;
								font-size: 24rpx;
								color: #777777;
								line-height: 28rpx;
							}
						}

						.button {
							display: flex;
							width: 100rpx;
							height: 80rpx;
							align-items: center;
							justify-content: center;
							background: #7FC705;
							border-radius: 8rpx;

							font-weight: 500;
							font-size: 24rpx;
							color: #FFFFFF;
							line-height: 28rpx;
						}
					}

					.scan {
						display: flex;
						width: 80rpx;
						height: 80rpx;
						justify-content: center;
						align-items: center;
						background: #FFFFFF;
						border-radius: 10rpx;

						.icon {
							width: 44rpx;
							height: 44rpx;
						}
					}
				}

				.swiper-box {
					width: 100%;

					.swiper {
						width: 100%;
						height: 236rpx;

						.swiper-item {
							padding-right: 20rpx;
							box-sizing: border-box;

							&:last-child {
								padding-right: 30rpx;
							}

							.swiper-item-iamge {
								width: 100%;
								height: 100%;
								object-fit: cover;
								border-radius: 20rpx;
							}
						}
					}
				}

				.swiper-pl {
					padding-left: 30rpx;
					box-sizing: border-box;
				}
			}

			.category-box {
				padding-top: 20rpx;
				padding-bottom: 30rpx;
				box-sizing: border-box;

				.category-title {
					display: flex;
					align-items: center;
					font-weight: 500;
					font-size: 28rpx;
					color: #000000;
					line-height: 33rpx;
					margin-bottom: 20rpx;
					padding-left: 30rpx;
					box-sizing: border-box;

					.category-icon {
						width: 40rpx;
						height: 40rpx;
						margin-left: 8rpx;
					}
				}

				.scroll-view {
					width: 100%;
					white-space: nowrap;
				}

				.category-item {
					display: inline-block;
					width: 180rpx;
					background: #FFFFFF;
					border-radius: 20rpx;
					padding-bottom: 20rpx;
					margin-left: 20rpx;

					&:first-child {
						margin-left: 30rpx;
					}

					&:last-child {
						margin-right: 30rpx;
					}

					.item-top {
						position: relative;
						width: 100%;
						height: 120rpx;

						.svg-icon {
							position: absolute;
							top: 0;
							left: 0;
							width: 180rpx;
							height: 120rpx;
							z-index: 1;
						}

						.category-image {
							position: absolute;
							width: 84rpx;
							height: 84rpx;
							z-index: 2;
							top: 10rpx;
							left: 50%;
							transform: translate(-50%, 0);
						}
					}

					.label {
						display: flex;
						width: 100%;
						height: 34rpx;
						justify-content: center;
						align-items: center;
						margin-top: 4rpx;
						font-weight: 500;
						font-size: 24rpx;
						color: #333333;
						line-height: 28rpx;
					}

					.item-icon-box {
						display: flex;
						width: 100%;
						height: 40rpx;
						justify-content: center;
						align-items: center;
						margin-top: 8rpx;

						.itme-icon {
							width: 40rpx;
							height: 40rpx;
						}
					}
				}
			}

			.shop-box {
				flex: 1;
				padding: 0 30rpx;
				box-sizing: border-box;
			}
		}
	}
</style>