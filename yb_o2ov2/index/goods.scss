.category {
		position: relative;
		.mtimg {
			height: 225rpx;
		}
		
		.sgmain {
			/* #ifdef H5 */
			padding-top: 120rpx;
			/* #endif */
			/* #ifndef H5 */
			padding-top: 190rpx;
			/* #endif */
		}
	}

	.header {
		height: 178rpx;
		z-index: 9;

		&-bd {
			height: 178rpx;
		}

		.myicon {
			width: 70rpx;
			height: 70rpx;
		}

		.sjimg {
			width: 90rpx;
			height: 90rpx;
		}

		.buytype {
			height: 62rpx;
			padding: 0 6rpx;
			border-radius: 45rpx;
			border: 0.5Px solid #ddd;
			background: #f6f6f6;

			.type {
				min-width: 86rpx;
				padding: 0 10rpx;
				height: 50rpx;
			}

			.atype {
				color: #fff;
				background: #000;
			}
		}
	}
.header2 {
			padding: 0rpx;
			border-radius: 30rpx 30rpx 0 0;

			.hrbd {
				// margin-top: -5rpx;
			}

			.logoimg {
				margin: -36rpx 10rpx 0 0;
				width: 120rpx;
				height: 120rpx;
			}

			.sc {
				width: 48rpx;
				height: 48rpx;
			}
		}
	.bodyer {
		padding: 0 25rpx;
	}

	.pt150 {
		padding-top: 240rpx;
	}

	.category-fix {
		position: absolute;
		left: 0;
		top: 0;
		z-index: 9;
	}

	// height: 100%;
	.category-c {
		width: 100%;
		height: 100%;

		.left-c {
			width: 168rpx;
			flex: 0 0 168rpx;
			// padding-bottom: 0rpx;
			background-color: #F6F6F6;

			.title-c {
				position: relative;
				height: 140rpx;
				color: #999;
				font-size: 26rpx;
				background: #fff;

				.title-n {
					// width: 80%;
					margin: auto;
					height: 100%;
					padding-left: 10rpx;
					background: #F6F6F6;
					overflow: hidden;

					.title-img {
						width: 40rpx;
						height: 40rpx;
						margin-right: 10rpx;
					}
				}

				.title-b {
					position: absolute;
					left: 0;
					top: 0;
					width: 7rpx;
					height: 100%;
					background: #fff;
				}

				.title-bx {
					position: absolute;
					right: 0;
					top: 8rpx;
					background: #FFEFEA;
					padding: 3rpx 10rpx;
					border-radius: 15rpx 0 0 15rpx;
				}

				&.onSelected {
					.title-n{
						background: #fff;
					}
					color: #333;
					font-weight: bold;
				}
			}

			.ysyj {
				border-top-right-radius: 14rpx;
			}

			.yxyj {
				border-bottom-right-radius: 14rpx;
			}
		}

		.right-c {
			background: #fff;
			// padding-bottom: 0rpx;

			.c-item {
				.c-title {
					font-size: 24rpx;
					color: #777;
					padding: 20rpx 30rpx 0 20rpx;
				}
			}

			.conw2mb {
				padding-bottom: 420rpx;
			}
		}
	}

	.conw2 {
		padding: 20rpx;

		.left {
			width: 168rpx;
			height: 168rpx;
			background: #F6F6F6;
			border-radius: 12rpx;
		}
		.ppbq {
				background: #FFE054;
				top: 0rpx;
				left: -1rpx;
				padding: 0rpx 8rpx 2rpx;
				border-radius: 10rpx 0 10rpx 10rpx;
		
				&::after {
					content: '';
					width: 0;
					height: 0;
					border-bottom: 9rpx solid #948233;
					border-right: 9rpx solid transparent;
					position: absolute;
					right: -8rpx;
					top: 0;
				}
		}
		.ysq {
				height: 42rpx;
				background: rgba(0,0,0,0.5)
			}
	}

	.ggc {
		position: relative;
		border-radius: 12rpx;
		font-weight: bold;
		height: 42rpx;
		width: 96rpx;
		margin-right: 10rpx;
		border: none;
	}

	.dot {
		position: absolute;
		width: 36rpx;
		height: 36rpx;
		right: -10rpx;
		top: -18rpx;
		border-radius: 50%;
		color: #fff;
		// border: 1rpx solid #fff;
	}

	.mr5 {
		margin-right: 5rpx;
	}

	.gyhqc {
		background: #FE624B;
		height: 48rpx;
		border-radius: 6rpx;
		overflow: hidden;

		.gyhql {
			padding: 6rpx 16rpx;
		}

		.gyhqr {
			padding: 0 16rpx;
			min-width: 56rpx;
			border-left: 1px dashed #fff;
		}

		.gdot {
			width: 8rpx;
			height: 8rpx;
			border-radius: 50%;
			background: #fff;
			right: -4rpx;
		}

		.gdot1 {
			top: -4rpx;
		}

		.gdot2 {
			bottom: -4rpx;
		}
	}

	.gyhqc2 {
		background: linear-gradient(90deg, #FF7D64, #FE624B);

		.gyhqb1 {
			width: 48rpx;
			background: #FFDCAF;
			border-bottom-right-radius: 24rpx;
		}
	}

	.gyhqc3 {
		color: #532600;
		background: linear-gradient(90deg, #FFE19C, #FFC467);

		.gyhqb1 {
			background: linear-gradient(135deg, #FADCA0, #FFB13A);
		}

		.gyhqr {
			border-left: 1px dashed #DBAF6A;
		}
	}

	.mjc {
		height: 40rpx;
	}