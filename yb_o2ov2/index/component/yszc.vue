<template>
	<view class="p30">
	<u-mask :show="value" :custom-style="{background: tColor}">
		<view class="p-a p-a-c w100" >
			<view class="mla bf bs15 p43" style="width: 650rpx;">
				<view class="f-c">
					<image class="wh dlb" :src="system.icon"  style="width: 140rpx;height: 140rpx;"></image>
				</view>
				<view class="t-c mt20">
					<view class="f30 mt20">服务协议和隐私条款</view>
					<view class="f26 c6 mt40">
						欢迎您使用{{system.name}}。请您仔细阅读并充分理解
						<text :style="{color:tColor}" @click="go('navigateTo',`/yb_o2ov2/my/other/gywm?t=${'隐私政策'}&p=${5}`)">《隐私政策》</text>,如您同意
						<text :style="{color:tColor}" @click="go('navigateTo',`/yb_o2ov2/my/other/gywm?t=${'隐私政策'}&p=${5}`)">《隐私政策》</text>
						的全部内容，请点击"同意"开始使用我们的服务。
					</view>
				</view>
				<view class="mt90 f-c">
					<button class="clearbtn sbtn sbtn2 f-c cf f26 qdsbtn" @click="tyxy" :style="{background: tColor}">同意</button>
				</view>
				<view class="c9 mt20 t-c f26"><navigator open-type="exit" target="miniProgram">不同意</navigator></view>
			</view>
			<!-- <view @click="show = false" class="mt60 f-c"><text class="iconfont iconcancelorder f50 cf"></text></view> -->
		</view>
	</u-mask>
</view>
</template>

<script>
	import {
		mapState,
		mapActions
	} from 'vuex'
	import mgImg from '@/components/common/mg-img.vue'
	import utils from '@/common/utils.js'
	export default {
		name: 'searchBox',
		components: {
			mgImg,
		},
		props: {
			co: {
				type: Object,
				default: function() {
					return {}
				}
			},
			value: {
				type: Boolean,
				default: false
			},
			color: {
				type: String,
				default: ''
			},
		},
		data() {
			return {
				loading: false,
			}
		},
		computed: {
			show: {
				get() {
					return this.value;
				},
				set(newVal) {
					this.$emit("input", newVal)
				}
			},
		},
		methods: {
			tyxy(){
				uni.setStorageSync('isYszc', true)
				this.$emit("close",false)
			},
			async dllq() {
				if(!this.isLogin){
					this.go('navigateTo',`/yb_o2ov2/my/login`)
				}
				if (!await this.checkLogin()) 
				this.$emit('close')
				return
			},
		},
		async created() {}
	}
</script>

<style scoped lang="scss">
	.dlb{
		position: absolute;
		top: -80rpx;
		margin-left: 25rpx;
	}
	.sbtn {
		width: 540rpx;
		height: 74rpx;
		background: linear-gradient(-90deg, rgba(233, 87, 0, 1) 0%, rgba(233, 87, 0, 1) 100%);
		border-radius: 47px;
	}
	.sbtn2{
		height: 74rpx;
	}
	
	.ysbtn {
		background: #999;
		color: #fff;
	}
	
	.qdsbtn{
		background: linear-gradient(-90deg, rgba(233, 87, 0, 1) 0%, rgba(233, 87, 0, 1) 100%);
	}
</style>
