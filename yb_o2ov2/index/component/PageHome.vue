<template>
	<mescroll-uni ref="mescroll" :height="`${height}rpx`" :down="{auto:false}" :up="{onScroll:true,auto:false}"
	 @scroll="listeningScroll" @down="refresh" @up="onreachBottom" class="p-r">
		<view style="background:#f5f6f9">
			<!-- custom 有顶部导航 无顶部导航 -->
			<view class="w100" :style="{height:navBarHeight+'px',background:customeColor,opacity:positionColorOpacity}">
			</view>
			<!-- 提示收藏tip -->
			<AddMyMiniApp v-if="layout.attention[0].styles.status === 1" :styles="layout.attention[0].styles"></AddMyMiniApp>
			<!-- 悬浮按钮 -->
			<SuspensionButton  v-if="layout.susBtn[0].styles.status === 1" :styles="layout.susBtn[0].styles"></SuspensionButton>
			<block v-for="(drag,index) in layout.list" :key="index">
				<!-- 固定定位-->
				<Position v-if="drag.name === 'fixed'" :styles="drag.styles" id="position" @storeListChangeFixedDot=storeListChangeFixedDot :positionFixedDot="positionFixedDot" :paddingTop="navBarHeight" :positionColorOpacity="positionColorOpacity" :handleScroll="handleScroll"></Position>
				<!-- 搜索  -->
				<Search v-else-if="drag.name === 'search'" :styles="drag.styles"></Search>
				<!-- 幻灯片  -->
				<CustomSwiper  v-else-if="drag.name === 'picLunbo'" :styles="drag.styles"></CustomSwiper>
				<!-- 图片组 -->
				<ImageGroup v-else-if="drag.name === 'pictures'" :styles="drag.styles"></ImageGroup>
				<!-- 标题栏 -->
				<TitleBlock v-else-if="drag.name === 'titles'" :styles="drag.styles"></TitleBlock>
				<!-- 辅助空白 -->
				<BlankBlock v-else-if="drag.name === 'blank'" :styles="drag.styles"></BlankBlock>
				<!-- 辅助线 -->
				<LineBlock v-else-if="drag.name === 'lines'" :styles="drag.styles"></LineBlock>
				<!-- 富文本 -->
				<RichText v-else-if="drag.name === 'book'" :styles="drag.styles"></RichText>
				<!-- 热区 -->
				<HotBlock v-else-if="drag.name === 'hot'" :styles="drag.styles"></HotBlock>
				<!-- 魔方 -->
				<MargicBlock v-else-if="drag.name === 'margic'" :styles="drag.styles"></MargicBlock>
				<!-- 按钮组  -->
				<ButtonGroup v-else-if="drag.name === 'btn'" :styles="drag.styles"></ButtonGroup>
				<!-- 公告  -->
				<Notice v-else-if="drag.name === 'notice'" :styles="drag.styles"></Notice>
				<!-- 告示  placard -->
				<!-- <placard></placard> -->
				<!-- 同城头条  Bulletin 列表导航 -->
				<Bulletin v-else-if="drag.name === 'listNav'" :styles="drag.styles"></Bulletin>
				<!-- 大牌臻选 -->
				<StoreSwiper v-else-if="drag.name === 'storeName'"  ref="storeswiper" :styles="drag.styles"></StoreSwiper>
				<!-- 推荐商店  -->
				<RecommendGoods v-else-if="drag.name === 'recommendGoods'" ref="recommendGoods" :styles="drag.styles"></RecommendGoods>
				<!-- 特价  -->
				<RecommendStore v-else-if="drag.name === 'recommend'" ref="recommend" :styles="drag.styles"></RecommendStore>
				<!-- 商店列表  -->
				<StoreList v-else-if="drag.name === 'storelist'" 
				id="storelist" ref="storelist"
				:styles="drag.styles" @changeTop="handleChangeTop"
				:paddingTop="navBarHeight" :storeListDot="storeListDot" 
				:storeListDotTop="storeListDotTop" :handleScroll="handleScroll"></StoreList>
				<!-- 商店列表  -->
			</block>
		</view>
	<!-- </scroll-view> -->
	<footc></footc>
	</mescroll-uni>
</template>

<script>
	import {
		mapState,
	} from 'vuex'
	import Search from "./drag/Search.vue"
	import CustomSwiper from "./drag/CustomSwiper.vue"
	import Notice from "./drag/Notice.vue"
	import Placard from "./drag/Placard.vue"
	import Bulletin from "./drag/Bulletin.vue"
	import Position from "./drag/Position.vue"
	import RecommendStore from "./drag/RecommendStore.vue"
	import ButtonGroup from "./drag/ButtonGroup.vue"
	import StoreList from "./drag/StoreList.vue"
	import StoreSwiper from "./drag/StoreSwiper.vue"
	import ImageGroup from "./drag/ImageGroup.vue"
	import TitleBlock from "./drag/TitleBlock.vue"
	import BlankBlock from "./drag/BlankBlock.vue"
	import LineBlock from "./drag/LineBlock.vue"
	import RichText from "./drag/RichText.vue"
	import HotBlock from "./drag/HotBlock.vue"
	import MargicBlock from "./drag/MargicBlock.vue"
	import AddMyMiniApp from "./drag/AddMyMiniApp.vue"
	import SuspensionButton from "./drag/SuspensionButton.vue"
	import footc from '@/components/common/footc.vue'
	import RecommendGoods from "./drag/RecommendGoods.vue"
	
	export default {
		components:{
			Search,CustomSwiper,Notice,Placard,Bulletin,RecommendStore,ButtonGroup,StoreList,Position,StoreSwiper,ImageGroup,
			TitleBlock,BlankBlock,LineBlock,RichText,HotBlock,MargicBlock,AddMyMiniApp,SuspensionButton,footc,RecommendGoods
		},
		data() {
			return {
				isInit:false,//是否初始化过页面
				refreshLoading:false,
				changeTop:0,
				custom:true,
				positionFixedDot:0,
				positionColorOpacity:1,
				handleScroll:-1,
				storeListDot:0, //storeList 改吸顶定位的临界点
				storeListDotTop:0 //改吸顶后的定位的top = <position>的top+height
			}
		},
		computed:{
			...mapState({
				height:state=>state.systemInfo.MainPageHeight,
				layout:state=>state.layout.index.body
			}),
			downOffset(){
				return 160
			},
			customeColor(){
				if(!this.layout)return ''
				let drag = this.layout.list.find(drag=>drag.name === 'fixed')
				if(!drag)return ''
				return drag.styles.colorBg || this.tColor
			},
			navBarHeight(){
				if(this.custom){
					return this.menuButtonTop
				}else{
					return this.statusNavBarHeight
				}
			}
		},
		watch:{
			layout(){
			
			}
		},
		mounted() {
			this.$nextTick(()=>{
				//100 是LoadingIcon的高度
				this.$refs.mescroll.mescroll.optDown.offset = this.menuButtonTop + 100/this.pxToRpxRate
			})
		},
		methods:{
			async hasRefs(ref,fn){
				if(this.$refs[ref]){
					await this.$refs[ref][0][fn]()
				}
			},
			// 初始化组件内 网络请求 然后计算getPositionTop定位位置 
			async init(){
				if(this.isInit)return
				await this.hasRefs('storeswiper','fetchData')
				await this.hasRefs('recommend','fetchData')
				await this.hasRefs('recommendGoods','fetchData')
				await this.hasRefs('storelist','init')
				// APP端需要$nextTick
				this.$nextTick(()=>{
					this.$nextTick(()=>{
						this.$nextTick(()=>{
							this.getPositionTop()
						})
					})
				})
				this.isInit = true
				
			},
			async refresh(e){
				await this.hasRefs('storeswiper','fetchData')
				await this.hasRefs('recommend','fetchData')
				await this.hasRefs('recommendGoods','fetchData')
				await this.hasRefs('storelist','init')
				setTimeout(()=>{
					e.endBySize()
				},500)
			},
			handleChangeTop(val){
				// 切换tab重新定位 -1减小rpx=>px的误差
				if( this.handleScroll < this.storeListDot){
					this.changeTop = this.handleScroll
				}else if(val > this.storeListDot){
					this.changeTop = val
					if(this.changeTop === val){
						this.changeTop = val-1
					}
				}else if(val <= 0){
					this.changeTop = this.storeListDot
				}
			},
			storeListChangeFixedDot(e){
				this.storeListDotTop = e
			},
			listeningScroll(e){
				let scrollTop = e.scrollTop
				this.handleScroll = scrollTop
				this.positionColorOpacity = (5 / scrollTop).toFixed(2) > 1 ? 1: (5 / scrollTop).toFixed(2)<0?1:(5 / scrollTop).toFixed(2)
			},
			onreachBottom(e){
				this.hasRefs('storelist','nextPage')
				e.endBySize()
			},
			getPositionTop(){
				// 由于拖拽式 需要接口请求所以要等网页渲染完毕在执行
				let position = uni.createSelectorQuery().in(this);
				// if(position.select('#position1'))
				position.select('#position').boundingClientRect().exec((res) => {
					if(res[0]){
						this.positionFixedDot = (res[0].top)|| 0
					}
				});
				let storelist = uni.createSelectorQuery().in(this);
				storelist.select('#storelist').boundingClientRect().exec((res) => {
					if(res[0]){
						this.storeListDot = (res[0].top) || 0
					}
				});
			}
		}
	}
</script>
<style lang="scss" scoped>
	.banner-swiper {
	   height: 600rpx;
	}
	.container {
		padding: 20rpx 60rpx;
	}

	.userinfo {
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.userinfo-avatar {
		width: 128rpx;
		height: 128rpx;
		margin: 20rpx;
		border-radius: 50%;
	}
	.lists {
		margin: 10px 0;
	}
	.ceiling{
		position: fixed;
		top: 0;
		left:0;
		height: 120rpx;
		background: #FFF;
		animation: wid750 .1s ease-in-out forwards;
		z-index: 9;
		@keyframes wid750 {
			0% {
				width: 700rpx;
			}
			100% {
				width: 750rpx;
			}
		}
		.input{
			height: 60rpx;
			margin-left: 25rpx;
			background: #FFF;
			display: flex;
			align-items: center;
			border-radius: 30rpx;
			border: 1px solid #ffd921;
			.btn{
				height: 60rpx;
				width: 90rpx;
				text-align: center;
				margin-left: auto;
				line-height: 60rpx;
				border-radius: 30rpx;
				background-color: #ffd921;
			}
		}
	}
</style>
