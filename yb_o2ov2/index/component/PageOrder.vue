<template>
	<view class="wh h90">
		<!-- 搜索框 -->
		<view class="p-f" style="top:0;width: 100%;z-index: 3;">
			<view class="flex bf":style="{height:`${statusNavBarHeight}px`,paddingTop:`${menuButtonTop}px`,background:tColor,color:fontColor}">
				<!-- <text class="iconfont iconsearch f42 ml20" style="line-height: 60rpx;"></text> -->
					<!-- <text class="mla wei f32">我的订单</text> -->
					<view class="oi1c be f-y-c bs15 mla">
						<block v-for="(v,i) in tarr" :key='i'>
							<view  @click="tClick(i)" class="item f-c bs10 f26 wei" :class="{bf:i==taIdx}" :style="{background:i==taIdx?tColor:''}">
								{{v.name}}
							</view>
						</block>
					</view>
			</view>
		</view>
		<!-- 	<view class="p-f" style="top:0;width: 100%;z-index: 3;">
		<view class="flex"
				:style="{height:`${statusNavBarHeight}px`,paddingTop:`${menuButtonTop}px`,background:tColor,color:fontColor}">
				<text class="iconfont iconremind f42 ml20" style="line-height: 60rpx;"></text>
				<view class="search bf5 f-y-c c3">
					<text class="iconfont iconsearch f28 mr10 c9"></text>
					<input type="text" :disabled="false" style="width: 400rpx" v-model="params.keyword" @input="searchM" placeholder="请输入订单中商店或商品名称" />
				</view>
			</view> 
			</view>-->
		<view class="tabNav" :style="{paddingTop:`${statusNavBarHeight}px`}">
			<tab-nav v-if="taIdx==0" gutter="80" :height="tabNavHeight" :activeColor="tColor" fontSize="30" inactiveTextColor="#666"
				:current-index="current" :list="tabs" @change="tabsChange" :isScroll="false"></tab-nav>
				<view v-else-if="taIdx==1&&tntabs.length" class="f-row bf">
					<view class="f-1 b-r-d">
						<!-- <nav-tab cname="" ifsize='32' v-model="aIdx" @changeTab='tabsChange' :tabs='tntabs'></nav-tab> -->
						<tab-nav gutter="80" :height="tabNavHeight" :activeColor="tColor" fontSize="30" inactiveTextColor="#666"
							:current-index="aIdx" :list="tntabs" @change="tabsChange" :isScroll="false"></tab-nav>
					</view>
				</view>
		</view>
		<!-- tabNavHeight是tab-nav的高度 如果给tab-nav传高，需要进行rpx px的转化 -->
		<scroll-view scroll-y="true" class="wh bf5 h90"
			refresher-enabled refresher-default-style="none"
			:refresher-threshold="100/pxToRpxRate" :refresher-triggered="refreshLoading" @refresherrefresh="refresh"
			@scrolltolower="nextPage">
			<!-- :style="{paddingTop:`${statusNavBarHeight+tabNavHeight/pxToRpxRate}px`}" -->
			<view class="p-a p-a-xc" style="top: -100rpx;">
				<loading-icon></loading-icon>
			</view>
			<view class="pt20" :style="{background:bgColor}"  v-if="taIdx==0">
				<view v-for="(item,index) in list" :key="item.id">
					<OrderBlock :orderInfo="item" @operation="operation"></OrderBlock>
				</view>
			</view>
			<view class="p2" :style="{background:bgColor}"  v-else>
				<dn-order @refresh='dnrefresh' :ltop='toph' v-model='laIdx' :labelarr='labelArr'
					:otype="tntabs[aIdx].type" :datalist="list"></dn-order>
			</view>
			<!-- 空布局 -->
			<mescroll-empty v-if="list.length===0" :option="{icon:'/static/empty/9.png',tip:'~ 暂无订单 ~'}"></mescroll-empty>
			<u-loadmore v-if="list.length!==0" @loadmore="nextPage" :status="status" />
			<footc></footc>
		</scroll-view>

	</view>
</template>

<script>
	import {
		mapState,
		mapActions
	} from 'vuex'
	import utils from '@/common/utils.js'
	import TabNav from '@/components/TabNav.vue'
	import OrderBlock from '@/components/OrderBlock.vue'
	import LoadingIcon from '@/components/LoadingIcon.vue'
	import footc from '@/components/common/footc.vue'
	import navTab from '@/components/common/nav-tab.vue'
	import dnOrder from '@/yb_o2ov2/index/component/dn-order.vue'
	export default {
		components: {
			TabNav,
			OrderBlock,
			LoadingIcon,
			footc,
			navTab,
			dnOrder,
		},
		data() {
			return {
				refreshLoading:false,
				status:'loading',
				tabNavHeight: 90,
				params: {
					page: 1,
					size: 10,
					type: '1' ,//1全部 2待评价 3退款
					keyword:'',
				},
				list: [],
				bgColor: '#f5f5f5',
				current: 0,
				tabs: [{
					name: '全部',
					value: 1
				}, {
					name: '待评价',
					value: 2
				}, {
					name: '退款/售后',
					value: 3
				}],
				orderInfo: {},
				toph: 0,
				taIdx: 0,
				aIdx: 0,
				laIdx: 0,
				labelArr: [],
				tntabs: [
					{
						name: '商店',
						type: 1,
					},
				// 	{
				// 	name: '快餐',
				// 	type: 4,
				// }, 
				{
					name: '当面付',
					type: 2,
				},
				],
			}
		},
		computed: {
			...mapState({
				orderSet: state => state.config.orderSet,
			}),
			tarr() {
				return [{
					// show: this.system.powerList.takeout == 1,
					name: this.system.custom && this.system.custom.outName + '订单',
					type: 1,
				}, {
					// show: this.system.powerList.instore == 1,
					name: this.system.custom && this.system.custom.selfName + '订单',
					type: 2,
				}]
			},
		},
		methods: {
			tClick(i) {
				if (i == this.taIdx) return
				this.taIdx = i
				this.aIdx = 0
				this.tabsChange()
				this.refresh()
			},
			dnrefresh(e) {
				if (e.hasOwnProperty('state')) {
					this.iparams.state = e.state
					this.isget = this.mygd = false
					this.params.page = 1
					this.list = []
					this.fetchData()
				} else {
					this.refresh();
				}
			},
			tabsChange(index){
				this.params.type = index + 1
				this.aIdx = index
				if (this.taIdx == 0) {
					// let type = ''
					// switch (index) {
					// 	case 0:
					// 		type = '1';
					// 		break;
					// 	case 1:
					// 		type = '2';
					// 		break;
					// }
					this.params.api = 'ddlb'
					this.iparams = index + 1
				} else {
					if(!index){
						index = 0
						this.aIdx = 0
					}
					switch (this.tntabs[index].type) {
						case 1:
							this.labelArr = ['全部', '待付款', '已支付', '已关闭']
							this.params.api = 'tsdd'
							this.iparams = {
								state: ''
							}
							break;
						case 2:
							this.labelArr = []
							this.params.api = 'dmlb'
							this.iparams = {}
							break;
						case 4:
							this.labelArr = ['全部', '制作中', '已完成']
							this.params.api = 'kclb'
							this.iparams = {
								state: '',
							}
							break;
						case 3:
							this.labelArr = []
							this.params.api = 'wdyy'
							this.iparams = {}
							break;
						case 5:
							this.labelArr = []
							this.params.api = 'pdlb'
							this.iparams = {}
							break;
					}
				}
				
				this.refresh()
			},
			async searchM(){
				this.fetchData()
			},
			async init() {
				let Scurrent = uni.getStorageSync('current')
				this.taIdx = 0
				this.params.api = 'ddlb'
				if(Scurrent>=0){
					this.current = Scurrent
					this.params.type = Scurrent + 1
					this.params.page = 1
					this.fetchData()
				}else{
					this.params.page = 1
					this.fetchData()
				}
			},
			async refresh() {
				this.refreshLoading = true
				this.params.page = 1
				await this.fetchData()
				setTimeout(()=>{
					this.refreshLoading = false
				},500)
			},
			async fetchData(type) {
				console.log(this.params.api)
				this.status = 'loading'
				let { data } = await this.util.request({
					// 'url': this.api.orderLis,
					'url': this.api[this.params.api],
					method: 'POST',
					// data: this.params
					data: {
						...this.params,
						...this.iparams
					},
				})
				if(type !=='nextPage' || this.params.page === 1){
					this.list = data
				}else{
					if(data.length === 0){
						this.status = 'nomore'
						return
					}
					this.list = this.list.concat(data)
				}
				uni.setStorageSync('current', -1)
				this.status = 'loadmore'
			},
			nextPage() {
				if (this.status === 'loading') { return }
				this.params.page++
				this.fetchData('nextPage')
			},
			async operation(e) {
				this.orderInfo = e.data
				let operationt = e.type
				let obj = {}
				switch (operationt) {
					// case 'cancelOrder':
					// 	if (e.tip) {
					// 		return this.qxdd(1)
					// 		this.yyradio = ''
					// 		this.yyArr = this.qxyyArr
					// 		this.showCancel = true
					// 		return;
					// 	} else {
					// 		return this.qxdd()
					// 	}
						case 'applyRefund':
							if (this.orderInfo.state == 2) {
								return this.qxdd(1)
							}
							obj = {
								title: this.$t('good.confirm_tip'),
								url: 'wmddtk',
								params: {
									orderId: this.orderInfo.id,
									note: this.qxyy || '',
								}
							}
							break;
						case 'reminder':
							obj = {
								title: this.$t('good.follow_tip'),
								url: 'wmddcd',
								params: {
									userId: this.user.userId,
									orderId: this.orderInfo.id
								}
							}
							break;
						case 'confirmReceipt':
							obj = {
								title: this.$t('good.received_tip'),
								url: 'wmddsh',
								params: {
									orderId: this.orderInfo.id
								}
							}
							break;
						case 'deleteOrder':
							obj = {
								title: this.$t('good.delet_tip'),
								url: 'wmddsc',
								params: {
									orderId: this.orderInfo.id
								}
							}
							break;
						case 'contactStore':
							return this.util.makeTel(this.orderInfo.storeTel)
						case 'evaluate':
							return this.go('navigateTo', `/yb_o2ov2/order/edit-comment?orderInfo=
						${encodeURIComponent(JSON.stringify({
							orderId:this.orderInfo.id,
							storeName:this.orderInfo.storeName,
							storeIcon:this.orderInfo.storeIcon
						}))}`)
						case 'payTo':
							let data = {}
							data.orderId = this.orderInfo.id
							data.orderType = 1,
							//支付前需要保存支付信息传给支付页面
							this.setPayInfo(data)
							this.go('navigateTo', '/yb_o2ov2/home/<USER>/index')
							return;
						case 'againOrder':
						return this.go('navigateTo',`/yb_o2ov2/home/<USER>
				}
				try {
					await this.util.modal(obj.title)
				} catch (e) {
					return
				}
				let data = await this.util.request({
					'url': this.api[obj.url],
					method: 'POST',
					mask: 1,
					data: obj.params,
				})
				if (data) {
					this.refresh();
					this.util.message('操作成功', 1)
				}
			},
			async qxdd(e) {
				if (e) {
					try {
						await this.util.modal('您确认取消订单吗？')
					} catch (e) {
						return
					}
				}
				let data = await this.util.request({
					'url': this.api.wmddqx,
					method: 'POST',
					mask: '取消订单中',
					data: {
						orderId: this.orderInfo.id,
						note: e ? this.qxyy || '' : '',
					},
				})
				if (data) {
					this.refresh();
					this.util.message('操作成功', 1)
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.search {
		width: 460rpx;
		height: 60rpx;
		margin-left: 20rpx;
		border-radius: 60rpx;
		line-height: 60rpx;
		padding: 10rpx 20rpx;
	}

	.order-card {
		width: 700rpx;
		margin: 0 auto;
		padding: 20rpx;
		border-radius: 20rpx;
		background: #FFF;
		margin-bottom: 20rpx;
	}
	.oi1c {
		height: 60rpx;
		padding: 0 6rpx;
	
		.item {
			padding: 0 10rpx;
			min-width: 152rpx;
			height: 56rpx;
		}
	}
	
	.oiqdd {
		width: 360rpx;
		height: 88rpx;
		margin-top: 40rpx;
	}
	.h90{
		height: 90%;
	}
</style>
