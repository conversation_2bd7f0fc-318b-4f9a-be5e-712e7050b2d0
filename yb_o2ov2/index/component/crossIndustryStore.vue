<template>
	<view class="shop-item">
		<image class="shop-img" :src="item.icon"></image>
		<view class="info">
			<view class="name">{{item.name}}</view>
			<view class="button">
				<text>{{$t('home.buy_in_store')}}</text>
				<view class="label">{{$t('home.from_you')}}{{item.distance}}</view>
			</view>
		</view>
	</view>
</template>

<script>
	import utils from '@/common/utils.js'
	export default {
		props: {
			item: {
				type: Object,
				default: () => {}
			}
		},
		data() {
			return {}
		},
		methods: {

		}
	}
</script>

<style lang="scss" scoped>
	.shop-item {
		display: flex;
		justify-content: flex-start;
		align-items: center;
		margin-top: 20rpx;

		&:first-child {
			margin-top: 0;
		}

		.shop-img {
			display: flex;
			width: 190rpx;
			height: 190rpx;
			border-radius: 20rpx;
			object-fit: contain;
			flex-shrink: 0;
		}

		.info {
			display: flex;
			height: 190rpx;
			flex-direction: column;
			justify-content: space-around;
			margin-left: 26rpx;

			.name {
				font-weight: 600;
				font-size: 28rpx;
				color: #333333;
				line-height: 33rpx;
			}

			.button {
				display: flex;
				align-items: center;

				.label {
					margin-left: 10rpx;
				}
			}
		}
	}
</style>