<template>
	<view class="posi-f" :style="(styles.type==='right'?'right:0;':'left:0;') + `bottom:${styles.shifting/pxToRpxRate}px`">
		<view style="width: 100rpx;height: 100rpx;" @click="goUrl(styles.imgUrl[0].url)">
			<image class="wh" :src="styles.imgUrl[0].img" mode=""></image>
		</view>
	</view>
</template>

<script>
	export default {
		props:{
			styles:{
				type:Object,
				default:()=>{}
			}
		},
		data() {
			return {
			}
		},
		methods: {
		},
	}
</script>

<style>
</style>
