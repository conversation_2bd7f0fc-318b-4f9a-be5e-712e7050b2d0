<template>
	<view class="btn bf bs20"
	:style="{
		marginTop:`${styles.marginTop}px`,
		marginBottom:`${styles.marginBottom}px`,
		marginLeft:`${styles.marginLR}px`,
		marginRight:`${styles.marginLR}px`,
		backgroundColor:`${styles.colorBg}`,
		}"
	>
		<u-swiper :height="height" custom :list="swiperPages" :mode="swiperPages>1?'round':'none'" bg-color="transparent" :autoplay="false">
			<template v-for="(item,index) in swiperPages" :slot="'element-'+index">
				<view :key="index" class="f-y-c f-w" >
					<view v-for="btn in btnList(index)" :key="btn.img" class="f-c-c mt20" :style="{width:`${itemWidth}rpx`}"
					 @click="goUrl(btn.url)">
						<view :style="{width:`${styles.btnSize|| 20}px`,height:`${styles.btnSize || 20}px`,borderRadius: styles.circleBtn + '%'}" class="p-r">
							<image class="wh" :src="btn.img"  mode="" :style="{borderRadius: styles.circleBtn + '%'}"></image>
							<view v-if="btn.labelWord" class="p-a labelN t-c" :style="{color:`${btn.colorLabelWord}`,backgroundColor:`${btn.colorLabelBg}`}">{{btn.labelWord}}</view>
						</view>
						<text class="c3 f24" :style="{color:`${styles.colorWord}`}">{{btn.word}}</text>
					</view>
				</view>
			</template>
		</u-swiper>
	</view>
</template>

<script>
	export default {
		props:{
			styles:{
				type:Object,
				default:()=>{}
			},
		},
		data(){
			return {
			
			}
		},
		computed:{
			height(){
				return (115+this.styles.btnSize)*this.styles.line
			},
			itemWidth(){
				return (750- 2 * this.styles.marginLR * this.pxToRpxRate)/this.styles.num
			},
			pageItems(){
				return this.styles.num*this.styles.line
			},
			swiperPages(){
				return Math.ceil(this.styles.btnList.length/this.pageItems)
			},
			btnList(){
				return (index) => {
					let start = index * this.pageItems
					let end = start + this.pageItems
					return this.styles.btnList.slice(start,end)
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	.labelN{
		top: -15rpx;
		left: 13rpx;
		width: 108rpx;
		height: 54rpx;
		line-height: 54rpx;
		transform: scale(.5);
		color: #fff;
		font-size: 32rpx;
		background: #f83287;
		border-radius: 24rpx 24rpx 24rpx 0;
		border: 1rpx solid #fff;
	}
</style>
