<template>
	<view 
	:style="{
		marginTop:`${styles.marginTop}px`,
		marginBottom:`${styles.marginBottom}px`,
		marginLeft:`${styles.marginLR}px`,
		marginRight:`${styles.marginLR}px`,
		borderRadius:`${styles.circle}px`,
		}">
	<RichText :text="styles.html"></RichText>
	</view>
</template>

<script>
	import RichText from "@/components/RichText.vue"
	export default {
		components:{
			RichText
		},
		props:{
			styles:{
				type:Object,
				default:()=>{}
			}
		},
		data() {
			return {
			}
		},
		methods: {
			
		},
	}
</script>

<style>
</style>
