<template>
	<view :style="{
		background:styles.colorBgLayer,
		marginTop:`${styles.marginTop}px`,
		marginBottom:`${styles.marginBottom}px`,
		marginLeft:`${styles.marginLR}px`,
		marginRight:`${styles.marginLR}px`,
		}">
		<view :style="{background:styles.colorBg,borderRadius:`${styles.assemblyCircle}px ${styles.assemblyCircle}px 0 0`}">
			<view style="width: 95%;margin: 0 auto;padding: 30rpx 0;">
				<view  class="search"  @click="go('navigateTo','/yb_o2ov2/home/<USER>')">
					<view class="input" :style="{borderColor:styles.colorFrame,background:styles.colorInput,borderRadius:styles.circle}">
						<text class="iconfont iconsearch f28 ml20" :style="{color:styles.colorWord}"></text>
						<text class="ml10" :style="{color:styles.colorWord}">{{styles.text}}</text>
						<text v-if="styles.btnOpen==='1'" class="btn" :style="{background:styles.colorBtnBg,color:styles.colorBtnWord,borderRadius:styles.circle}">搜索</text>
					</view>
				</view>
				<view class="f-y-c mt20 o-x-s" v-if="styles.keyword">
					<text @click="toSearch(word)" :style="{color:styles.colorKeyword,background:styles.colorBgKeyword}" class="recommend" v-for="word in styles.keyword.split(',')" :key="word">{{word}}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props:{
			styles:{
				type:Object,
				default:()=>{}
			}
		},
		data() {
			return {
			}
		},
		methods: {
			toSearch(word){
				this.go('navigateTo',`/yb_o2ov2/home/<USER>
			}
		},
	}
</script>

<style scoped lang="scss">
	.search{
		height: 60rpx;
		.input{
			display: flex;
			align-items: center;
			border-radius: 30rpx;
			border: 1px solid #ffd921;
			.btn{
				height: 60rpx;
				width: 110rpx;
				text-align: center;
				margin-left: auto;
				line-height: 60rpx;
				border-radius: 30rpx;
				background-color: #ffd921;
			}
		}
	}
	.recommend{
		background: #f6f7f9;
		padding: 5rpx 10rpx;
		border-radius: 20rpx;
		font-size: 24rpx;
		margin-right: 20rpx;
		flex-shrink: 0;
	}
</style>
