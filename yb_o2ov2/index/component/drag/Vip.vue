<template>
	<view class="user-info">
		<view class="f-x-bt p2">
			<view class="f-raw">
				<!-- 用户avatar -->
				<view class="avatar-box bsf mr30">
					<image class="wh" :src="user.portrait || '/static/no.png'" mode=""></image>
				</view>
				<view class="f36 wei f-col">
					<view v-if="user.nickname">
						<view>{{(user.nickname)}}
							<!-- <image class="user-right-img" style="margin-left: 16rpx;" :src="user.level.icon" mode="">
							</image> -->
						</view>
						<view class="f26 wei" v-if="user.userTel">
							{{user.userTel.replace(/(\d{3})\d{4}(\d{4})/,'$1****$2')}}
						</view>
					</view>
					<view v-else class="f36 wei" @click="go('navigateTo','/yb_o2ov2/my/login')">{{$t('my.login_but')}}
					</view>
				</view>
			</view>
			<view class="f-raw">
				<!-- 用户条形码 -->
				<view class="code" style="width: 60rpx;height: 60rpx;" @click="handleCode">
					<image class="img" src="/static/qrcode.png"></image>
				</view>
				<!-- 用户设置 -->
				<view class="code" @click="go('navigateTo',user.nickname?'/yb_o2ov2/my/user/user-info':'/yb_o2ov2/my/login')">
					<image class="img" src="/static/icon_commone_seting.png"></image>
				</view>
			</view>
		</view>
		<view>
			<view class="card">
				<view class="f-x-c-sa">
					<view class="f-c-c mt20">
						<view class="f-y-c f36">
							<text class="wei">{{user.ucoin || 0.00}}</text>
						</view>
						<view>
							<text class="">{{$t('my.u_coin_balance')}}</text>
						</view>
					</view>
					<view class="f-c-c mt20" @click="go('navigateTo','/yb_o2ov2/my/uCoinhistorical/index')">
						<text class="">{{$t('my.u_coin_history')}}</text>
					</view>
				</view>
			</view>
		</view>

		<u-modal v-model="isModal" :showTitle="false" :showConfirmButton="false" :maskCloseAble="true">
			<slot>
				<view class="qrcode-container">
					<view class="loading-container">
						<u-loading mode="circle" size="48"></u-loading>
					</view>
					<canvas canvas-id="qrcode" class="qrcode-canvas"></canvas>
				</view>
			</slot>
		</u-modal>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				isModal: false
			}
		},
		computed: {
			qrCodeValue() {
				if (!this.user || !this.user.id) return ''
				return 'youliwangH5_userId'+this.user.id
			}
		},
		methods: {
			handleCode() {
				this.isModal = true
				this.$nextTick(() => {
					this.generateQRCode()
				})
			},
			generateQRCode() {
				const canvas = uni.createCanvasContext('qrcode', this)
				const size = 200
				
				// 使用 uni-app 的 API 生成二维码
				uni.request({
					url: 'https://api.qrserver.com/v1/create-qr-code/',
					data: {
						data: this.qrCodeValue,
						size: size + 'x' + size,
						margin: 0
					},
					responseType: 'arraybuffer',
					success: (res) => {
						const base64 = uni.arrayBufferToBase64(res.data)
						const imgPath = 'data:image/png;base64,' + base64
						
						// 绘制二维码
						canvas.drawImage(imgPath, 0, 0, size, size)
						canvas.draw()
					},
					fail: (err) => {
						console.error('生成二维码失败:', err)
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.user-info {
		background-color: #FFCC00;
		margin-bottom: 40rpx;
		padding-bottom: 40rpx;
	}

	.avatar-box {
		width: 100rpx;
		height: 100rpx;
	}

	.code {
		width: 48rpx;
		height: 48rpx;
		margin-right: 20rpx;

		.img {
			width: 100%;
			height: 100%;
			object-fit: contain;
		}
	}

	.card {
		border-radius: 20rpx;
		// padding: 20rpx;
		// margin-top:20rpx ;
	}

	.title {
		font-size: 28rpx;
		font-weight: bold;
	}

	.user-right-img {
		width: 100rpx;
		height: 30rpx;
	}

	.qrcode-container {
		padding: 40rpx 20rpx;
		background: #fff;
		border-radius: 10rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		position: relative;
	}

	.loading-container {
		position: absolute;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		height: 200px;
		width: 200px;
		z-index: 1;
	}

	.loading-text {
		margin-top: 10px;
		font-size: 14px;
		color: #666;
	}

	.qrcode-canvas {
		width: 200px;
		height: 200px;
		position: relative;
		z-index: 2;
	}
</style>