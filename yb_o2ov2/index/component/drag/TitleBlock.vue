<template>
	<view :style="{
		background:styles.colorBg,
		marginTop:`${styles.marginTop}px`,
		marginBottom:`${styles.marginBottom}px`,
		marginLeft:`${styles.marginLR}px`,
		marginRight:`${styles.marginLR}px`,
		borderRadius:`${styles.circle}px`,
		}">
		<view class="w100 p-r" style="height: 80rpx;">
			<view class="h100 f-y-c"
				:style="styles.wordStatus==='center'?'justify-content: center':'padding-left:20rpx'">
				<view class="mr10" style="width: 50rpx;height: 50rpx;">
					<image class="wh" :src="styles.upLoad1.img" mode="aspectFill"></image>
				</view>
				<view :style="{color:styles.upLoad1.colorWord,fontSize:`${styles.upLoad1.fontSize*pxToRpxRate}rpx`}" class=" wei" @click="goUrl(styles.upLoad1.url)">
					{{styles.upLoad1.text}}</view>
			</view>
			<view class="p-a p-a-yc" style="right: 20rpx;" :style="{color:styles.upLoad2.colorWord}"
				@click="goUrl(styles.upLoad2.url)">{{styles.upLoad2.text}}</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			styles: {
				type: Object,
				default: () => {}
			}
		},
		data() {
			return {}
		},
		methods: {

		},
	}
</script>

<style>
</style>
