<template>
	<view class="p03">
		<!-- 我的功能 -->
		<view class="card">
			<view class="title">{{$t('my.my_action')}}</view>
			<view class="f-raw">
				<block v-for="(item,index) in btnList" :key="index">
					<view class="btn-item" @click="go('navigateTo',item.url)">
						<image class="btn-item-img" :src="item.img" mode="aspectFill"></image>
						<view class="btn-item-label">{{item.label}}</view>
					</view>
				</block>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				btnList: [{
					img: require('@/static/my/issue-an-invoice.png'),
					label: this.$t('my.issue_an_invoice'),
					url: "/yb_o2ov2/my/issueAnInvoice/index"
				}, {
					img: require('@/static/my/receiving-address.png'),
					label: this.$t('my.receiving_address'),
					url: "/yb_o2ov2/my/address/index"
				}, {
					img: require('@/static/my/invoice-header.png'),
					label: this.$t('my.invoice_header'),
					url: "/yb_o2ov2/my/issueAnInvoice/index"
				}, {
					img: require('@/static/my/order-evaluation.png'),
					label: this.$t('my.order_evaluation'),
					url: "/yb_o2ov2/my/evaluate/index"
				},{
					img: require('@/static/my/food-procurement.png'),
					label: this.$t('my.food_procurement'),
					url: "/yb_o2ov2/my/supplier/index"
				}]
			}
		},
		methods: {

		},
	}
</script>

<style lang="scss" scoped>
	.btn-item {
		display: flex;
		width: 130rpx;
		height: 116rpx;
		justify-content: center;
		align-items: center;
		flex-direction: column;
		margin-top: 30rpx;
		flex-shrink: 0;

		.btn-item-img {
			width: 68rpx;
			height: 68rpx;
		}

		.btn-item-label {
			font-weight: 400;
			font-size: 26rpx;
			color: #555555;
			line-height: 44rpx;
		}
	}

	.card {
		background: #ffffff;
		border-radius: 20rpx;
		padding: 20rpx;
		margin-top: 20rpx;
	}

	.title {
		font-size: 30rpx;
		font-weight: bold;
	}
</style>