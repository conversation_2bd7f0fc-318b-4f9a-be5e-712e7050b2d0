<template>
	<view :style="{backgroundColor:`rgba(${bgColor})`,position:'relative'}">
		<view class="f-y-c mb40 p02" style="height: 44rpx;padding-top: 16rpx;" @click="go('navigateTo','/yb_o2ov2/home/<USER>/index')">
			<block v-if="styles.module!='2'">
				<text v-if="styles.positionModule !=='4'" class="iconfont iconposition f28" :style="{color:styles.colorWord}"></text>
				<text v-if="storeSet && storeSet.showType==1" class="mr20 f30 t-o-e t-l wei" style="margin-top: -5rpx;" :style="{color:styles.colorWord}" @click="go('navigateTo','/yb_o2ov2/home/<USER>/area')" >
					<text>{{regionId.name || '区域名称'}}</text>
					<text class="iconfont iconinto f28 ml10 nowei" :style="{color:styles.colorWord}"></text>
				</text>
				<text class="wei f30 t-o-e t-l" style="max-width: 480rpx;margin-top: -5rpx;" :style="{color:styles.colorWord}">
					<text v-if="styles.positionModule ==='1'">{{addInfo.maddress}}</text>
					<text v-if="styles.positionModule ==='2'">{{addInfo.city}}</text>
					<text v-if="styles.positionModule ==='4'"></text>
				</text>
				<text v-if="styles.positionModule !=='4'" class="iconfont iconinto f28 ml10" :style="{color:styles.colorWord}"></text>
				<!-- <text class="iconfont iconremind f28" style="margin-left: auto;"></text> -->
			</block>
			<view :style="{color:styles.colorWord}" v-if="styles.module==='2'" class="f40 wei">{{systemObj.name}}</view>
		</view>
		<view class="positionSearch" :style="fixed?positionFixed.fixed:'position:absolute;top:100rpx;width:100%'">
			<view class="f-y-c mb40 p02" style="height: 44rpx;padding-top: 16rpx;" @click="go('navigateTo','/yb_o2ov2/home/<USER>/index')" v-if="styles.module==='2' && !fixed">
				<view class="f-bt f-c f-g-1">
					<view class="t-o-e f-g-1 f-y-c">
						<text v-if="styles.positionModule !=='4'" class="iconfont iconposition f32" style="color:#333;"></text>
						<text v-if="storeSet && storeSet.showType==1" class="f32 t-l mr10" style="margin-top: -5rpx;color:#333"  @click="go('navigateTo','/yb_o2ov2/home/<USER>/area')" >
							<text>{{regionId.name || '区域名称'}}</text>
						</text>
						<text class="f32 t-o-e t-l" style="max-width: 350rpx;margin-top: -5rpx;color:#333">
							<text v-if="styles.positionModule ==='1'">{{addInfo.maddress}}</text>
							<text v-if="styles.positionModule ==='2'">{{addInfo.city}}</text>
							<text v-if="styles.positionModule ==='4'"></text>
							
						</text>
						<text>></text>
						<!-- <text v-if="styles.positionModule !=='4'" class="iconfont iconinto f28 ml10" :style="{color:styles.colorWord}"></text> -->
						<!-- <text class="iconfont iconremind f28" style="margin-left: auto;"></text> -->
					</view>
					<view class="c9 f24 f-g-0 ml30">{{styles.fixedSearch.text2}}</view>
				</view>
			</view>
			<view class="bs30-nh" style="background:#FFF;" >
				<!-- 搜索 -->
				<view  class="search"
				:style="fixed?`width:${700/pxToRpxRate - menuButtonWidth}px;margin-left:20rpx`:''"
				 @click="go('navigateTo','/yb_o2ov2/home/<USER>')">
					<view class="input" :style="{borderColor:styles.colorBg}">
						<text class="iconfont iconsearch f28 ml20 cd"></text>
						<text class="ml10 c9">{{styles.fixedSearch.text}}</text>
						<text class="btn" v-if="!fixed" :style="{color:styles.colorWord,backgroundColor:styles.colorBg}">搜索</text>
					</view>
				</view>
			</view>
			<view class="bg-card2" v-if="styles.module==='2' && !fixed">
				<view class="f-y-c f-w">
					<text @click="toSearch(word)" class="recommend" v-for="word in styles.fixedSearch.keyword.split(',')" :key="word">{{word}}</text>
				</view>
			</view>
		</view>
		<view class="bg-card" :class="styles.module==='2' && !fixed?'h24':''">
			<view style="height: 70rpx;"></view>
			<view class="f-y-c f-w" v-if="styles.module==='1'">
				<text @click="toSearch(word)" class="recommend" v-for="word in styles.fixedSearch.keyword.split(',')" :key="word">{{word}}</text>
			</view>
			<!-- 幻灯片 -->
			<view  v-if="styles.module==='2'"  class="mt20 pb20">
				<!-- <custom-swiper :styles="Object.assign(styles,{height:150})"></custom-swiper> -->
			</view>
			<view v-else class="w100" style="height: 20rpx;"></view>
		</view>
	</view>
	
</template>

<script>
	import {
		mapState,
	} from 'vuex'
	import utils from '@/common/utils.js'
	import CustomSwiper from "./CustomSwiper.vue"
	export default {
		components:{
			CustomSwiper
		},
		props:{
			styles:{
				type:Object,
				default:()=>{}
			},
			handleScroll:{
				type:[Number, String],
				default:0
			},
			paddingTop:{
				type: [Number, String],
				default: 0
			},
			positionColorOpacity:{
				type: [Number, String],
				default: 1
			},
			positionFixedDot:{
				type: [Number, String],
				default: 0
			}
		},
		data() {
			return {
				fixed: false,
				bgColor:'',
				background:'transparent',
			};
		},
		computed:{
			...mapState('dndc',['addInfo','regionId']),
			...mapState({
				storeSet: state => state.config.storeSet
			}),
			positionFixed(){
				return{
					fixed:`position:fixed;top:0;padding-top:${this.paddingTop}px;padding-bottom:20rpx;z-index:9;width:100%;background:${this.background}`,
				}
			},
			systemObj(){
				return this.system
			}
		},
		watch:{
			handleScroll(val){
				if(val>this.positionFixedDot + 1){
					this.fixed=true
				}else{
					this.fixed=false
				}
				if(val>this.positionFixedDot + 30){
					this.background = "#FFFFFF"
				}else{
					this.background = "transparent"
				}
			},
			positionColorOpacity(val){
				// rgba
				let arr = this.bgColor.split(',')
				// alpha 值
				arr[3] = val
				this.bgColor = arr.toString()
			},
			fixed(val){
				if(val){
					this.$nextTick(()=>{
						let query = uni.createSelectorQuery().in(this);
						query.select('.positionSearch').boundingClientRect().exec((res) => {
							// 获取<position>组件的高度 = top位置+组件高度-paddingTop手机状态栏高度 top位置需要减去一个paddingTop手机状态栏高度
							// this.$emit('storeListChangeFixedDot',(res[0].top+res[0].height)-this.paddingTop)
							// 搜索框放在机状态栏位置 不需要-paddingTop
							this.$emit('storeListChangeFixedDot',(res[0].top+res[0].height))
						})
					})
				}
			}
		},
		methods:{
			toSearch(word){
				this.go('navigateTo',`/yb_o2ov2/home/<USER>
			}
		},
		created() {
			// 滑动修改背景透明度
			this.bgColor = utils.colorToRGB(this.styles.colorBg) + ',1'
		}
	}
</script>

<style scoped lang="scss">
	.search{
		height: 60rpx;
		width: 700rpx;
		margin: 0 auto;
		.input{
			display: flex;
			height: 100%;
			align-items: center;
			border-radius: 30rpx;
			border: 1px solid;
			.btn{
				height: 60rpx;
				width: 90rpx;
				text-align: center;
				margin-left: auto;
				line-height: 60rpx;
				border-radius: 30rpx;
			}
		}
	}
	.recommend{
		background: #f6f7f9;
		padding: 5rpx 10rpx;
		border-radius: 20rpx;
		font-size: 24rpx;
		margin-right: 10rpx;
		margin-top: 10rpx;
	}
	.bg-card{
		background: #fff;
		padding: 20rpx 25rpx 0rpx;
		border-radius: 30rpx 30rpx 0 0;
	}
	.bg-card2{
		background: #fff;
		padding: 10rpx 25rpx 0rpx;
	}
	.h24{
		height: 240rpx;
	}
</style>
