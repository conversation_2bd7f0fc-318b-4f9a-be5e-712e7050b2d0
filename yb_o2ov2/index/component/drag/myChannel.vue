<template>
	<view class="" v-if="isLogin" :style="{
		background:styles.colorBg,
		marginTop:`${styles.marginTop}px`,
		marginBottom:`${styles.marginBottom}px`,
		marginLeft:`${styles.marginLR}px`,
		marginRight:`${styles.marginLR}px`,
		borderRadius:`${styles.circle}px`,
		}">
		<view class="card">
			<view class="title">{{styles.title}}</view>
			<view class="f-y-c f-bt f-w">
				<view class="mt20 bf bs15 p2" style="width:49%" v-for="(v,index) in styles.btnList" :key="index">
					<view class="flex f-y-c">
						<view class="iconbg1 f-c">
							<image class="wh el-image" :src="v.img1.img" mode="aspectFill">
						</view>
						<view class="ml10 wei f30">{{v.word1}}</view>
					</view>
					<view class="t-o-e mt10 f24 c6">{{v.word2}}</view>
					<view class="iconbg2 mt10" @click="goUrl(v.url1.url)">
						<image class="wh el-image" :src="v.img2.img" mode="aspectFill">
					</view>
					<view class="f-bt mt10 iconbg3">
						<view class="f-1 f-c-c brpx" @click="goUrl(v.url2.url)">
							<view class="limg f-c">
								<image class="wh el-image" :src="v.img3.img" mode="aspectFill">
							</view>
							<view class="mt5 t-o-e c3">{{v.word3}}</view>
						</view>
						<view class="f-1 f-c-c" @click="goUrl(v.url3.url)">
							<view class="limg f-c">
								<image class="wh el-image" :src="v.img4.img" mode="aspectFill">
							</view>
							<view class="mt5 t-o-e c3">{{v.word4}}</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
	} from 'vuex'
	export default {
		props: {
			styles: {
				type: Object,
				default: () => {}
			}
		},
		computed: {
			...mapState({
				layout: state => state.layout.personalcenter.body,
				user: state => state.user,
			}),
			jfName() {
				return this.system.custom.integral
			},
		},
		data() {
			return {}
		},
		methods: {

		},
	}
</script>

<style lang="scss" scoped>
	.card {
		// background: #ffffff;
		// border-radius: 20rpx;
		// padding: 20rpx;
		// margin-top: 20rpx;
	}

	.title {
		font-size: 30rpx;
		font-weight: bold;
	}

	.iconbg1 {
		width: 40rpx;
		height: 40rpx;
		background: #f5f5f5;
		border-radius: 50%;

		.el-image {
			width: 30rpx;
			height: 30rpx;
		}
	}

	.iconbg2 {
		.el-image {
			width: 100%;
			height: 280rpx;
			border-radius: 10px;
		}
	}

	.iconbg3 {
		.brpx {
			border-right: 2rpx solid #F2F2F2;
		}

		.limg {
			width: 60rpx;
			height: 60rpx;
			border-radius: 50%;
			background: #f5f5f5;

			.el-image {
				width: 40rpx;
				height: 40rpx;
			}
		}
	}
</style>
