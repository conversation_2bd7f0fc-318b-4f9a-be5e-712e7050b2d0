<template>
	<view class="bf bs20 p1"
	:style="{
		marginTop:`${styles.marginTop}px`,
		marginLeft:`${styles.marginLR}px`,
		marginRight:`${styles.marginLR}px`,
		marginBottom:`${styles.marginBottom}px`,
		background:`${styles.colorBg}`,
	}">
		<view class="f36 wei mb20">{{styles.title}}</view>
		<scroll-view scroll-x="true" class="ws-n f-bt mt10">
				<view v-for="(item,index) in list" :key="item.id" class="element dis-in" @click="goTo(item.id)">
					<view class="f-c-c wh">
						<view class="bs10" style="width: 150rpx;height: 150rpx;">
							<image class="wh" :src="item.icon" ></image>
						</view>
						<text class="t-o-e t-c c0 mt10" style="width:120rpx ;">{{item.name}}</text>
					</view>
				</view>
		</scroll-view>
	</view>
</template>

<script>
	import {
		mapState
	} from 'vuex'
	export default {
		props:{
			styles:{
				type:Object,
				default:()=>{}
			},
		},
		data(){
			return {
			list:[]
			}
		},
		computed:{
			...mapState({
				storeSet: state => state.config.storeSet
			}),
			...mapState('dndc',['regionId']),
		},
		watch:{
			// latLng(val){
			// 	// 修改定位后重新请求
			// 	if(val.latitude!==undefined){
			// 		this.fetchData()
			// 	}
			// }
		},
		created() {
			
		},
		methods:{
			async fetchData(){
				let {data} = await this.util.request({
					'url': this.api.storeRecommend,
					method: 'POST',
					data: {
						lat:this.latLng.latitude,
						lng:this.latLng.longitude,
						regionId:this.storeSet && this.storeSet.showType==1?this.regionId.id:'',
					}
				})
				this.list = data
			},
			goTo(id){
				// this.go('navigateTo',`/yb_o2ov2/home/<USER>/category-store?storeId=${id}`)
				this.go('navigateTo',`/yb_o2ov2/home/<USER>
			}
		}
		
	}
</script>

<style scoped lang="scss">
	.element{
		width: 150rpx;
		margin-right: 20rpx;
		display: inline-block;
	}
</style>
