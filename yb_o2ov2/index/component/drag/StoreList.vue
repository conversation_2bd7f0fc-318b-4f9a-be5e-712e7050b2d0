<!-- 首页商店列表专用 -->
<template>
	<view class="wrap">
		<view :style="fixed?positionFixed.tabnav:''">
				<!-- height 80rpx -->
				<tab-nav :bg-color="styles.colorBg" :activeTextColor="styles.colorWord" :inactiveTextColor="styles.colorWord" :activeColor="tColor" :current-index="current" :list="tabs" @change="tabsChange"></tab-nav>
		</view>
	
		<view v-for="key in styles.checkItem" :key="key">
			<block v-if="key==='1'">
				<view v-show="tabs[current].key === '1'">
					<view style="height: 80rpx;" :style="fixed?positionFixed.expand:`background:${styles.colorBg}`">
						<dropdown :isFixed="fixed" :styles="styles"></dropdown>
					</view>
					<view  :style="fixed?'padding-top:220rpx':''">
						<StoreList ref="storelist" :styles="styles"></StoreList>
					</view>
				</view>
			</block>
			<block v-if="key==='2'">
				<view v-show="tabs[current].key === '2'" :style="fixed?'padding-top:80rpx':''">
					<FindGoods ref="findgoods"></FindGoods>
				</view>
			</block>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
	} from 'vuex'
	import TabNav from '@/components/TabNav.vue'
	import StoreList from '@/components/StoreList.vue'
	import FindGoods from '@/components/FindGoods.vue'
	import Dropdown from '@/components/Dropdown.vue'
	export default {
		components:{
			TabNav,StoreList,FindGoods,Dropdown
		},
		props:{
			styles:{
				type:Object,
				default:()=>{}
			},
			handleScroll:{
				type:[Number, String],
				default:0
			},
			paddingTop:{
				type: [Number, String],
				default:0
			},
			storeListDot:{
				type: [Number, String],
				default:0
			},
			storeListDotTop:{
				type: [Number, String],
				default:0
			},
		},
		data() {
			return {
				bgColor:'transparent',
				temp:[0,0,0], //储存每个页面的scroll位置信息 有几个页面就几个0
				fixed:false,
				current: 0, // tabs组件的current值，表示当前活动的tab选项
			}
		},
		watch:{
			handleScroll:{
				handler(val){
					if(val+this.storeListDotTop>=this.storeListDot){
						this.fixed=true
						this.bgColor = '#FFFFFF'
					}else{
						this.fixed=false
						this.bgColor = 'transparent'
					}
				},
				deep:true,
				immediate:true
			},
			latLng(val){
				this.init()
			}
		},
		computed:{
			...mapState({
				pxToRpxRate:state=>state.systemInfo.pxToRpxRate,
			}),
			tabs(){
				return this.styles.checkItem.map(key=>{
					if(key === '1')return{name:this.styles.titleList[0],key}
					if(key === '2')return{name:this.styles.titleList[1],key}
				})
				// return [{name: '商店列表'}, {name: '发现好菜'}]
			},
			positionFixed(){
				return{
					tabnav:`position:fixed;top:${Math.floor(this.storeListDotTop)}px;width:100%;background:${this.bgColor};z-index:1;`,
					expand:`position:fixed;top:${Math.floor(this.storeListDotTop+(79/this.pxToRpxRate))}px;height:150rpx;width:100%;background:${this.bgColor};z-index:1;`
				}
			},
		},
		methods: {
			init(){
				// 传参 请求数据
				try{
					if(this.$refs.storelist){
						this.$refs.storelist[0].setApi(this.api.storeList,{lat:this.latLng.latitude,lng:this.latLng.longitude,page:1})
						this.$refs.storelist[0].refresh()
					}
					if(this.$refs.findgoods){
						this.$refs.findgoods[0].setApi(this.api.rcommendGood,{lat:this.latLng.latitude,lng:this.latLng.longitude})
						this.$refs.findgoods[0].refresh()
					}
					
				}catch(e){
					//TODO handle the exception
					this.util.message('网络异常', 3)
				}
				
			},
			nextPage(){
				let current = this.current
				if(this.tabs[current].key === '1'){
					this.$refs.storelist[0].nextPage()
				}else if(this.tabs[current].key === '2'){
					this.$refs.findgoods[0].nextPage()
				}
			},
			// tabs通知swiper切换
			tabsChange(index) {
				this.temp[this.current] = this.handleScroll
				this.current = index;
				this.$emit('changeTop', this.temp[index])
			},
		}
	}
</script>

<style lang="scss" scoped>
	
	
</style>