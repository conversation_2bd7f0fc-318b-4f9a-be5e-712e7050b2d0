<template>
	<view class="wrap mt20 o-h"
	:style="{
		marginTop:`${styles.marginTop}px`,
		marginLeft:`${styles.marginLR}px`,
		marginRight:`${styles.marginLR}px`,
		marginBottom:`${styles.marginBottom}px`,
		borderRadius:`${styles.circle}px`
	}"
	>
		<u-swiper :list="styles.imgUrl" :height="styles.height * pxToRpxRate" name="img" @click="goToUrl"></u-swiper>
	</view>
</template>

<script>
	export default {
		props:{
			styles:{
				type:Object,
				default:()=>{}
			},
			imgUrl:{
				type:Array,
				default:()=>[]
			}
		},
		data(){
			return{
				
			}
		},
		methods:{
			goToUrl(index){
				this.goUrl(this.styles.imgUrl[index].url)
			},
			goTo(index){
				if(index >2) return
				this.go('navigateTo',`/yb_o2ov2/home/<USER>/card?type=${index+1}`)
			}
		}
	}
</script>

<style>
</style>
