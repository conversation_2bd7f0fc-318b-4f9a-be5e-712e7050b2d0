<template>
	<view class="" v-if="isLogin" :style="{
		background:styles.colorBg,
		marginTop:`${styles.marginTop}px`,
		marginBottom:`${styles.marginBottom}px`,
		marginLeft:`${styles.marginLR}px`,
		marginRight:`${styles.marginLR}px`,
		borderRadius:`${styles.circle}px`,
		}">
		<view class="f-x-bt p2">
			<view class="f-1 t-o-e wei" :style="{color:styles.colorWord}">{{styles.text}}</view>
			<view class="f-0" @click="goVip">
				<view class="kthy f-c" :style="{background:styles.colorBg2,color:styles.colorWord2}" v-if="user.isVip">查看详情</view>
				<view class="kthy f-c" :style="{background:styles.colorBg2,color:styles.colorWord2}" v-else>开通会员</view>
			</view>
		</view>
		
	</view>
</template>

<script>
	import {
		mapState,
	} from 'vuex'
	export default {
		props:{
			styles:{
				type:Object,
				default:()=>{}
			}
		},
		computed:{
			...mapState({
				layout:state=>state.layout.personalcenter.body,
				user: state => state.user,
			}),
			jfName() {
				return this.system.custom.integral
			},
			customeColor(){
				if(!this.layout)return ''
				let drag = this.layout.list.find(drag=>drag.name === 'vip')
				if(!drag)return ''
				if(drag.styles.bgType=='1'){
					if(drag.styles.gradient.model){
						return `linear-gradient(${drag.styles.gradient.deg}deg,${drag.styles.gradient.startColor},${drag.styles.gradient.endColor})`
					}else{
						return drag.styles.colorBg || this.tColor
					}
				}
				else{
					return `url(${drag.styles.img})`
				}
			},
			customeBg(){
				if(!this.layout)return false
				let drag = this.layout.list.find(drag=>drag.name === 'vip')
				if(!drag)return false
				if(drag.styles.bgType=='2'){
					return true
				}else{
					return false
				}
			}
		},
		data() {
			return {
			}
		},
		methods: {
			goVip(){
				let pages = getCurrentPages(),
				url = {category: "平台首页",name: {id: "payVip", name: "会员卡"},params: "platform"},
				type = 'navigateTo'
				if(pages[0].__route__==='yb_o2ov2/index/index'&&pages[1]===undefined){
					pages[0].$vm.changeTab(url)
				}else{
					this.go(type,`/yb_o2ov2/index/index?changeTab=${JSON.stringify(url)}`)
				}
			},
		},
	}
</script>

<style lang="scss" scoped>
	.kthy{
		width: 160rpx;
		height: 60rpx;
		border-radius: 60rpx;
	}
</style>
