<template>
	<view
	 v-if="list.length!==0"
	 :style="{
		marginTop:`${styles.marginTop}px`,
		marginLeft:`${styles.marginLR}px`,
		marginRight:`${styles.marginLR}px`,
		marginBottom:`${styles.marginBottom}px`,
	}">
		<u-swiper height="480" custom :list="list" bg-color="#FFF" :autoplay="false"
			:borderRadius="styles.circleBtn * pxToRpxRate">
			<template v-for="(item,index) in list" :slot="'element-'+index">
				<view :key="index" class="p-r" @click="go('navigateTo',`/yb_o2ov2/home/<USER>">
					<!-- 背景 -->
					<view class="p-a w100 o-h"
						style="height: 110rpx;left:0;top: 0;z-index: -1;filter: brightness(80%);">
						<image class="wh"
							:src="item.takeOutSet.signature"
							mode="widthFix"></image>
						<!-- 大牌臻选label -->
						<view class="p-a" style="width:56rpx ;height: 64rpx;top: 0;right: 20rpx;">
							<image class="wh" src="/static/label/1.png" mode=""></image>
						</view>
					</view>
					<view class="p2 p-r" style="padding-top: 40rpx;">
						<!-- 店铺 -->
						<view class="f-y-c">
							<!-- logo -->
							<view class="mr20 bs10" style="width: 90rpx;height: 90rpx;">
								<image class="wh"
									:src="item.icon"
									mode=""></image>
							</view>
							<view>
								<view class="cf f32 t-o-e" style="width: 540rpx;">{{item.name}}</view>
								<block v-if="item.discount.reduce && item.discount.reduce.data.type === '1'">
									<!-- 循环满减 -->
									<view>
										<text class="red-label">每满{{item.discount.reduce.data.fullMoney}}减{{item.discount.reduce.data.money}}</text>
									</view>
								</block>
								<block v-else-if="item.discount.reduce && item.discount.reduce.data.type === '2'">
									<!-- 阶梯满减 -->
									<view>
										<text v-for="(text,index) in item.discount.reduce.data.moneyArr" :key='text.fullMoney'
										class="red-label">
											{{`满${text.fullMoney}减${text.money}`}}
										</text>
									</view>
								</block>
							</view>
						</view>
						<!-- 随机评论 -->
						<view class="f36 wei m10">
							<text  class="mr10">{{item.takeOutSet.recommendation}}</text>
						</view>
						<!-- 商品 -->
						<view :class="item.recommendGoods.length>2?'f-x-bt':'f-y-c'">
							<view class="f-col" v-for="goods in item.recommendGoods" :key="goods.id"
							:style="{maxWidth:`${itemWidth}rpx`,width:'215rpx',marginRight:`${recommendGoods.length===2?':20rpx':''}`}"
							@click="go('navigateTo',`/yb_o2ov2/home/<USER>">
								<view class="bs10" style="width: 215rpx;height: 160rpx;">
									<image class="wh"
										:src="goods.icon"
										mode="aspectFill"></image>
								</view>
								<view class="f24 t-o-e mt10 c0" style="width: 200rpx;">{{goods.name}}</view>
								<view class="f24" style="margin-top: -5rpx;">
									<text class="mr10" style="color:#f94136 ;"><text
											class="f24">$</text><text class="wei f28">{{goods.price}}</text></text>
									<text v-if="goods.crossedPrice" class="t-d-l c9 ">${{goods.crossedPrice}}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</template>
		</u-swiper>
	</view>
</template>

<script>
	import {
		mapState
	} from 'vuex'
	export default {
		props: {
			styles: {
				type: Object,
				default: () => {}
			},
			type:{
				type: [String,Number],
				default: 1 //1首页 2分类页
			},
			pid:{
				type: [String,Number],
				default: 0 ,//pid 一级分类id
			}
		},
		data() {
			return {
				list:[]
			}
		},
		computed:{
			itemWidth(){
				return (750- 2 * this.styles.marginLR * this.pxToRpxRate)/3
			},
			...mapState({
				storeSet: state => state.config.storeSet
			}),
			...mapState('dndc',['regionId']),
		},
		methods:{
			async fetchData(){
				let {data} = await this.util.request({
					'url': this.api.getStoreSwiper,
					data: {
						type: this.type,
						id: this.pid,
						lat:this.latLng.latitude,
						lng:this.latLng.longitude,
						regionId:this.storeSet && this.storeSet.showType==1?this.regionId.id:'',
					},
				})
				this.list = data
			}
		}

	}
</script>

<style scoped lang="scss">
	.red-label {
		display: inline-block;
		font-size: 20rpx;
		padding: 2rpx 10rpx;
		margin-right: 10rpx;
		border-radius: 5rpx;
		background: #f94136;
		color: #fff;
	}
</style>
