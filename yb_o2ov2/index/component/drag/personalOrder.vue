<template>
	<view v-if="isLogin" :style="{
		marginTop:`${styles.marginTop}px`,
		marginBottom:`${styles.marginBottom}px`,
		marginLeft:`${styles.marginLR}px`,
		marginRight:`${styles.marginLR}px`,
		borderRadius:`${styles.circle}px`,
		}" class="bf p20">
		<view class="p02">
			<view class="card" v-if="isLogin&&styles.showAssets==='1'">
				<view class="f-bt">
					<view class="title">我的订单</view>
					<view class="c9 f26 f-c"  @click="goComment(0)">全部订单 <text class="iconfont iconinto f24 c9 ml5"></text></view>
				</view>
				<view class="f-x-c-sa">
					<view v-if="styles.balance.show === '1'" class="f-c-c mt20" @click="goComment(0)">
						<view class="f-y-c f30">
							<view class="" style="width: 40rpx;height: 40rpx;">
								<image class="wh" :src="styles.balance.img.search('/static/img/') === -1?styles.balance.img:'/static/img_my/3.png'" mode=""></image>
							</view>
						</view>
						<view class="mt10">
							<text class="">{{styles.balance.text}}</text>
						</view>
					</view>
					<view  v-if="styles.platform.show === '1'" class="f-c-c mt20" @click="goComment(0)">
						<view class="f-y-c f30">
							<view class="" style="width: 40rpx;height: 40rpx;">
								<image class="wh" :src="styles.platform.img.search('/static/img/') === -1?styles.platform.img:'/static/img_my/4.png'" mode=""></image>
							</view>
						</view>
						<view class="mt10">
							<text class="">{{styles.platform.text}}</text>
						</view>
					</view>
					<view v-if="styles.store.show === '1'" class="f-c-c mt20" @click="goComment(1)">
						<view class="f-y-c f30">
							<view class="" style="width: 40rpx;height: 40rpx;">
								<image class="wh" :src="styles.store.img.search('/static/img/') === -1?styles.store.img:'/static/img_my/1.png'" mode=""></image>
							</view>
						</view>
						<view class="mt10">
							<text class="">{{styles.store.text}}</text>
						</view>
					</view>
					<view v-if="styles.integral.show === '1'"  class="f-c-c mt20" @click="goComment(2)">
						<view class="f-y-c f30">
							<view class="" style="width: 40rpx;height: 40rpx;">
								<image class="wh" :src="styles.integral.img.search('/static/img/') === -1?styles.integral.img:'/static/img_my/2.png'" mode=""></image>
							</view>
						</view>
						<view class="mt10">
							<text class="">{{styles.integral.text}}</text>
						</view>
					</view>
				</view>
			</view>
			<!-- 我的收藏 -->
		<!-- 	<view class="card" @click="go('navigateTo','/yb_o2ov2/my/collection')">
				<view class="title">我的收藏</view>
				<view class="f-x-bt mt30 p-r">
					<view class="flex o-x-s f24">
						<view v-for="element in 4" :key="element" class="f-c-c mr20">
							<view class="bs20"  style="width: 180rpx; height: 140rpx;">
								<image class="wh" src="https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fimg.zcool.cn%2Fcommunity%2F010abe58c0dd94a801219c77ca8b11.jpg" mode=""></image>
							</view>
						</view> -->
						<!-- 占位元素 -->
				<!-- 		<view style="width: 120rpx;height: 1px;flex-shrink: 0;"></view>
					</view>
					<view class="t-r p-a h100 f-c-xc pl20" style="right: 0;background: rgba(255,255,255,.5);">
						<view class="f22 flex f-a-b">
							<text class="wei">4家店铺</text>
							<text class="iconfont iconinto f18"></text>
						</view>
					</view>
				</view>
			</view> -->
			
		</view>
		
	</view>
</template>

<script>
	import {
		mapState,
	} from 'vuex'
	export default {
		props:{
			styles:{
				type:Object,
				default:()=>{}
			}
		},
		computed:{
			...mapState({
				layout:state=>state.layout.personalcenter.body
			}),
			jfName() {
				return this.system.custom.integral
			},
			customeColor(){
				if(!this.layout)return ''
				let drag = this.layout.list.find(drag=>drag.name === 'vip')
				if(!drag)return ''
				if(drag.styles.bgType=='1'){
					if(drag.styles.gradient.model){
						return `linear-gradient(${drag.styles.gradient.deg}deg,${drag.styles.gradient.startColor},${drag.styles.gradient.endColor})`
					}else{
						return drag.styles.colorBg || this.tColor
					}
				}
				else{
					return `url(${drag.styles.img})`
				}
			},
			customeBg(){
				if(!this.layout)return false
				let drag = this.layout.list.find(drag=>drag.name === 'vip')
				if(!drag)return false
				if(drag.styles.bgType=='2'){
					return true
				}else{
					return false
				}
			}
		},
		data() {
			return {
			}
		},
		methods: {
			goComment(current){
				let pages = getCurrentPages(),
				url = {category: "平台首页",name: {id: "myOrder", name: "我的订单"},params: "platform"},
				type = 'navigateTo'
				uni.setStorageSync('current', current)
				if(pages[0].__route__==='yb_o2ov2/index/index'&&pages[1]===undefined){
					pages[0].$vm.changeTab(url)
				}else{
					this.go(type,`/yb_o2ov2/index/index?changeTab=${JSON.stringify(url)}`)
				}
			},
		},
	}
</script>

<style lang="scss" scoped>
	.card{
		// background: #ffffff;
		border-radius: 20rpx;
		// padding: 20rpx;
		// margin-top:20rpx ;
	}
	.title{
		font-size: 30rpx;
		font-weight: bold;
	}
</style>
