<template>
	<view class="notice f-s-ac bf p1"
	 :style="{
		borderRadius:`${styles.circle}px`,
	 	marginTop:`${styles.marginTop}px`,
	 	marginBottom:`${styles.marginBottom}px`,
	 	marginLeft:`${styles.marginLR}px`,
	 	marginRight:`${styles.marginLR}px`,
		color:`${styles.colorWord}`,
		background:`${styles.colorBg}`
	 	}"
	 >
		<view v-if="styles.hand[0].img" class="limg mr20"  @click="checkNotice">
			<image class="wh" :src="styles.hand[0].img"  mode=""></image>
		</view>
		<text v-else class="f24 bs10 wei" style="padding: 0 6rpx;"  @click="checkNotice"  :style="{color:styles.leftText.color,background:styles.leftText.colorBg}">{{styles.leftText.text}}</text>
		<u-notice-bar class="f-g-1" :color="styles.colorWord || '#000'" padding="18rpx 24rpx 18rpx 0" :volume-icon="false" type="none" mode="vertical" :list="list" @click="checkNotice"  @change="getIndex"></u-notice-bar>
		<!-- <text @click="checkNotice" class="f24 bs10" style="padding: 0 6rpx;" :style="{color:styles.leftText.colorBg,border:`1rpx solid ${styles.leftText.colorBg}`}">去看看</text> -->
		<text @click="checkNotice" class="iconfont iconinto f24 ml10 c6"></text>
	</view>
</template>

<script>
	export default {
		props:{
			styles:{
				type:Object,
				default:()=>{}
			},
		},
		data(){
			return {
				list:[],
				index:'0'
			}
		},
		created() {
			this.fetchData()
		},
		methods:{
			getIndex(index){
				this.index = index
			},
			checkNotice(){
				let result = this.result[this.index]
				uni.setStorageSync('noticeDetail',result)
				this.go('navigateTo',`/yb_o2ov2/home/<USER>
			},
			async fetchData(){
				let {data} =  await this.util.request({
					url: this.api.ptgg,
					method: 'GET',
					data: {type:this.styles.source}
				})
				this.result = data
				data.forEach(item=>{
					this.list.push(item.title)
				})
			}
		}
		
	}
</script>

<style scoped lang="scss">
	.limg image{
		width: 48rpx;
		height: 48rpx;
	}
</style>
