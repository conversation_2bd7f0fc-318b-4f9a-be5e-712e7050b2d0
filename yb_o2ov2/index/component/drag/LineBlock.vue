<template>
	<view class="w100" 
	:style="{
		height:`${styles.height}px`,
		marginTop:`${styles.marginTB}px`,
		marginBottom:`${styles.marginTB}px`
		}">
		<view v-if="styles.type===1" class="h100 w100" :style="{background:styles.colorBg}"></view>
		<view v-else-if="styles.type===2" class="h100 w100" :style="{borderTop:`1px solid ${styles.colorBg}`,borderBottom:`1px solid ${styles.colorBg}`}"></view>
		<view v-else-if="styles.type===3" class="h100 w100" :style="{borderTop:`1px dashed ${styles.colorBg}`,borderWidth:`${styles.height}px`}"></view>
		<view v-else-if="styles.type===4" class="h100 w100" :style="{borderTop:`1px dotted ${styles.colorBg}`,borderWidth:`${styles.height}px`}"></view>
	</view>
</template>

<script>
	export default {
		props:{
			styles:{
				type:Object,
				default:()=>{}
			}
		},
		data() {
			return {
			}
		},
		methods: {
			
		},
	}
</script>

<style>
</style>
