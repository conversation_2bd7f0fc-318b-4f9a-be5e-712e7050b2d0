<template>
	<view class="headlines bf p1"
		:style="{
		background:styles.colorBg,
		marginTop:`${styles.marginTop}px`,
		marginBottom:`${styles.marginBottom}px`,
		marginLeft:`${styles.marginLR}px`,
		marginRight:`${styles.marginLR}px`,
		borderRadius:`${styles.circle}px`,
		}">
		<!-- <view class="f-bt m1110"><text class="f36 wei">同城头条</text><text>更多 ></text></view> -->
		<view class="f-bt  mt30 bb569" v-for="(item,index) in styles.imgUrl" :key="index" @click="goUrl(item.url)">
			<view class="f-c">
				<view class="m1110" style="width: 40rpx;height: 40rpx;">
					<image class="wh" :src="item.img" mode=""></image>
				</view>
				<text :style="{color:styles.leftColor}">{{item.leftWord}}</text>
			</view>
			<view class="f-y-c" :style="{color:styles.rightColor}">
				<view class="mr10">{{item.rightWord}}</view>
					<view v-if="styles.img" style="width: 40rpx;height: 40rpx;">
						<image class="wh" :src="styles.img" mode=""></image>
					</view>
					<text v-else class="iconfont iconinto f26" :style="{color:styles.rightColor}"></text>
			</view>
			
		</view>
	</view>
</template>

<script>
	export default {
		props:{
			styles:{
				type:Object,
				default:()=>{}
			}
		},
		data() {
			return {
			}
		},
		methods: {
			
		},
	}
</script>

<style scoped lang="scss">
	.bb569{
		border-bottom: 1px solid #f5f6f9;
	}
</style>
