<template>
	<view class=""
	:style="{
		marginTop:`${styles.marginTop}px`,
		marginBottom:`${styles.marginBottom}px`,
		marginLeft:`${styles.marginLR}px`,
		marginRight:`${styles.marginLR}px`,
		}">
			<view class="p-r o-h" style="width:100%" :style="{borderRadius:`${styles.circle}px`}">
				<!-- 背景图 -->
				<image class="wh" :src="styles.img" mode="widthFix"></image>
				<!-- 热区 -->
				<view v-for="(item,index) in styles.divStyles" :key="index" class="p-a"
				:style="{width:`${item.width*transform}rpx`,height:`${item.height*transform}rpx`,left:`${item.sX*transform}rpx`,top:`${item.sY*transform}rpx`}"
				@click="handlerGoUrl(item.url)"
				></view>
			</view>
	</view>
</template>

<script>
	export default {
		components:{
		},
		props:{
			styles:{
				type:Object,
				default:()=>{}
			}
		},
		data() {
			return {
			}
		},
		computed:{
			transform(){
				return 1-(2*this.styles.marginLR*this.pxToRpxRate/750)
			}
		},
		methods: {
			handlerGoUrl(url){
				this.goUrl(JSON.parse(url))
			}
		},
	}
</script>

<style>
</style>
