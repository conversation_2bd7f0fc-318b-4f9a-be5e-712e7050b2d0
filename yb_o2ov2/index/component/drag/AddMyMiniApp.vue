<template>
	<view class="tip" v-if="show" @click="show=false" :style="{top:`${statusNavBarHeight +30/pxToRpxRate}px`,background:styles.colorBg,color:styles.colorWord}">
		<view class="bsf bf mr10" style="width: 60rpx;height: 60rpx;"><image class="wh" :src="styles.img||system.icon" mode="aspectFill"></image></view>
		<view class="wei f24">{{styles.text}}</view>
		<!-- 三角 -->
		<view class="p-a triangle" :style="{borderBottom:`20rpx solid ${styles.colorBg}`}"></view>
	</view>
</template>

<script>
	export default {
		props:{
			styles:{
				type:Object,
				default:()=>{}
			}
		},
		data() {
			return {
				show:true
			}
		},
		created(){
			setTimeout(()=> {
				this.show=false
			}, 10000);
		},
		methods: {
		},
	}
</script>

<style scoped lang="scss">
	.tip{
		display: flex;
		justify-content: flex-start;
		position: fixed;
		right: 20rpx;
		z-index: 10;
		border-radius: 60rpx;
		padding: 10rpx 20rpx 10rpx 10rpx;
		align-items: center;
	}
	.triangle{
		width: 0;
		height: 0;
		border-right: 20rpx solid transparent;
		border-left: 20rpx solid transparent;
		top: -20rpx;
		right: 110rpx;
	}
</style>