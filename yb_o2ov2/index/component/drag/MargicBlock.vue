<template>
	<view class="" :style="{
		marginTop:`${styles.marginTop}px`,
		marginBottom:`${styles.marginBottom}px`,
		marginLeft:`${styles.marginLR}px`,
		marginRight:`${styles.marginLR}px`,
		}">
		<!-- 一行两个 -->
		<block v-if="styles.type === '1'">
			<view class="flex" :style="{height:`${styles.height*pxToRpxRate}rpx`}">
				<view class="p-r o-h" style="width: 50%;border: 1px solid #eee;"
					:style="{borderRadius:`${styles.circle}px`}" @click="goUrl(styles.imgList1[0].url)">
					<image class="wh p-a p-a-c" :src="styles.imgList1[0].img" mode="aspectFill"></image>
				</view>
				<view class="p-r o-h" style="width: 50%;border: 1px solid #eee;"
					:style="{borderRadius:`${styles.circle}px`,marginLeft:`${styles.shifting}px`}"
					@click="goUrl(styles.imgList1[1].url)">
					<image class="wh p-a p-a-c" :src="styles.imgList1[1].img" mode="aspectFill"></image>
				</view>
			</view>
		</block>
		<!-- 一行三个 -->
		<block v-else-if="styles.type === '2'">
			<view class="flex" :style="{height:`${styles.height*pxToRpxRate}rpx`}">
				<view class="p-r o-h" style="width: 50%;border: 1px solid #eee;"
					:style="{borderRadius:`${styles.circle}px`}" @click="goUrl(styles.imgList1[0].url)">
					<image class="wh p-a p-a-c" :src="styles.imgList1[0].img" mode="aspectFill"></image>
				</view>
				<view class="p-r o-h" style="width: 50%;border: 1px solid #eee;"
					:style="{borderRadius:`${styles.circle}px`,marginLeft:`${styles.shifting}px`,marginRight:`${styles.shifting}px`}"
					@click="goUrl(styles.imgList1[1].url)">
					<image class="wh p-a p-a-c" :src="styles.imgList1[1].img" mode="aspectFill"></image>
				</view>
				<view class="p-r o-h" style="width: 50%;border: 1px solid #eee;"
					:style="{borderRadius:`${styles.circle}px`}" @click="goUrl(styles.imgList1[2].url)">
					<image class="wh p-a p-a-c" :src="styles.imgList1[2].img" mode="aspectFill"></image>
				</view>
			</view>
		</block>
		<!-- 一行四个 -->
		<block v-else-if="styles.type === '3'">
			<view class="flex" :style="{height:`${styles.height*pxToRpxRate}rpx`}">
				<view class="p-r o-h" style="width: 50%;border: 1px solid #eee;"
					:style="{borderRadius:`${styles.circle}px`}" @click="goUrl(styles.imgList1[0].url)">
					<image class="wh p-a p-a-c" :src="styles.imgList1[0].img" mode="aspectFill"></image>
				</view>
				<view class="p-r o-h" style="width: 50%;border: 1px solid #eee;"
					:style="{borderRadius:`${styles.circle}px`,marginLeft:`${styles.shifting}px`}"
					@click="goUrl(styles.imgList1[1].url)">
					<image class="wh p-a p-a-c" :src="styles.imgList1[1].img" mode="aspectFill"></image>
				</view>
				<view class="p-r o-h" style="width: 50%;border: 1px solid #eee;"
					:style="{borderRadius:`${styles.circle}px`,marginLeft:`${styles.shifting}px`}"
					@click="goUrl(styles.imgList1[2].url)">
					<image class="wh p-a p-a-c" :src="styles.imgList1[2].img" mode="aspectFill"></image>
				</view>
				<view class="p-r o-h" style="width: 50%;border: 1px solid #eee;"
					:style="{borderRadius:`${styles.circle}px`,marginLeft:`${styles.shifting}px`}"
					@click="goUrl(styles.imgList1[3].url)">
					<image class="wh p-a p-a-c" :src="styles.imgList1[3].img" mode="aspectFill"></image>
				</view>
			</view>
		</block>
		<!-- 左一右二 -->
		<block v-else-if="styles.type === '4'">
			<view class="flex" :style="{height:`${styles.height*pxToRpxRate}rpx`}">
				<view class="p-r o-h" style="width: 50%;border: 1px solid #eee;"
					:style="{borderRadius:`${styles.circle}px`}" @click="goUrl(styles.imgList1[0].url)">
					<image class="wh p-a p-a-c" :src="styles.imgList1[0].img" mode="aspectFill"></image>
				</view>
				<view class="f-col" style="width: 50%;" :style="{marginLeft:`${styles.shifting}px`}">
					<view class="p-r o-h w100" style="height: 50%;border: 1px solid #eee;"
						:style="{borderRadius:`${styles.circle}px`,marginBottom:`${styles.shifting}px`}"
						@click="goUrl(styles.imgList1[1].url)">
						<image class="wh p-a p-a-c" :src="styles.imgList1[1].img" mode="aspectFill"></image>
					</view>
					<view class="p-r o-h w100" style="height: 50%;border: 1px solid #eee;"
						:style="{borderRadius:`${styles.circle}px`}" @click="goUrl(styles.imgList1[2].url)">
						<image class="wh p-a p-a-c" :src="styles.imgList1[2].img" mode="aspectFill"></image>
					</view>
				</view>
			</view>
		</block>
		<!-- 上一下二 -->
		<block v-else-if="styles.type === '5'">
			<view class="flex" :style="{height:`${styles.height*pxToRpxRate}rpx`}">
				<view class="p-r o-h w100" style="border: 1px solid #eee;"
					:style="{borderRadius:`${styles.circle}px`,marginBottom:`${styles.shifting}px`}"
					@click="goUrl(styles.imgList1[0].url)">
					<image class="wh p-a p-a-c" :src="styles.imgList1[0].img" mode="aspectFill"></image>
				</view>
			</view>
			<view class="flex" :style="{height:`${styles.height*pxToRpxRate}rpx`}">
				<view class="p-r o-h" style="width: 50%;border: 1px solid #eee;"
					:style="{borderRadius:`${styles.circle}px`}" @click="goUrl(styles.imgList1[1].url)">
					<image class="wh p-a p-a-c" :src="styles.imgList1[1].img" mode="aspectFill"></image>
				</view>
				<view class="p-r o-h" style="width: 50%;border: 1px solid #eee;"
					:style="{borderRadius:`${styles.circle}px`,marginLeft:`${styles.shifting}px`}"
					@click="goUrl(styles.imgList1[2].url)">
					<image class="wh p-a p-a-c" :src="styles.imgList1[2].img" mode="aspectFill"></image>
				</view>
			</view>
		</block>
	</view>
</template>

<script>
	export default {
		components: {},
		props: {
			styles: {
				type: Object,
				default: () => {}
			}
		},
		data() {
			return {}
		},

		methods: {

		},
	}
</script>

<style>
</style>
