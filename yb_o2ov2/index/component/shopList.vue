<template>
	<view class="shop-item" @click="go('navigateTo', `/yb_o2ov2/home/<USER>">
		<image class="shop-img" :src="item.icon"></image>
		<view class="info">
			<view class="name">{{item.name}}</view>
			<view class="location-box">
				<image class="icon" src="/static/home/<USER>"></image>
				<text>{{item.distance}}</text>
			</view>
			<view class="info-buttom">
				<image class="icon" src="/static/home/<USER>"></image>
				<text>{{item.evaluationscore}}</text>
				<view class="label">{{item.category_name}}</view>
				<view class="label">{{$t('home.sales_volume')}}:{{item.outSales}}</view>
			</view>
		</view>
	</view>
</template>

<script>
	import utils from '@/common/utils.js'
	export default {
		props: {
			item: {
				type: Object,
				default: () => {}
			}
		},
		data() {
			return {}
		},
		methods: {

		}
	}
</script>

<style lang="scss" scoped>
	.shop-item {
		display: flex;
		justify-content: flex-start;
		align-items: center;
		margin-top: 20rpx;

		&:first-child {
			margin-top: 0;
		}

		.shop-img {
			display: flex;
			width: 190rpx;
			height: 190rpx;
			border-radius: 20rpx;
			object-fit: contain;
			flex-shrink: 0;
		}

		.info {
			margin-left: 26rpx;

			.name {
				font-weight: 600;
				font-size: 28rpx;
				color: #333333;
				line-height: 33rpx;
			}

			.location-box {
				display: flex;
				justify-content: flex-start;
				align-items: center;
				margin-top: 4rpx;

				font-weight: 400;
				font-size: 22rpx;
				color: #777777;
				line-height: 26rpx;

				.icon {
					width: 32rpx;
					height: 32rpx;
					margin-right: 8rpx;
				}
			}

			.info-buttom {
				display: flex;
				justify-content: flex-start;
				align-items: center;
				margin-top: 24rpx;

				font-weight: bold;
				font-size: 28rpx;
				color: #333333;
				line-height: 33rpx;

				.icon {
					width: 26rpx;
					height: 26rpx;
					margin-right: 10rpx;
				}

				.label {
					padding: 8rpx 12rpx;
					background: #EDEDED;
					border-radius: 10rpx;
					margin-left: 10rpx;

					font-weight: 400;
					font-size: 22rpx;
					color: #777777;
					line-height: 26rpx;
				}
			}
		}
	}
</style>