<template>
	<view class="wh">
	<!-- 	<view class="p-f" style="top:0;width: 100%;z-index: 3;">
			<view class="flex f-c"
				:style="{height:`${statusNavBarHeight}px`,paddingTop:`${menuButtonTop}px`,background:customeColor,color:customefontColor}">
				<text class="wei f32">个人中心</text>
			</view>
			<view class="tabNav">
				<tab-nav gutter="80" :height="tabNavHeight" :activeColor="tColor" fontSize="30" inactiveTextColor="#666"
					:current-index="current" :list="tabs" @change="tabsChange"></tab-nav>
			</view>
		</view> -->
		<!-- <view class="p-f" style="top:0;width: 100%;z-index: 3;">
			<view class="flex bf"
				:style="{height:`${statusNavBarHeight}px`,paddingTop:`${menuButtonTop}px`}">
					<text class="mla wei f32">个人中心</text>
			</view>
		</view> -->
		<scroll-view scroll-y="true" class="wh"
			style="background: #f5f5f5;"
			>
			<block v-for="(drag,index) in layout.list" :key="index">
				<!-- 幻灯片  -->
				<CustomSwiper  v-if="drag.name === 'picLunbo'" :styles="drag.styles"></CustomSwiper>
				<!-- 按钮组  -->
				<ButtonGroup v-else-if="drag.name === 'btn'" :styles="drag.styles"></ButtonGroup>
				<!-- 图片组 -->
				<ImageGroup v-else-if="drag.name === 'pictures'" :styles="drag.styles"></ImageGroup>
				<!-- 标题栏 -->
				<TitleBlock v-else-if="drag.name === 'titles'" :styles="drag.styles"></TitleBlock>
				<!-- 辅助空白 -->
				<BlankBlock v-else-if="drag.name === 'blank'" :styles="drag.styles"></BlankBlock>
				<!-- 辅助线 -->
				<LineBlock v-else-if="drag.name === 'lines'" :styles="drag.styles"></LineBlock>
				<!-- 富文本 -->
				<RichText v-else-if="drag.name === 'book'" :styles="drag.styles"></RichText>
				<!-- 同城头条  Bulletin 列表导航 -->
				<Bulletin v-else-if="drag.name === 'listNav'" :styles="drag.styles"></Bulletin>
				<!-- 热区 -->
				<HotBlock v-else-if="drag.name === 'hot'" :styles="drag.styles"></HotBlock>
				<!-- 魔方 -->
				<MargicBlock v-else-if="drag.name === 'margic'" :styles="drag.styles"></MargicBlock>
				<!-- 个人信息 -->
				<Vip v-else-if="drag.name === 'vip'" :styles="drag.styles"></Vip>
				<!-- 功能按钮 -->
				<VipButtonGroup v-else-if="drag.name === 'btn2'" :styles="drag.styles"></VipButtonGroup>
				<VipCard v-else-if="drag.name === 'vipCard'" :styles="drag.styles"></VipCard>
				<personalOrder v-else-if="drag.name === 'personalOrder'" :styles="drag.styles"></personalOrder>
				<myChannel v-else-if="drag.name === 'myChannel'" :styles="drag.styles"></myChannel>
			</block>
		<footc v-if="layout.list"></footc>
		</scroll-view>
		<Load :show="showLoad"></Load>
	</view>
</template>

<script>
	import {
		mapState,
	} from 'vuex'
	import Vip from "./drag/Vip.vue"
	import VipButtonGroup from "./drag/VipButtonGroup.vue"
	import CustomSwiper from "./drag/CustomSwiper.vue"
	import ButtonGroup from "./drag/ButtonGroup.vue"
	import ImageGroup from "./drag/ImageGroup.vue"
	import TitleBlock from "./drag/TitleBlock.vue"
	import BlankBlock from "./drag/BlankBlock.vue"
	import LineBlock from "./drag/LineBlock.vue"
	import RichText from "./drag/RichText.vue"
	import Bulletin from "./drag/Bulletin.vue"
	import HotBlock from "./drag/HotBlock.vue"
	import MargicBlock from "./drag/MargicBlock.vue"
	import VipCard from "./drag/VipCard.vue"
	import personalOrder from "./drag/personalOrder.vue"
	import myChannel from "./drag/myChannel.vue"
	import footc from '@/components/common/footc.vue'
	export default {
		components:{
			Vip,VipButtonGroup,CustomSwiper,ButtonGroup,ImageGroup,TitleBlock,BlankBlock,LineBlock,RichText,Bulletin,HotBlock,MargicBlock,VipCard,personalOrder,myChannel,footc
		},
		data(){
			return {
				isInit:false,
				showLoad:true,
			}
		},
		computed:{
			...mapState({
				layout:state=>state.layout.personalcenter.body
			}),
			customeColor(){
				if(!this.layout)return ''
				let drag = this.layout.list.find(drag=>drag.name === 'vip')
				if(!drag)return ''
				if(drag.styles.bgType=='1'){
					if(drag.styles.gradient.model){
						return `linear-gradient(${drag.styles.gradient.deg}deg,${drag.styles.gradient.startColor},${drag.styles.gradient.endColor})`
					}else{
						return drag.styles.colorBg || this.tColor
					}
				}
				else{
					return `url(${drag.styles.img})`
				}
			},
			customefontColor(){
				if(!this.layout)return ''
				let drag = this.layout.list.find(drag=>drag.name === 'vip')
				if(!drag)return ''
				return drag.styles.colorWord || this.tColor
			},
			customeBg(){
				if(!this.layout)return false
				let drag = this.layout.list.find(drag=>drag.name === 'vip')
				if(!drag)return false
				if(drag.styles.bgType=='2'){
					return true
				}else{
					return false
				}
			}
		},
		methods:{
			async init(){
				this.refreshUser()
				if(this.isInit)return
				await this.getLayout({
					page: 'personalcenter',
					id: '2'
				})
				this.isInit = true
				this.showLoad = false
			}
		}
	}
</script>

<style lang="scss" scoped>
	.card{
		background: #ffffff;
		border-radius: 20rpx;
		padding: 20rpx;
		margin-top:20rpx ;
	}
	.title{
		font-size: 28rpx;
		font-weight: bold;
	}
</style>
