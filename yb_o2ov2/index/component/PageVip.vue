<template>
	<view class="wh" style="background: #f5f5f5;">
		<!-- navigation -->
		<view class="t-c p-f w100 bf" style="z-index: 2;top: 0;"
			:style="{height:`${statusNavBarHeight}px`,paddingTop:`${menuButtonTop}px`,background:payVipset.color || tColor,color:fontColor}">
			<text class="f32 wei">会员中心</text>
		</view>
		
		
		<block v-if="user.isVip">
			<scroll-view scroll-y="true" class="wh" :style="{paddingTop:`${statusNavBarHeight}px`}">
				<!-- 上半部 -->
				<view class="bf pt20" style="border-radius: 0 0 40rpx 40rpx;">
					<!-- 会员文案 -->
					<!-- <view v-if="false" class="p05 f34 wei">
						<view>亲爱的会员，</view>
						<view><text style="color: #FF4D3A;">5天后</text>您的会员到期，<text
								style="color: #FF4D3A;">提前</text>续费优惠不停享</view>
					</view> -->
					<!-- 会员卡 -->
					<view class="mla p3 posi-r" style="width: 700rpx;border-radius: 30rpx;">
						<view class="bg-img">
							<view class="wh"><image class="wh" :src="payVipset.vipBg" mode="widthFix"></image></view>
						</view>
						<view class="f-y-c">
							<view class="bsf mr30" style="width: 100rpx;height: 100rpx;">
								<image class="wh" :src="user.portrait || '/static/no.png'" mode=""></image>
							</view>
							<view class="f24" v-if="isLogin">
								<view class="f36 wei" v-if="user.userTel">{{user.userTel.replace(/(\d{3})\d{4}(\d{4})/,'$1****$2')}}</view>
								<view>
									<text>{{ffhysj.vipEndTime}}到期</text>
									<text class="iconfont iconinto f16 mr10" style="margin-top: -4rpx;"></text>
									|
									<text class="ml10"
										@click="go('navigateTo',`/yb_o2ov2/vip/buyvip-detailed-list`)">购买记录</text>
								</view>
							</view>
						</view>
						<view class="f-x-bt f24 wei mt60">
							<view>累计已省<text class="f48 wei">{{ffhysj.alreadyMoney || 0}}</text><text class="f34">元</text>
								<!-- <text class="iconfont iconinto f18"></text> -->
							</view>
							<view class="bs10 f24" style="padding: 14rpx 24rpx;background: rgba(245,245,245,.4);" @click="go('navigateTo',`/yb_o2ov2/vip/vip-plans`)">立即续费
							</view>
						</view>
					</view>
				<!-- 	<view class="w100" style="height: 300rpx">
						<image class="wh" :src="payVipset.vipBg" mode="aspectFill"></image>
					</view> -->
					<!-- 会员四大权益 -->
					<view class="p2">
						<view class="f-c mb30" v-if="ffhysj.rightsCount">
							<view class="bsf"
								style="width: 20rpx;height: 20rpx;background: #FCF5BE;margin-right: -10rpx;"></view>
							<view class="bsf mr10" style="width: 20rpx;height: 20rpx;background: #FDE98A;"></view>
							<view class="wei f34">会员{{ffhysj.rightsCount}}大专属权益</view>
							<view class="bsf ml10" style="width: 20rpx;height: 20rpx;background: #FDE98A;z-index: 1;">
							</view>
							<view class="bsf"
								style="width: 20rpx;height: 20rpx;background: #FCF5BE;margin-left: -10rpx;"></view>
						</view>
						<view class="f-y-c o-x-s" v-if="ffhysj.rightsList">
							<view class="f-c-c f-s-0 mr50" v-for="(item,index) in ffhysj.rightsList" :key="index">
								<view calss="bs10" style="width: 80rpx;height: 80rpx;">
									<image class="wh" :src="item.icon || '/static/img_vip/1.png'" mode=""></image>
								</view>
								<view class="wei mt10">{{item.name}}</view>
								<view class="c6 f24 t-o-e">{{item.interests}}</view>
							</view>
			<!-- 				<view class="f-c-c f-s-0 mr50">
								<view calss="bs10" style="width: 80rpx;height: 80rpx;">
									<image class="wh" src="/static/img_vip/2.png" mode=""></image>
								</view>
								<view class="wei mt10">兑换商店红包</view>
								<view class="c6 f24">5立省更多</view>
							</view>
							<view class="f-c-c f-s-0 mr50">
								<view calss="bs10" style="width: 80rpx;height: 80rpx;">
									<image class="wh" src="/static/img_vip/3.png" mode=""></image>
								</view>
								<view class="wei mt10">加量包</view>
								<view class="c6 f24">低价卖红包</view>
							</view>
							<view class="f-c-c f-s-0 mr50">
								<view calss="bs10" style="width: 80rpx;height: 80rpx;">
									<image class="wh" src="/static/img_vip/4.png" mode=""></image>
								</view>
								<view class="wei mt10">会员日</view>
								<view class="c6 f24">专属折扣菜</view>
							</view> -->
						</view>
					</view>

				</view>
				<!-- 下半部start -->
				<view class="p02">
					<!-- 卡券 -->
					<view class="p2 mt30 bs20 bf" v-if="ffhysj.payVipCycleRebate && ffhysj.payVipCycleRebate.couponInfo">
						<view class="f34 wei f-x-bt">
							<view><text class="f42 mr10" style="color: #FF4D3A;">{{ffhysj.waitNum || 0}}</text>张可用</view>
							<!-- <view class="c6 f28 nowei">去查看<text class="iconfont iconinto f18 c9"></text></view> -->
						</view>
						<view class="flex o-x-s w100" style="height: 200rpx;">
							<view v-for="(item,index) in ffhysj.payVipCycleRebate.couponInfo" :key="index" class="mr20 p-r"
								style="width: 180rpx;flex-shrink: 0;">
								<!-- 遮罩 -->
								<view class="p-a wh" style="z-index: 1;"
									:style="{background:item.state!=2?'rgba(255,255,255,.4)':'' }"></view>
								<view class="p-a" style="top:60rpx;width: 180rpx;height: 140rpx;border-radius: 20rpx;"
									:style="{background:gradualColor('#ff7411')}">
									<view class="curve p-a p-a-xc">
										<view class="t-c" style="color: #FA4A34;">
											<view class="wei">
												<text class="f20">$</text>
												<text class="f40">{{item.money}}</text>
											</view>
											<view class="f24">无门槛</view>
										</view>
									</view>
									<view class="p-a p-a-xc cf" style="bottom: 10rpx;">{{item.state==2?'未使用':'已使用'}}</view>
								</view>
							</view>
						</view>
					</view>
					<!-- 选项卡 -->
					<view class="f-c p20 bf bs20 mla mt30 p28 mb20" style="width: 700rpx;">
						<view class="f-c-c f-1" :class="[active===1?'active':'inactive']" @click="active=1">
							<view class="title">会员商店</view>
							<view class="subtitle">最高省10元</view>
						</view>
						<view class="f-c-c icon f-1" :class="[active===2?'active':'inactive']" @click="active=2">
							<view class="title">加量包</view>
							<view class="subtitle">更多会员红包</view>
						</view>
						<!-- <view class="f-c-c" :class="[active===3?'active':'inactive']" @click="active=3">
							<view class="title">会员日</view>
							<view class="subtitle">专享折扣菜</view>
						</view> -->
					</view>
					<!-- 会员商店 -->
					<view v-if="active===1" class="mla bf bs20" style="width: 700rpx;">
						<view class="p2" v-if="vipdata.type5 && vipdata.type5.configData ">
							<view class="f28 wei">会员商店</view>
							<view class="flex w100 o-x-s mt20" v-if="vipdata.type5.configData.shopTable || vipdata.type5.configData.shopTable2">
								<view v-for="item in vipdata.type5.configData.storeType=='3'? vipdata.type5.configData.shopTable:vipdata.type5.configData.shopTable2" :key="item" class="mr20 f-c-c p-r bs10"
									style="flex-shrink: 0;width: 220rpx;height: 260rpx;background: #FAFAFA;" @click="go('navigateTo',`/yb_o2ov2/home/<USER>">
									<view class="bs20" style="width: 100rpx;height: 100rpx;margin-top: -20rpx;">
										<image class="wh" :src="item.icon" mode=""></image>
									</view>
									<view class="t-o-e wei c3 f26 f-c" style="width: 160rpx;">{{item.name}}</view>
									<!-- <view class="p-a f20 bs10 p-a-xc c6" style="top:10rpx;padding: 2rpx 10rpx;">买过<text
											style="color: #ff7411;">3</text>次</view> -->
									<view class="p-a w100 t-c f20"
										style="bottom: 0;background: #FEE3B6;padding: 10rpx 0;">{{item.categoryName}}
										<!-- <text style="color:#e85845 ;">立省7元</text> -->
									</view>
								</view>
							</view>
							<!-- <view class="f-y-c o-x-s mt20" v-if='vipdata.type5.configData.shopTable || vipdata.type5.configData.shopTable2'>
								<view class="mr10" v-for="item in vipdata.type5.configData.storeType=='3'? vipdata.type5.configData.shopTable:vipdata.type5.configData.shopTable2" :key="item"  
								style="width: 210rpx;" @click="go('navigateTo',`/yb_o2ov2/home/<USER>">
									<view class="p-r bs20" style="width: 210rpx;height: 190rpx;">
										<image class="wh" :src="item.icon || '/static/no.png'" mode=""></image>
										<view class="p-a b0 l0 f24"
											style="padding:0 8rpx;background: #FDE8B7;border-top-right-radius: 20rpx;color:#af804d;">{{item.categoryName}}
										</view>
									</view>
									<view class="wei mt10 t-o-e t-c">{{item.name}}</view>
								</view>
							</view> -->
						</view>
					</view>
					<!-- 加量包 -->
					<view v-else-if="active===2" class="mla" style="width: 700rpx;">
						<view class="w100 f24 p-10-20 c3" style="border-radius: 30rpx;background: #FFF3BF;" v-if="ffhysj.vipEndTime">
							请在会员有效期（{{ffhysj.vipEndTime}}）内购买和使用</view>
						<block v-if="vipdata.type2 && vipdata.type6.configData && vipdata.type2.configData.dayArr">
						<view v-for="item in vipdata.type2.configData.dayArr" :key="item" class="f-x-bt bf bs20 p2 mt10" style="padding-top: 60rpx;">
							<view class="p-r mr20" style="height: 120rpx;width: 240rpx;">
								<view class="p-a" style="top:0;width: 240rpx;height: 120rpx;border-radius: 20rpx;"
									:style="{background:gradualColor('#ff7411')}">
									<view class="curve-long p-a p-a-xc">
										<view class="t-c" style="color: #ff4c10;">
											<view class="t-c">
												<text class="f36 wei">{{vipdata.type2.configData.money}}</text>
												<text class="f30 wei">元</text>
												<text class="f28 m01" style="color: #fa7b40 ;"> x </text>
												<text class="f36 wei">{{item.sheets}}</text>
												<text class="f30 wei">张</text>
											</view>
											<view class="f24">无门槛</view>
										</view>
									</view>
									<view class="p-a p-a-xc cf f24 w100 t-c " style="bottom: 0rpx;">可兑换商店红包</view>
								</view>
							</view>
							<view class="f-col f-g-1 wei f28" v-if="item.sheets && vipdata.type2.configData.money">
								<view>${{Number(item.sheets)*Number(vipdata.type2.configData.money)}}元加量包</view>
								<view style="color:#ff4c10 ;"><text class="f20">$</text><text class="f36">{{item.money}}</text>
								</view>
							</view>
							<view class="wei bs10" style="padding: 8rpx 30rpx;"
								:style="{background:tColor,color:fontColor}"  @click="ljkt(item.sheets,item.money)">购买</view>
						</view>
						</block>
					</view>
					<!-- 会员日 -->
					<view v-else-if="active===3" class="mla bs20 bf p2" style="width: 700rpx;">
						<view class="bs10" style="background: #FCF5BE;">
							<view class="p28" style="color: #fa713b;">
								<view class="f36 wei">会员专属优惠</view>
								<view class="f26"><text>专享折扣菜 / 大额商店红包 / 惊喜福利</text></view>
							</view>
						</view>
					</view>
				</view>
				<!-- 下半部end -->
			</scroll-view>
		</block>
		<!-- 非会员 -->
		<block v-else-if="!user.isVip">
			<scroll-view scroll-y class="wh" @scroll="onScroll">
				<view class="w100 p-r" style="background:linear-gradient(180deg, #757a80 10%, #cecece);height: 1000rpx;">
					<view class="p2 m20 p-a p-a-xc b0" style="width: 710rpx;height: 830rpx;padding-top: 40rpx;">
						<!-- 背景 -->
						<image class="p-a wh t0 l0" style="z-index: -1;" src="/static/img_vip/no_vip/bg.png"></image>
						<view class="color-text f36">{{payVipset.name}}</view>
						<view class="title"  v-if="vipdata.type6 && vipdata.type6.configData && vipdata.type6.configData.stock && vipdata.type6.configData.money">
							<text class="wei f30 color-text">每月至少省<text class="color-r mr20">${{Number(vipdata.type6.configData.stock)*Number(vipdata.type6.configData.money)}}</text>享{{ffhysj.rightsCount}}大专属权益</text>
						</view>
						<view class="f-y-c mt20 o-x-s" v-if="vipdata.type6 && vipdata.type6.configData && vipdata.type6.configData.stock">
							<view class="bf  bs10 f-c-c f-s-0 mr10" v-for="i in Number(vipdata.type6.configData.stock)" :key="i">
								<view class="f-c-c p-r"
									style="background:linear-gradient(-45deg, #F3D28F, #F3D28F88);width:160rpx;height:120rpx">
									<view class="t">无门槛</view>
									<view class="f-y-c mt20" style="color:#af804d">
										<text class="f24">$</text>
										<text class="f42">{{vipdata.type6.configData.money}}</text>
									</view>
								</view>
							</view>
						</view>
									
						<view class="f-c f-w mt20"  v-if="ffhysj.rightsList">
							<view class="f-y-c  mt20" style="width: 310rpx;" v-for="(item,index) in ffhysj.rightsList" :key="index">
								<view class="mr10 f-0" style="width: 90rpx;height: 90rpx;">
									<image class="wh" :src="item.icon || '/static/img_vip/no_vip/1-2.png'" mode=""></image>
								</view>
								<view class="f-col f26 f-1">
									<text class="color-text wei">{{item.name}}</text>
									<text class="c9 t-o-e">{{item.interests}}</text>
								</view>
							</view>
		<!-- 					<view class="f-y-c  mt20" style="width: 310rpx;">
								<view class="mr10" style="width: 90rpx;height: 90rpx;">
									<image class="wh" src="/static/img_vip/no_vip/1-3.png" mode=""></image>
								</view>
								<view class="f-col f26">
									<text class="color-text wei">红包升级</text>
									<text class="c9">升级大额店铺红包</text>
								</view>
							</view>

							<view class="f-y-c  mt20" style="width: 310rpx;">
								<view class="mr10" style="width: 90rpx;height: 90rpx;">
									<image class="wh" src="/static/img_vip/no_vip/1-4.png" mode=""></image>
								</view>
								<view class="f-col f26">
									<text class="color-text wei">加量红包</text>
									<text class="c9">超值优惠购买</text>
								</view>
							</view>
							<view class="f-y-c  mt20" style="width: 310rpx;">
								<view class="mr10" style="width: 90rpx;height: 90rpx;">
									<image class="wh" src="/static/img_vip/no_vip/1-1.png" mode=""></image>
								</view>
								<view class="f-col f26">
									<text class="color-text wei">海量特价菜品</text>
									<text class="c9">超级吃货专享</text>
								</view>
							</view> -->
						</view>

						<!-- <view class="vip-btn mt60" @click="go('navigateTo','/yb_o2ov2/vip/vip-plans')">
							<text>连续包月$</text>
							<text class="f48">10</text>
							<text>/月</text>
							<text class="ml20 f40">立即开通</text>
						</view> -->
						<view class="vip-btn mt60" @click="ktdl" v-if="payVipset.dayArr">
							<text>$</text>
							<text class="f48">{{first==1 && ffhysj.state==1?firstMoney:money}}</text>
							<text class="ml20 f40">立即开通{{title}}</text>
						</view>
					</view>
				</view>
				<view id="vip" class="w100 bf5 p2 p-r" style="border-radius: 20rpx 20rpx 0 0;margin-top: -20rpx;">
					<!-- 特权1 -->
					<view class="power-card" v-if="vipdata.type6 && vipdata.type6.configData">
						<view class="title">
							<text class="tip">特权1</text>
							<text v-if="vipdata.type6.configData.stock && vipdata.type6.configData.money">每月送<text class="color-r">${{Number(vipdata.type6.configData.stock)*Number(vipdata.type6.configData.money)}}</text >会员红包</text>
						</view>
						<view class="f-y-c mt20 o-x-s" v-if="vipdata.type6.configData.stock && vipdata.type6.configData.money">
							<view class="bf  bs10 f-c-c f-s-0 mr10" v-for="i in Number(vipdata.type6.configData.stock)" :key="i">
								<view class="f-c-c p-r"
									style="background:linear-gradient(-45deg, #F3D28F, #F3D28F88);width:160rpx;height:120rpx">
									<view class="t">无门槛</view>
									<view class="f-y-c mt20" style="color:#af804d">
										<text class="f24">$</text>
										<text class="f42">{{vipdata.type6.configData.money}}</text>
									</view>
								</view>
							</view>
						</view>
					</view>
					<!-- 特权2 -->
					<view class="power-card"  v-if="vipdata.type5 && vipdata.type5.configData">
						<view class="title">
							<text class="tip">特权2</text>
							<text>专享会员价</text>
						</view>
						<view class="flex w100 o-x-s mt20" v-if="vipdata.type5.configData.shopTable || vipdata.type5.configData.shopTable2">
							<view v-for="(item,index) in vipdata.type5.configData.shopTable || vipdata.type5.configData.shopTable2" :key="index" class="mr20 f-c-c p-r bs10"
								style="flex-shrink: 0;width: 220rpx;height: 260rpx;background: #FAFAFA;" @click="go('navigateTo',`/yb_o2ov2/home/<USER>">
								<view class="bs20" style="width: 100rpx;height: 100rpx;margin-top: -20rpx;">
									<image class="wh" :src="item.icon" mode=""></image>
								</view>
								<view class="t-o-e wei c3 f26 f-c" style="width: 160rpx;">{{item.name}}</view>
							<!-- 	<view class="p-a f20 bs10 p-a-xc c6" style="top:10rpx;padding: 2rpx 10rpx;">买过<text
										style="color: #ff7411;">3</text>次</view> -->
								<view class="p-a w100 t-c f20"
									style="bottom: 0;background: #FEE3B6;padding: 10rpx 0;">{{item.categoryName}}
									<!-- <text style="color:#e85845 ;">立省7元</text> -->
								</view>
							</view>
						</view>
						<!-- <view class="f-y-c o-x-s mt20" v-if='vipdata.type5.configData.shopTable || vipdata.type5.configData.shopTable2'>
							<view class="mr10" v-for="(item,index) in vipdata.type5.configData.shopTable || vipdata.type5.configData.shopTable2" :key="index" style="width: 210rpx;">
								<view class="p-r bs20" style="width: 210rpx;height: 190rpx;">
									<image class="wh" :src="item.icon || '/static/no.png'" mode=""></image>
									<view class="p-a b0 l0 f24"
										style="padding:0 8rpx;background: #FDE8B7;border-top-right-radius: 20rpx;color:#af804d;">{{item.categoryName}}
									</view>
								</view>
								<view class="wei mt10 t-o-e t-c">{{item.name}}</view>
							</view>
						</view> -->
					</view>
					<!-- 特权3 -->
					<view class="power-card"  v-if="vipdata.type4 && vipdata.type4.configData">
						<view class="title">
							<text class="tip">特权3</text>
							<text>免配红包天天送</text>
						</view>
						<view class="f-x-bt mt20">
							<view class="p-r" style="width: 290rpx;">
								<image class="wh" src="/static/img_vip/no_vip/1-5.png" mode="widthFix"></image>
								<view class="p-a t-c l0 w100" style="top: 100rpx;">
									<text>免配红包</text>
									<text class="color-r" v-if="vipdata.type4.configData.stock">x{{vipdata.type4.configData.stock}}</text>
								</view>
							</view>
							<view class="p-r" style="width: 290rpx;color:#af804d">
								<image class="wh" src="/static/img_vip/no_vip/1-6.png" mode="widthFix"></image>
								<view class="p-a f-c" style="top: 36rpx;right: 36rpx;" v-if="vipdata.type4.configData.money && vipdata.type4.configData.stock">
									<text>$</text>
									<text class="f42">{{vipdata.type4.configData.money}}</text>
									<text class="m01">x</text>
									<text class="f42">{{vipdata.type4.configData.stock}}</text>
									<text>张</text>
								</view>
								<view class="p-a f22" style="top: 6rpx;right: 76rpx;">
									<span v-if="vipdata.type4.configData.isFull=='1'">无门槛</span>
									<span v-else>有门槛</span>
								</view>
							</view>
						</view>
					</view>
					<!-- 特权4 -->
					<view class="power-card" v-if="vipdata.type3">
						<view class="title">
							<text class="tip">特权4</text>
							<text>会员红包可升级<text class="color-r">$6-11</text>店铺大额红包</text>
						</view>
						<view class="mt20">
							<view class="w100 p-r">
								<image class="wh" src="/static/img_vip/no_vip/1-7.png" mode="widthFix"></image>
								<view class="p-a f-c-c" style="color:#af804d;top: 20rpx;left:40rpx">
									<view class="flex">
										<text>$</text>
										<text class="f42" style="margin-top: -10rpx;">5</text>
									</view>
									<view>会员红包</view>
								</view>
								<view class="p-a f-c-c cf" style="top: 10rpx;right: 100rpx;">
									<view class="f42 flex">
										<text class="f28" style="padding-top: 10rpx;">$</text>
										<text>6</text>
										<text>-</text>
										<text>11</text>
									</view>
									<view>店铺大额红包</view>
								</view>
								<!-- 可用商户 -->
<!-- 								<view class="f-x-bt f-w">
									<view class="store-card f-c-ac pt10" v-for="item in 6" :key="item">
										<view class="bs20" style="width: 140rpx;height: 140rpx;">
											<image class="wh" src="/static/no.png" mode=""></image>
										</view>
										<view class="t-o-e" style="width: 140rpx;">无敌棒棒好吃店</view>
										<view class="f-y-c color-r">
											<text>$</text>
											<text class="f42 mr10">12</text>
											<text style="color:#af804d">无门槛</text>
										</view>
									</view>
								</view> -->
							</view>
						</view>
					</view>
					<!-- 特权5 -->
					<view class="power-card" v-if="vipdata.type6 && vipdata.type6.configData && vipdata.type2 && vipdata.type2.configData">
						<view class="title">
							<text class="tip">特权5</text>
							<text>超值优惠购买加量红包</text>
						</view>
						<view class="f-y-c mt20 o-x-s" v-if="vipdata.type2.configData.dayArr">
							<view v-for="(item,index) in vipdata.type2.configData.dayArr" :key="item" class="p-r mr20  f-s-0" style="width: 230rpx;color:#834E1E">
								<image class="wh" src="/static/img_vip/no_vip/1-8.png" mode="widthFix"></image>
								<view class="p-a f22" style="top: 6rpx;right: 76rpx;">无门槛</view>
								<view class="p-a f-c" style="top: 36rpx;right: 36rpx;">
									<text class="wei">$</text>
									<text class="f42 wei">{{vipdata.type6.configData.money}}</text>
									<text class="m01">x</text>
									<text class="f42 wei">{{item.sheets}}</text>
									<text>张</text>
								</view>
								<view class="p-a f-c" style="bottom:20rpx;right: 36rpx;">
									<text>售价</text>
									<text class="color-r">$</text>
									<text class="color-r f30 wei">{{item.money}}</text>
									<text>起</text>
								</view>
							</view>
						</view>
					</view>
					<!-- 特权6 -->
					<view class="power-card" v-if="vipdata.type1 && vipdata.type1.configData">
						<view class="title">
							<text class="tip">特权6</text>
							<text>专属客服，一对一在线解答</text>
						</view>
						<view class="f-x-bt mt20">
							<view class="p-r" style="width: 290rpx;">
								<image class="wh" :src="vipdata.type1.configData.icon" v-if="vipdata.type1.configData.icon" mode="widthFix" style="width: 150rpx;height: 150rpx;"></image>
								<!-- <view class="p-a t-c l0 w100" style="top: 100rpx;">
									<text>吃货豆</text>
									<text class="color-r">x5</text>
								</view> -->
							</view>
							<!-- <view class="p-r" style="width: 290rpx;color:#af804d">
								<image class="wh" src="/static/img_vip/no_vip/1-6.png" mode="widthFix"></image>
								<view class="p-a f-c" style="top: 36rpx;right: 36rpx;">
									<text>$</text>
									<text class="f42">5</text>
									<text class="m01">x</text>
									<text class="f42">2</text>
									<text>张</text>
								</view>
								<view class="p-a f22" style="top: 6rpx;right: 76rpx;">无门槛</view>
							</view> -->
						</view>
					</view>
				</view>
				<view style="width: 1rpx;height: 220rpx;"></view>
			</scroll-view>
			<view class="posi-s f-c bf" style="transition: .2s ease-in-out;height: 180rpx;" v-if="payVipset.dayArr"
			:style="{bottom:showVipBtn?'0':'-180rpx'}" @click="ktdl">
				<!-- <view class="vip-btn" style="width: 670rpx;">
					<text>连续包月$</text>
					<text class="f48">10</text>
					<text>/月</text>
					<text class="ml20 f40">立即开通</text>
				</view> -->
				<view class="vip-btn" style="width: 670rpx;">
					<text>$</text>
					<text class="f48">{{first==1 && ffhysj.state==1?firstMoney:money}}</text>
					<text class="ml20 f40">立即开通{{title}}</text>
				</view>
			</view>
		</block>
		<Load :show="showLoad"></Load>
	</view>
</template>

<script>
	import {
		mapState,
		mapActions
	} from 'vuex'
	import utils from '@/common/utils.js'
		import mgImg from '@/components/common/mg-img.vue'
	export default {
		comments:{
			mgImg,
		},
		props: {
			isVip: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				active: 1,
				top:'',//判断底部开通会员按钮是否出现
				showVipBtn:false,
				ffhysj: {},
				money:'',
				first:'',
				firstMoney:'',
				title:'',
				vipdata:{},
				showLoad:true,
			}
		},
		computed: {
			gradualColor() {
				return function(color) {
					return utils.gradualColor(color, 30, '-90deg')
				}
			},
			...mapState({
				payVipset: state => state.config.payVipset,
				user: state => state.user,
			}),
		},
		async onLoad(){
			await Promise.all([this.getSystem(),
				this.getLoginInfo()
			])
			this.refreshUser({
				nomask: 1,
				get: 1,
				now: 1,
			})
			this.getPayConfig()
		},
		methods: {
			...mapActions(['getConfig']),
			async getPayConfig(){
				await this.getConfig({
					name: 'payVipset',
					api: this.api.config,
					data: {
						ident: 'payVip'
					}
				})
				this.showLoad = false
				if(this.payVipset.dayArr){
					this.money = this.payVipset.dayArr[0].money || ''
					this.first = this.payVipset.dayArr[0].first || ''
					this.firstMoney = this.payVipset.dayArr[0].firstMoney || ''
					this.title = this.payVipset.dayArr[0].title || ''
				}
			},
			init(){
				this.showLoad = true
				this.$nextTick(()=>{
					this.getVipShowDot()
					this.vipint()
				})
			},
			async vipint(){
				await Promise.all([this.getSystem(),
					this.getLoginInfo()
				])
				this.refreshUser({
					nomask: 1,
					get: 1,
					now: 1,
				})
				this.getDjlb()
				this.getPayConfig()
			},
			async getDjlb() {
				let res = await Promise.all([this.util.request({
					'url': this.api.vipData,
				}), this.getHysj()])
				this.vipdata = res[0].data
			},
			async getHysj() {
				let res = await this.util.request({
					'url': this.api.ffhysj,
				})
				this.ffhysj = res.data
			},
			onScroll(e){
				let top = e.detail.scrollTop
				if(top>this.top){
					this.showVipBtn = true
				}else{
					this.showVipBtn = false
				}
			},
			getVipShowDot() {
				let query = uni.createSelectorQuery().in(this);
				query.select('#vip').boundingClientRect((res) => {
					if(res&&res.top){
						this.top = res.top
					}
					console.log('res',res)
				}).exec()
			},
			ktdl(){
				if(!this.isLogin){
					this.go('navigateTo',`/yb_o2ov2/my/login`)
				}
				this.go('navigateTo','/yb_o2ov2/vip/vip-plans')
			},
			ljkt: utils.throttle(async function(e,m) {
				let day = e,money = m
				// if (!this.gmxz) {
				// 	return this.util.message('请阅读并同意购买须知', 3)
				// }
				// if (this.loading) return
				this.loading = true
				let orderRes = await this.util.request({
					'url': this.api['vipCouponOrder'],
					method: 'POST',
					mask: '下单中',
					data: {day,},
				})
				if (orderRes.code==1) {
					let data = {}
					data.orderId = orderRes.data
					data.orderType = 8,
					data.money = money,
					this.setPayInfo(data)
					this.go('redirectTo', '/yb_o2ov2/home/<USER>/index')
					this.loading = false
				} else {
					this.loading = false
					return this.util.message(orderRes.msg || orderRes.data, 3, 2000)
				}
			}, 1000)
		},
	}
</script>

<style lang="scss" scoped>
	.store-card {
		width: 210rpx;
		height: 250rpx;
		background: #FFF3DE;
		border-radius: 10rpx;
		margin-top: 20rpx;
		position: relative;

		&:after {
			content: '';
			display: block;
			position: absolute;
			width: 20rpx;
			height: 20rpx;
			left: -10rpx;
			bottom: 55rpx;
			background: #FFFFFF;
			border-radius: 50%;
		}

		&:before {
			content: '';
			display: block;
			position: absolute;
			width: 20rpx;
			height: 20rpx;
			right: -10rpx;
			bottom: 55rpx;
			background: #FFFFFF;
			border-radius: 50%;
		}
	}

	.vip-btn {
		height: 100rpx;
		text-align: center;
		line-height: 100rpx;
		border-radius: 100rpx;
		color: #ffffff;
		background: linear-gradient(45deg, #FF6C53, #F7422D);
	}

	.t {
		position: absolute;
		top: 0;
		z-index: 1;
		font-size: 26rpx;
		width: 90rpx;
		height: 34rpx;
		padding: 2rpx 0;
		text-align: center;
		color: #fff;
		line-height: 30rpx;
		color: #AE6C28;
		background-color: #F6CE92;
		border-radius: 0 0 20rpx 20rpx;

		&:before {
			content: '';
			display: block;
			width: 20rpx;
			height: 34rpx;
			position: absolute;
			z-index: -1;
			transform: skewX(20deg);
			background: #F6CE92;
			border-bottom-left-radius: 14rpx;
			left: -6rpx;
			top: 0;
		}

		&:after {
			content: '';
			display: block;
			width: 20rpx;
			height: 34rpx;
			position: absolute;
			z-index: -1;
			transform: skewX(-20deg);
			background: #F6CE92;
			border-bottom-right-radius: 14rpx;
			top: 0;
			right: -6rpx;
		}
	}

	.color-r {
		color: #ff4c10
	}

	.color-text {
		color: #FFD19C;
	}

	.power-card {
		padding: 20rpx;
		margin-top: 20rpx;
		border-radius: 10rpx;
		background: #ffffff;

		.title {
			font-weight: bold;
			font-size: 26rpx;

			.tip {
				display: inline-block;
				margin-right: 10rpx;
				padding: 1rpx 8rpx;
				border-radius: 8rpx;
				background: linear-gradient(90deg, #313339bb, #313339);
				color: #f6d1a2
			}
		}
	}

	.curve {
		width: 160rpx;
		height: 80rpx;
		top: -20rpx;
		background: #FAFAE0;
		border-radius: 20rpx 20rpx 0 0;
	}

	.curve::after {
		content: '';
		width: 100%;
		height: 20rpx;
		position: absolute;


		left: 0;
		top: 100%;
		z-index: -1;
		border-radius: 0 0 50% 50%;
		background: #FAFAE0;
	}

	.curve-long {
		width: 220rpx;
		height: 80rpx;
		top: -20rpx;
		background: #FAFAE0;
		border-radius: 20rpx 20rpx 0 0;
		// border-bottom: 1px solid #FAFAE0;

	}

	.curve-long::after {
		content: '';
		width: 100%;
		height: 20rpx;
		position: absolute;

		left: 0;
		top: 100%;
		z-index: -1;
		border-radius: 0 0 50% 50%;
		background: #FAFAE0;
		border-top: 1px solid #FAFAE0;
	}

	.curve-long::before {
		content: '';
		width: 50%;
		height: 50rpx;
		position: absolute;


		left: 50%;
		top: -20rpx;
		z-index: -1;
		transform: translateX(-50%);
		border-radius: 50% 50% 0 0;
		background: #FAFAE0;
	}

	.active {
		width: 220rpx;

		.title {
			font-size: 36rpx;
			font-weight: bold;
		}

		.subtitle {
			background: #f5f6f9;
			border-radius: 20rpx;
			padding: 2rpx 12rpx;
			font-size: 20rpx;
		}
	}

	.inactive {
		width: 220rpx;

		.title {
			color: #8a8a8a;
			font-size: 28rpx;
			font-weight: bold;
		}

		.subtitle {
			color: #8a8a8a;
			font-size: 20rpx;
		}
	}

	.icon {
		position: relative;
	}

	.icon::after {
		content: '';
		width: 2rpx;
		height: 30rpx;
		position: absolute;

		left: 0;
		top: 20rpx;
		background: #ddd;
	}

	.icon::before {
		content: '';
		width: 2rpx;
		height: 30rpx;
		position: absolute;

		right: 0;
		top: 20rpx;
		background: #ddd;
	}

	.curve-swiper {
		position: relative;
		width: 220rpx;
		height: 260rpx;
		top: 40rpx;
		border-radius: 20rpx;
		background: #FEF8E0;
	}

	.curve-swiper::before {
		content: '';
		width: 50%;
		height: 50rpx;
		position: absolute;


		left: 50%;
		top: -20rpx;
		z-index: -1;
		transform: translateX(-50%);
		border-radius: 50% 50% 0 0;
		background: #FEF8E0;
	}

	.buy-button {
		width: 100%;
		height: 100rpx;
		text-align: center;
		font-weight: bold;
		border-radius: 20rpx;
		color: #fff;
		// background-size: 400% 400%;
		// background: linear-gradient(90deg,  #ffc700 0%, #e91e1e 100%);
		// animation: gradientBG 15s ease infinite;
	}

	@keyframes gradientBG {
		0% {
			background-position: 0% 50%;
		}

		50% {
			background-position: 100% 50%;
		}

		100% {
			background-position: 0% 50%;
		}
	}
	.bg-img {
		position: absolute;
		overflow: hidden;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
		z-index: -1;
		border-radius: 30rpx
	}
</style>
