<template>
	<view>
		<!-- 头部 start -->
		<view class="posi-s w100 " :style="{top:0,height:`${statusNavBarHeight}px`,paddingTop:`${menuButtonTop}px`,zIndex:3,background:'#fff'}">
			<!-- 返回 -->
			<view class="p-a" :style="{top:`${menuButtonTop}px`,left:`20rpx`,zIndex:4}" @click="go('back')">
				<text class="iconfont iconback f40"></text>
			</view>
			<view class="t-c f32 wei">
				<view>编辑评论</view>
			</view>
		</view>
		<!-- 头部 end -->
		<view class="p2">
			<view class="p2 bf bs20"> 
				<view class="f36 wei pb20" style="border-bottom: 1rpx solid #f5f6f9;">您对商店/商品满意吗？</view>
				<view class="p20 f-y-c">
					<view class="bs20 mr20" style="width: 80rpx;height: 80rpx;">
						<image class="wh" :src="storeIcon" mode="scaleToFill" style=""></image>
					</view>
					<view>{{storeName}}</view>
				</view>
				<!-- 打分 -->
				<view class="mt20 f-y-c">
					<view class="f28 wei mr20" style="width:60rpx">总体</view>
					<u-rate v-model="total" gutter="20" size="56" :active-color="tColor" inactive-color="#f0f0f0"></u-rate>
					<view class="f28 wei ml20">{{totalText}}</view>
				</view>
				<block v-if="total>0">
					<view class="mt20 f-y-c">
						<view class="c9 f22 mr20" style="width:60rpx">口味</view>
						<u-rate v-model="taste" gutter="36" size="40" :active-color="tColor" inactive-color="#f0f0f0"></u-rate>
						<view class="c9 f22 ml20">{{tasteText}}</view>
					</view>
					<view class="mt20 f-y-c">
						<view class="c9 f22 mr20" style="width:60rpx">包装</view>
						<u-rate v-model="pack" gutter="36" size="40" :active-color="tColor" inactive-color="#f0f0f0"></u-rate>
						<view class="c9 f22 ml20">{{packText}}</view>
					</view>
					<!-- 评论输入 -->
					<view class="p-r mt40">
						<textarea v-model="comment" type="textarea" :maxlength="maxlength" class="p-10-20 wh bfa f26" 
						  style="height:150rpx;border-radius: 20rpx 20rpx 0 0;"
						  placeholder-style="color:#999999" 
						 :placeholder="placeholder" />
						<!-- <view class="p-a" style="bottom: 20rpx;right: 25rpx;"><text class="c9">{{value.length || 0}}</text>/{{maxlength}}</view> -->
						<view class="wh p02 bfa f-y-c f-w images" style="border-radius: 0 0 20rpx 20rpx;">
							<!-- 图片 -->
							<view v-for="(item,index) in uploadImgArr" :key="index" class="image p-r mr10 bs10 mt10" style="width: 150rpx;height: 150rpx;">
								<image class="wh" :src="item" mode="aspectFill"></image>
							</view>
							<view @click="chooseImg" class="f-c-c bs10 mt10" style="border: 1rpx dashed #bbb;width: 150rpx;height: 150rpx;">
								<view class="t-c" style="width: 60rpx;height: 60rpx;"><text class="c9 wei f56 iconfont icontcamera"></text></view>
								<view class="c6 mt10" style="margin-bottom: -10rpx;">添加图片</view>
							</view>
						</view>
					</view>
				</block>
			</view>
			<button v-if="total!==0" class="btn" style="background:#F8C10D" type="default" @click="submit">提交评论</button>
		</view>
	</view>
</template>

<script>
	import {
		uploadImg
	} from '@/common/wechat-util.js'
	export default {
		data() {
			return {
				orderId:'',
				storeName:'',
				storeIcon:'',
				uploadImgArr:[],
				maxlength:220, //字数限制
				comment:'',
				total:0,
				totalText:'',
				taste:0,
				tasteText:'',
				pack:0,
				packText:'',
			}
		},
		watch:{
			total(val){
				switch(val){
					case 1 :
					this.totalText = '非常差'
					break;
					case 2 :
					this.totalText = '差'
					break;
					case 3 :
					this.totalText = '一般'
					break;
					case 4 :
					this.totalText = '满意'
					break;
					case 5 :
					this.totalText = '非常满意'
					this.taste = 5
					this.pack = 5
					break;
					default:break;
				}
			},
			taste(val){
				switch(val){
					case 1 :
					this.tasteText = '非常差'
					break;
					case 2 :
					this.tasteText = '差'
					break;
					case 3 :
					this.tasteText = '一般'
					break;
					case 4 :
					this.tasteText = '满意'
					break;
					case 5 :
					this.tasteText = '非常满意'
					break;
					default:break;
				}
			},
			pack(val){
				switch(val){
					case 1 :
					this.packText = '非常差'
					break;
					case 2 :
					this.packText = '差'
					break;
					case 3 :
					this.packText = '一般'
					break;
					case 4 :
					this.packText = '满意'
					break;
					case 5 :
					this.packText = '非常满意'
					break;
					default:break;
				}
			},
			comment(val){
				this.comment = val.substring(0,this.maxlength)
			}
		},
		computed:{
			placeholder(){
				if(this.pack>=3 && this.taste>=3){
					return '口味赞，包装好，推荐给大家'
				}else if(this.pack>=3 && this.taste<3){
					return '对口味不满意？可描述问题，帮助商店改善'
				}else if (this.pack<3 && this.taste>=3){
					return '包装质量差？可描述问题，帮助商店改善'
				}else{
					return '对口味、包装不满意？可描述问题，帮助商店改善'
				}
			}
		},
		onLoad(option) {
			
			try {
			  let result = JSON.parse(decodeURIComponent(option.orderInfo))
			  console.log(result)
			  this.orderId = result.orderId
			  this.storeName = result.storeName
			  this.storeIcon = result.storeIcon
			} catch (error) {
			  console.log(option)
			  this.orderId = option.orderId
			  this.storeName = option.storeName
			}


			this.getStatus();
		},
		onUnload() {
			//更新订单列表
			getCurrentPages()[0].$vm.$refs.initOrder.init()
		},
		methods:{
			async getStatus(){
				 await this.util.request({
					url: this.api.orderEvaluate,
					contentType:'application/json',
					method: 'get',
					data: {orderId:this.orderId}
				}).then(res=>{
					console.log(res)
					if(res.code==2){
						this.util.message('当前订单已评价', 3, 3000)
						setTimeout(() => {
							this.go('back')
						}, 1000)
					}
				})
				
				
			},
			chooseImg(){
				if(9-this.uploadImgArr.length === 0 ){
					this.util.message('最多上传9张图片', 3)
					return
				}
				uni.chooseImage({
					sizeType:['compressed'],
					count:9-this.uploadImgArr.length,
					success:res=>{
						this.uploadImgArr.push(...res.tempFilePaths)
					}
				})
			},
			async submit(){
				this.util.showLoading('上传中')
				let imgUrls = await uploadImg({
					files: this.uploadImgArr
				})
				let params = {
					anonymous: 1,
					orderId:this.orderId,
					media:imgUrls,
					body: this.comment,
					star: this.total,
					pack: this.pack,//打包
					taste:this.taste,//口味
				}
				await this.util.request({
					url: this.api.orderEvaluate,
					contentType:'application/json',
					method: 'POST',
					data: params
				})
				this.util.message('评论成功', 1, 1000)
				this.go('back')
				// this.go('redirectTo',`/yb_o2ov2/order/edit-comment-complete`)
			}
		}
	}
</script>

<style scoped lang="scss">
	.images {
		view:nth-child(4n){
			margin-right: 0;
		}
	}
	.btn{
		width: 700rpx;
		margin: 0 auto;
		margin-top: 80rpx;
	}
</style>
