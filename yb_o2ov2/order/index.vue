<template>
	<view class="page-wrapper">
		<view class="header-section" :style="{ paddingTop: `${statusNavBarHeight}px` }">
			<!-- Main tab for order types (店铺/供应商) -->
			<tab-nav gutter="80" :height="tabNavHeight" :activeColor="'#333'" fontSize="30"
				inactiveTextColor="#999" :current-index="aIdx" :list="tntabs" @change="typeTabChange"
				:isScroll="false" bgColor="transparent" :bold="true" :activeItemStyle="activeTypeTabStyle" :showBar="false"></tab-nav>
			<!-- Secondary tab for order statuses (待付款/待收货/退款售后/全部) -->
			<tab-nav gutter="80" :height="tabNavHeight" :activeColor="tColor" fontSize="30"
				inactiveTextColor="#666" :current-index="current" :list="tabs" @change="tabsChange"
				:isScroll="false" bgColor="#fff" :bold="true" :activeItemStyle="activeStatusTabStyle" :showBar="true"></tab-nav>
		</view>

		<scroll-view 
			scroll-y 
			class="order-list-scroll-view" 
			@scrolltolower="nextPage" 
			:scroll-top="scrollTop"
			:lower-threshold="50"
			:enable-flex="true"
			:show-scrollbar="false"
		>
			<view class="loading-placeholder">
				<loading-icon></loading-icon>
			</view>
			<view class="order-list-content">
				<view v-for="(item) in list" :key="item.id">
					<OrderBlock :orderInfo="item" :orderType="orderType" @operation="operation"></OrderBlock>
				</view>
			</view>
			<mescroll-empty v-if="list.length===0"
				:option="{icon:'/static/empty/9.png',tip:`~ ${$t('common.no_order')} ~`}"></mescroll-empty>
			<u-loadmore v-if="list.length!==0" @loadmore="nextPage" :status="status" />
			<footc></footc>
		</scroll-view>
		<TabBar :current="1"></TabBar>
	</view>
</template>

<script>
	import {
		mapState,
		mapActions
	} from 'vuex'
	import utils from '@/common/utils.js'
	import TabNav from '@/components/TabNav.vue'
	import OrderBlock from '@/components/OrderBlock.vue'
	import LoadingIcon from '@/components/LoadingIcon.vue'
	import footc from '@/components/common/footc.vue'
	import navTab from '@/components/common/nav-tab.vue'
	import dnOrder from '@/yb_o2ov2/index/component/dn-order.vue'
	import TabBar from "@/components/TabBar.vue"
	export default {
		components: {
			TabNav,
			OrderBlock,
			LoadingIcon,
			footc,
			navTab,
			dnOrder,
			TabBar
		},
		data() {
			return {
				refreshLoading: false,
				status: 'loading',
				tabNavHeight: 90,
				params: {
					page: 1,
					size: 10,
					state: '1', // 订单状态 1尚未付款,2已付款,3已接单,4外送中/待取货,5已收货,6已评价,7已取消,8.拒单，9申请退款,10已退款，11退款拒绝
				},
				list: [],
				bgColor: '#f5f5f5',
				current: 0,
				tabs: [{
					name: this.$t('order.oreder_obligation_text'),
					value: 1
				}, {
					name: this.$t('order.oreder_pending_text'),
					value: 2
				}, {
					name: this.$t('order.oreder_refund_text'),
					value: 3
				}, {
					name: this.$t('order.oreder_all_text'),
					value: 4
				}],
				orderInfo: {},
				orderType: 'store',
				toph: 0,
				taIdx: 0,
				aIdx: 0,
				laIdx: 0,
				labelArr: [],
				tntabs: [{
						name: this.$t('order.store'),
					},
					{
						name: this.$t('order.supplier'),
					},
				],
				activeTypeTabStyle: {
					backgroundColor: '#fff',
					boxShadow: '0 4rpx 10rpx rgba(0, 0, 0, 0.1)',
					borderRadius: '16rpx 16rpx 0 0',
					fontWeight: 'bolder',
					color: '#333' // Explicitly set color for selected item
				},
				activeStatusTabStyle: {
					fontWeight: 'bolder',
				},
				scrollTop: 0,
			}
		},
		onShow() {
			this.init()
		},
		methods: {
			dnrefresh(e) {
				if (e.hasOwnProperty('state')) {
					this.iparams.state = e.state
					this.isget = this.mygd = false
					this.params.page = 1
					this.list = []
					this.fetchData()
				} else {
					this.refresh();
				}
			},
			tabsChange(index) {
				switch (index) {
					case 0:
						this.params.state = '1'
						this.params.type = '1'
						break;
					case 1:
						this.params.state = '4'
						this.params.type = '1'
						break;
					case 2:
						this.params.state = '9'
						this.params.type = '3'
						break;
					case 3:
						this.params.state = ''
						this.params.type = '1'
				}
				this.current = index
				this.iparams = index + 1
				this.refresh()
			},
			typeTabChange(index) {
				switch(index) {
					case 0:
						this.orderType = 'store'
						this.taIdx = 0
						this.params.api = 'ddlb'
						break;
					case 1:
						this.orderType = 'supplier'
						this.taIdx = 1
						this.params.api = 'supplierOrder'
						break;
				}
				console.log(this.orderType);
				
				this.aIdx = index
				this.$nextTick(() => {
					this.scrollTop = 0;
				});
				this.params.page = 1;
				this.list = [];
				this.fetchData();
			},
			async searchM() {
				this.params.page = 1;
				this.list = [];
				this.fetchData();
			},
			async init() {
				let Scurrent = uni.getStorageSync('current') || 0
				this.taIdx = this.orderType === 'supplier' ?1:0
				this.params.api = this.orderType === 'supplier' ?'supplierOrder':'ddlb'
				if (Scurrent >= 0) {
					this.current = Scurrent;
					this.params.state = this.tabs[Scurrent].value;
				}
				this.params.page = 1;
				this.list = [];
				await this.fetchData();
				uni.setStorageSync('current', -1)
			},
			async refresh() {
				this.refreshLoading = true
				this.params.page = 1;
				this.list = [];
				await this.fetchData()
				setTimeout(() => {
					this.refreshLoading = false
				}, 500)
			},
			async fetchData() {
				this.status = 'loading'
				let params = {
					...this.params,
					page: this.params.page,
					size: this.params.size
				}
				
				let {data} = await this.util.request({
					'url': this.api[this.params.api],
					method: 'POST',
					data: params,
				});

				if (this.params.page === 1) {
					this.list = data;
				} else {
					this.list = this.list.concat(data);
				}

				if (data.length === 0 || data.length < this.params.size) {
					this.status = 'nomore';
				} else {
					this.status = 'loadmore';
				}
			},
			nextPage() {
				if (this.status === 'loading' || this.status === 'nomore') {
					return
				}
				this.params.page++
				this.fetchData()
			},
			async operation(e) {
				const { data: orderInfo, type: operationType } = e;
				this.orderInfo = orderInfo;

				// 定义操作映射表
				const operationMap = {
					applyRefund: {
						condition: () => orderInfo.state === 2,
						action: () => this.qxdd(1),
						getConfig: () => ({
							title: this.$t('good.confirm_tip'),
							url: this.orderType === 'supplier' ? 'supplierCancel' : 'wmddtk',
							params: {
								orderId: orderInfo.id,
								note: this.qxyy || '',
							}
						})
					},
					reminder: {
						getConfig: () => ({
							title: this.$t('good.follow_tip'),
							url: this.orderType === 'supplier' ? 'supplierWmddcd' : 'wmddcd',
							params: {
								userId: this.user.userId,
								orderId: orderInfo.id
							}
						})
					},
					confirmReceipt: {
						getConfig: () => ({
							title: this.$t('good.received_tip'),
							url: this.orderType === 'supplier' ? 'supplierWmddsh' : 'wmddsh',
							params: {
								orderId: orderInfo.id
							}
						})
					},
					deleteOrder: {
						getConfig: () => ({
							title: this.$t('good.delet_tip'),
							url: this.orderType === 'supplier' ? 'supplierDelOrder' : 'wmddsc',
							params: {
								orderId: orderInfo.id
							}
						})
					},
					contactStore: {
						action: () => this.util.makeTel(orderInfo.storeTel)
					},
					evaluate: {
						condition: () => this.orderType === 'supplier',
						action: () => {
							this.util.message('供应商类型', 3);
							return;
						},
						getConfig: () => ({
							action: () => this.go('navigateTo', `/yb_o2ov2/order/edit-comment?orderInfo=${
								encodeURIComponent(JSON.stringify({
									orderId: orderInfo.id,
									storeName: orderInfo.storeName,
									storeIcon: orderInfo.storeIcon
								}))
							}`)
						})
					},
					payTo: {
						action: () => {
							const data = {
								orderId: orderInfo.id,
								orderType: 1
							};
							this.setPayInfo(data);
							const path = this.orderType === 'supplier' 
								? '/yb_o2ov2/my/pay/index'
								: '/yb_o2ov2/home/<USER>/index';
							return this.go('navigateTo', path);
						}
					},
					againOrder: {
						action: () => {
							const path = this.orderType === 'supplier'
								? `/yb_o2ov2/my/supplier/store?storeId=${orderInfo.id}`
								: `/yb_o2ov2/home/<USER>
							return this.go('navigateTo', path);
						}
					}
				};

				const operation = operationMap[operationType];
				if (!operation) {
					console.warn(`未知的操作类型: ${operationType}`);
					return;
				}

				// 检查条件
				if (operation.condition && operation.condition()) {
					return operation.action();
				}

				// 直接执行的操作
				if (operation.action) {
					return operation.action();
				}

				// 需要确认的操作
				if (operation.getConfig) {
					try {
						const config = operation.getConfig();
						
						// 如果是直接执行的操作
						if (config.action) {
							return config.action();
						}

						// 需要确认的操作
						await this.util.modal(config.title);
						
						const response = await this.util.request({
							url: this.api[config.url],
							method: 'POST',
							mask: 1,
							data: config.params,
						});

						if (response) {
							await this.refresh();
							this.util.message('操作成功', 1);
						}
					} catch (error) {
						console.error('操作执行失败:', error);
						if (error !== 'cancel') {
							this.util.message('操作失败，请重试', 2);
						}
					}
				}
			},
			async qxdd(e) {
				if (e) {
					try {
						await this.util.modal('您确认取消订单吗？')
					} catch (e) {
						return
					}
				}
				let data = await this.util.request({
					'url': this.orderType === 'supplier'?this.api.supplierCancel:this.api.wmddqx,
					method: 'POST',
					mask: '取消订单中',
					data: {
						orderId: this.orderInfo.id,
						note: e ? this.qxyy || '' : '',
					},
				})
				if (data) {
					this.refresh();
					this.util.message('操作成功', 1)
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.page-wrapper {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f8f8f8;
	}

	.header-section {
		position: sticky;
		top: 0;
		z-index: 100;
		background-color: #ffffff;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
		border-bottom-left-radius: 20rpx;
		border-bottom-right-radius: 20rpx;
	}

	.order-list-scroll-view {
		flex: 1;
		height: 0;
		padding-top: 20rpx;
	}

	.loading-placeholder {
		position: absolute;
		top: -100rpx;
		left: 50%;
		transform: translateX(-50%);
	}

	// .order-list-content {
		// Contains the OrderBlock components, OrderBlock.vue already has margin: 0 auto 20rpx;
	// }
</style>