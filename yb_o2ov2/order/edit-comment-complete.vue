<template>
	<view>
		<!-- 头部 start -->
		<view class="p-f w100 " :style="{top:0,height:`${statusNavBarHeight}px`,paddingTop:`${menuButtonTop}px`,zIndex:3}">
			<!-- 返回 -->
			<view class="p-a" :style="{top:`${menuButtonTop}px`,left:`20rpx`,zIndex:4}" @click="go('back')">
				<text class="iconfont iconback f40"></text>
			</view>
			<view class="c0 t-c f-c">
				<text class="iconfont iconselect f32 mr10"></text>
				<text class="f32 wei">评价完成，获得22金豆</text>
			</view>
		</view>
		<!-- 头部 end -->
		<!-- 背景 -->
		<view class="p-a w100" style="top: 0;z-index: -1;height: 600rpx;" :style="{background:`linear-gradient(0, #fff0, ${tColor} 400rpx)`}">
		</view>
		<view class="p2" :style="{marginTop:`${statusNavBarHeight}px`}">
			<view class="bs20 bf p2">
				<view class="f-y-c">
					<view class="f30 mr20">帳戶评价</view>
					<u-rate v-model="rate" :disabled="true" size="38" active-color="#ff5454"></u-rate>
				</view>
				<view class="c3 mt30">“{{content}}”</view>
				<!-- 分享 -->
				<view class="f-y-c mt20">
					<view class="bfa bs10 p2 f-c mr20" style="width: 320rpx;">
						<u-icon name="weixin-fill" color="#00CB07" size="50"></u-icon>
						<text class="ml10">朋友圈</text>
					</view>
					<view class="bfa bs10 p2 f-c" style="width: 320rpx;">
						<u-icon name="weixin-fill" color="#00CB07" size="50"></u-icon>
						<text class="ml10">微信好友</text>
					</view>
				</view>
			</view>
		</view>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				rate:5,
				content:"便宜又好吃"
			}
		}
	}
</script>

<style scoped lang="scss">
	
</style>
