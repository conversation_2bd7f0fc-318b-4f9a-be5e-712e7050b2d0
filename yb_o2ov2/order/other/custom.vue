<template>
	<view class="wh">
	<!-- 	<view class="p-f" style="top:0;width: 100%;z-index: 3;">
			<view class="flex f-c"
				:style="{height:`${statusNavBarHeight}px`,paddingTop:`${menuButtonTop}px`,background:customeColor,color:customefontColor}">
				<text class="wei f32">自定义</text>
			</view>
			<view class="tabNav">
				<tab-nav gutter="80" :height="tabNavHeight" :activeColor="tColor" fontSize="30" inactiveTextColor="#666"
					:current-index="current" :list="tabs" @change="tabsChange"></tab-nav>
			</view>
		</view> -->
		<!-- <view class="p-f" style="top:0;width: 100%;z-index: 3;">
			<view class="flex bf"
				:style="{height:`${statusNavBarHeight}px`,paddingTop:`${menuButtonTop}px`}">
					<text class="mla wei f32">自定义</text>
			</view>
		</view> -->
		<scroll-view scroll-y="true" class="wh"
			style="background: #f5f5f5;"
			>
			<block v-for="(drag,index) in layout.list" :key="index">
				<!-- 固定定位-->
				<Position v-if="drag.name === 'fixed'" :styles="drag.styles" id="position" @storeListChangeFixedDot=storeListChangeFixedDot :positionFixedDot="positionFixedDot" :paddingTop="navBarHeight" :positionColorOpacity="positionColorOpacity" :handleScroll="handleScroll"></Position>
				<!-- 搜索  -->
				<Search v-else-if="drag.name === 'search'" :styles="drag.styles"></Search>
				<!-- 幻灯片  -->
				<CustomSwiper  v-else-if="drag.name === 'picLunbo'" :styles="drag.styles"></CustomSwiper>
				<!-- 图片组 -->
				<ImageGroup v-else-if="drag.name === 'pictures'" :styles="drag.styles"></ImageGroup>
				<!-- 标题栏 -->
				<TitleBlock v-else-if="drag.name === 'titles'" :styles="drag.styles"></TitleBlock>
				<!-- 辅助空白 -->
				<BlankBlock v-else-if="drag.name === 'blank'" :styles="drag.styles"></BlankBlock>
				<!-- 辅助线 -->
				<LineBlock v-else-if="drag.name === 'lines'" :styles="drag.styles"></LineBlock>
				<!-- 富文本 -->
				<RichText v-else-if="drag.name === 'book'" :styles="drag.styles"></RichText>
				<!-- 热区 -->
				<HotBlock v-else-if="drag.name === 'hot'" :styles="drag.styles"></HotBlock>
				<!-- 魔方 -->
				<MargicBlock v-else-if="drag.name === 'margic'" :styles="drag.styles"></MargicBlock>
				<!-- 按钮组  -->
				<ButtonGroup v-else-if="drag.name === 'btn'" :styles="drag.styles"></ButtonGroup>
				<!-- 公告  -->
				<Notice v-else-if="drag.name === 'notice'" :styles="drag.styles"></Notice>
				<!-- 告示  placard -->
				<!-- <placard></placard> -->
				<!-- 同城头条  Bulletin 列表导航 -->
				<Bulletin v-else-if="drag.name === 'listNav'" :styles="drag.styles"></Bulletin>
				<!-- 大牌臻选 -->
				<StoreSwiper v-else-if="drag.name === 'storeName'"  ref="storeswiper" :styles="drag.styles"></StoreSwiper>
				<!-- 推荐商店  -->
				<RecommendStore v-else-if="drag.name === 'recommend'" ref="recommend" :styles="drag.styles"></RecommendStore>
				<!-- 商店列表  -->
				<StoreList v-else-if="drag.name === 'storelist'" 
				id="storelist" ref="storelist"
				:styles="drag.styles" @changeTop="handleChangeTop"
				:paddingTop="navBarHeight" :storeListDot="storeListDot" 
				:storeListDotTop="storeListDotTop" :handleScroll="handleScroll"></StoreList>
				<!-- 商店列表  -->

				<myChannel v-else-if="drag.name === 'myChannel'" :styles="drag.styles"></myChannel>
			</block>
		<footc v-if="layout.list"></footc>
		</scroll-view>
		<Load :show="showLoad"></Load>
	</view>
</template>

<script>
	import {
		mapState,
	} from 'vuex'
	import CustomSwiper from "../../index/component/drag/CustomSwiper.vue"
	import ButtonGroup from "../../index/component/drag/ButtonGroup.vue"
	import ImageGroup from "../../index/component/drag/ImageGroup.vue"
	import TitleBlock from "../../index/component/drag/TitleBlock.vue"
	import BlankBlock from "../../index/component/drag/BlankBlock.vue"
	import LineBlock from "../../index/component/drag/LineBlock.vue"
	import RichText from "../../index/component/drag/RichText.vue"
	import Bulletin from "../../index/component/drag/Bulletin.vue"
	import HotBlock from "../../index/component/drag/HotBlock.vue"
	import MargicBlock from "../../index/component/drag/MargicBlock.vue"
	import StoreSwiper from "../../index/component/drag/StoreSwiper.vue"
	import RecommendStore from "../../index/component/drag/RecommendStore.vue"
	import StoreList from "../../index/component/drag/StoreList.vue"
	import footc from '@/components/common/footc.vue'
	export default {
		components:{
			CustomSwiper,ButtonGroup,ImageGroup,TitleBlock,BlankBlock,LineBlock,RichText,Bulletin,HotBlock,MargicBlock,StoreSwiper,RecommendStore,StoreList,footc
		},
		data(){
			return {
				isInit:false,//是否初始化过页面
				refreshLoading:false,
				changeTop:0,
				custom:true,
				positionFixedDot:0,
				positionColorOpacity:1,
				handleScroll:-1,
				storeListDot:0, //storeList 改吸顶定位的临界点
				storeListDotTop:0 //改吸顶后的定位的top = <position>的top+height
			}
		},
		computed:{
			...mapState({
				layout:state=>state.layout.custom.body,
				height:state=>state.systemInfo.MainPageHeight,
			}),
		},
		onLoad(options) {
			this.query = options
			this.xlsx(options)
		},
		onShow(){
			this.xlsx()
		},
		methods:{
			async hasRefs(ref,fn){
				if(this.$refs[ref]){
					await this.$refs[ref][0][fn]()
				}
			},
			async xlsx(options) {
				this.getSystem();
				await this.getLayout({
					page: 'custom',
					id: '3',
					pageId: this.query.pid,
				})
				await this.hasRefs('storeswiper','fetchData')
				await this.hasRefs('recommend','fetchData')
				await this.hasRefs('storelist','init')
				this.showloading = false
				this.shopinfo = this.storeInfo
				this.getLoginInfo({
					inviteId: this.query.userId
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.card{
		background: #ffffff;
		border-radius: 20rpx;
		padding: 20rpx;
		margin-top:20rpx ;
	}
	.title{
		font-size: 28rpx;
		font-weight: bold;
	}
</style>
