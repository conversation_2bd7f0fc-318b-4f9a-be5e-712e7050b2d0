<template>
	<view :style="{minHeight:viewHeight+'px'}">
		<view class="mt20 bf">
			<view class="f-y-c f30 p2 c3">
				<view>
					<text class="c9 mr10 iconfont iconpositionfill f30"></text>
				</view>
				<view class="f-g-1" @click.stop="navigation">
					<text class="c3">{{shopData.address}}</text>
				</view>
				<view class="t-c" style="width: 100rpx;" @click.stop="call">
					<text class="c9 iconfont iconphone f30"></text>
				</view>
			</view>
			<scroll-view scroll-x="true" style="width: 100%;" class="f-x-bt ws-n p02">
				<view v-for="(item,index) in shopData.environment" :key="item.name" class="dis-in mr20 bs10" style="width: 190rpx;height: 110rpx;"
				@click="checkImg(index)">
					<image class="wh" :src="item" mode="aspectFill" ></image>
				</view>
			</scroll-view>
			<u-cell-item  :title-style="{fontSize:'30rpx',color:'#333'}" title="查看食品安全档案"  @click.stop="navigation2">
				<text slot="icon" class="c9 mr10 iconfont icontaketalk f30"></text>
			</u-cell-item>
		</view>

		<view class="mt20 bf">
			<!-- <u-cell-item :arrow="false" :title-style="{fontSize:'30rpx',color:'#333'}" :title="title.deliveryMode">
				<text slot="icon" class="c9 mr10 iconfont icontakeout f30"></text>
			</u-cell-item> -->
			<u-cell-item :arrow="false" :title-style="{fontSize:'30rpx',color:'#333'}" :title="title.timeArr">
				<text slot="icon" class="c9 mr10 iconfont icontime f30"></text>
			</u-cell-item>
		</view>

		<view class="mt20 bf">
			<u-cell-item title-width="600"
				:title-style="{fontSize:'30rpx',color:'#333'}"
				:title="moreSet.notice">
				<text slot="icon" class="c9 mr10 iconfont iconnotice f30"></text>
			</u-cell-item>
			<u-cell-item :arrow="false">
				<text slot="icon" class="c9 mr10 iconfont icontaketalk f30"></text>
				<text slot="title" class="f30 c3 f-y-c">
					 商店服务：
					 <block>
						 <text class="bs10 f24 wei m02" style="border: 1rpx solid #9dd7fb;color:#9dd7fb;padding: 0 6rpx;margin-right: 5rpx;">跨</text>
						 跨天预订
					 </block>
					 <block v-if="moreSet.distributionSupport.includes('2')">
						 <text class="bs10 f24 wei m02" style="border: 1rpx solid #9dd7fb;color:#9dd7fb;padding: 0 6rpx;margin-right: 5rpx;">自</text>
						 到店自取（享优惠）
					 </block>
				 </text>
			</u-cell-item>
		</view>
	</view>

</template>

<script>
	export default {
		props: {
			tabNavHeight: { //tabNav高度单位rpx
				type: [String, Number],
				default: 80
			},
			shopData:{
				type:Object,
				default:()=>{}
			},
			moreSet:{
				type:Object,
				default:()=>{}
			},
		},

		data() {
			return {
				title:{}
			}
		},
		computed: {
			viewHeight() {
				return this.wHeight - this.statusNavBarHeight - this.tabNavHeight / this.pxToRpxRate
			}
		},
		watch:{
			shopData(){
				let storeIsOpen = false
				let result = ''
				try{
					if(this.shopData.timeType === 1){
						storeIsOpen = true
						result += this.$t('good.alltoday')
					}else{
						let date = new Date()
						let hours = date.getHours()
						let min = date.getMinutes()
						console.log('时间',hours,min)
						this.shopData.timeArr.forEach(item=>{
							let sTime = item.startTime.split(":")
							let eTime = item.endTime.split(":")
							//判断营业时间
							if(!item.ciri){//同一天
								if((hours>=sTime[0]&&min>=sTime[1])&&(hours<=eTime[0]&&min<=eTime[1])){
									storeIsOpen = true
								}else{
									storeIsOpen = false
								}
							}else{//次日
								
							}
							result += `${item.startTime}-${item.ciri?'次日':''}${item.endTime}  `
						})
					}
					
				}catch(e){
					console.log(e)
					result = ''
					//TODO handle the exception
				}
				this.title = {
					deliveryMode: this.$t('location.service', { mode: this.shopData.deliveryMode }),
					timeArr: this.$t('location.hours', { time: result }),
				}
			}
		},
		methods:{
			checkImg(index){
				uni.previewImage({
					urls:this.shopData.environment,
					current:index,
					indicator:true
				})
			},
			call(){
				uni.makePhoneCall({
				    phoneNumber: this.shopData.storeTel //仅为示例
				});
			},
			navigation(){
				let info = {
					lat:this.shopData.lat,
					lng:this.shopData.lng,
					name:this.shopData.name,
					address:this.shopData.address
				}
				this.util.ckWz(info)
			},
			navigation2(){
				this.go('navigateTo','/yb_o2ov2/home/<USER>' + encodeURIComponent(JSON.stringify(this.shopData)))
			},
		}
	}
</script>

<style>
</style>
