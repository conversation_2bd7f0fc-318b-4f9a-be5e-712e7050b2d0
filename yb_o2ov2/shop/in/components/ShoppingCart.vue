<template>
	<view>
		<!-- 弹窗部分 -->
		<u-popup v-model="show" mode="bottom" zIndex="1" border-radius="20">
			<view class="p-r" :style="{paddingBottom:isIpx?'140rpx':'100rpx',maxHeight:`${wHeight/2}px`,background:'#fff'}">
				<!-- title -->
				<view style="position: sticky;top: 0;width: 100%;z-index: 1;" :style="{background:'#fff'}">
					<!-- <view v-if="alomostMoney>0" class="t-c f24"
						style="height: 60rpx;line-height: 60rpx;background:#F3EDD5;">
						<text>还差</text>
						<text style="color:#ff5454">{{alomostMoney}}元</text>
						<text>就能起送</text>
						<text style="color:#ff5454">[去凑单]</text>
					</view> -->
					<view class="t-c f24" style="height: 60rpx;line-height: 60rpx;background:#F3EDD5;">
						<discount :reduceArr="reduceArr" :discount="discount"></discount>
					</view>
					<view class="p2 f-x-bt f24 c9" style="border-bottom: 1px solid #fafafa;">
						<view>
							<text class="c6">商品詳情</text>
							<!-- （<text class="c3">打包费</text>
							<text style="color: #FE624B;">${{cartInfo.boxMoney}}</text>
							） -->
						</view>
						<view class="f-y-c" @click="clear">
							<text class="iconfont icondelete f26 c6" style="margin-right: 5rpx;"></text>
							<text class="c6">清空購物車</text>
						</view>
					</view>
				</view>
				<!-- content -->
				<scroll-view scroll-y class="f-col p02" :style="{maxHeight:`${scrollHeight}px`}">
					<!-- 商品 -->
					<view v-for="(item,index) in cartInfo.data" :key="item.id" class="flex p20"
						style="border-bottom: 1px solid #fafafa;">
						<view style="width: 600rpx;" class="flex">
							<view class="mr20 bs10" style="width: 120rpx;height: 120rpx;">
								<image class="wh" :src="item.icon" mode=""></image>
							</view>
							<view>
								<view class="f28 wei">{{item.name}}</view>
								<view class="f20 c6" style="line-height: 20rpx;">
									<text v-if="item.groupName">{{item.groupName}}</text>
									<text v-if="item.groupName&&(item.attribute||item.materialName)">+</text>
									<text v-if="item.attribute">{{item.attribute}}</text>
									<text v-if="(item.groupName||item.attribute)&&item.materialName">+</text>
									<text v-if="item.materialName">{{item.materialName}}</text>
								</view>
								<view class="f28 mt15 wei" style="color: #FE624B;"><text
										class="f20">$</text>{{item.totalMoney}}</view>
							</view>
						</view>
						<view class="f-x-bt f-g-1">
							<view class="f-e-bt" style="width: 120rpx;">
								<view class="f-y-e pb10" style="height: 60rpx;" @click="removeCart(item)">
									<view class="bs10 f-c"
										style="height: 36rpx;width: 36rpx;text-align: center;"
										:style="{border: `1px solid ${shopGoodsInfo.categorySet.delColor}`}">
										<!-- 减号 -->
										<text class="iconfont iconminus f20 bs10 wei" style="padding: 4rpx 0;"
										:style="{color:shopGoodsInfo.categorySet.addColor2}"
										></text>
									</view>
								</view>
								<view style="padding-bottom: 5rpx;">{{item.num}}</view>
								<view class="f-y-e pb10" style="height: 60rpx;" @click="addCart(item)">
									<view class="bs10 f-c"
										style="height: 36rpx;width: 36rpx;text-align: center;"
										:style="{background: shopGoodsInfo.categorySet.delColor}">
										<!-- 加号 -->
										<text class="iconfont iconplus f20 bs10 wei" style="padding: 4rpx 0;"
										:style="{color: shopGoodsInfo.categorySet.addColor}"
										></text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</u-popup>
		<!-- 悬浮窗部分 -->
		<view class="cartBoxf24 p-r" :style="{height: isIpx?'140rpx':'100rpx',background:'#FEFEFE'}">
			<!-- 满减悬浮 -->
			<view v-if="!show && reduceArr" class="p-a p-a-xc bs20"
				style="width: 700rpx;height: 60rpx;background: rgba(249,247,226,.9);top:-60rpx;">
				<discount :reduceArr="reduceArr" :discount="discount"></discount>
			</view>

			<view class="f-s-ac mla c9 p-r"
				style="margin: 0 auto;height: 100rpx;" :style="{height: isIpx?'140rpx':'100rpx'}" @click="show=true">
				<view class="p=r" style="margin-left: 30rpx;width: 100rpx;height: 100rpx;"
					id="aniEnd" :style="{height: isIpx?'140rpx':'100rpx'}" >
					<!-- <image class="wh"
						:src="cartInfo.data.length===0?shopGoodsInfo.categorySet.noGoodIcon:shopGoodsInfo.categorySet.goodIcon"
						mode="aspectFit"></image> -->
						<image class="wh"
							:src="cartInfo.data.length===0?'/static/instore/gwc1.png':'/static/instore/gwc2.png'"
							mode="aspectFit"></image>
				</view>
				<view class="countPoint" v-if="cartInfo.data.length !== 0">{{goodsTotalNum}}</view>
				<block v-if="cartInfo.data.length===0">
					<view class="ml20 f-g-1 f-y-c">
						<text class="f30 mt10 c9">$</text>
						<text class="f36 wei c9">0</text>
						<text class="m01 c9">|</text>
						<!-- <text class="f24">预估外送费${{shopGoodsInfo.shopData.distribution.money}}</text> -->
						<text class="f24">美味多多,快来选购~</text>
					</view>
					<view class="mr20">
						<!-- <text>${{shopGoodsInfo.shopData.distribution.startMoney}}起送</text> -->
						<view class="wxh cf f-c" :style="{background:tColor,opacity: .6}">选好了</view>
					</view>
				</block>
				<block v-else>
					<view class="ml20 f-g-1 f-col" style="line-height: 30rpx;">
						<view>
							<!-- 满减后 价格 -->
							<text class="c9 f26 mt10">$</text>
							<text class="c9 f30">{{cartInfo.price||0}}</text>
							<!-- 划线价 （原价） -->
							<text class="f24 c9 t-d-l"
								v-if="cartInfo.price!==cartInfo.oldPrice">${{cartInfo.oldPrice||0}}</text>
						</view>
						<view>
							<!-- <text class="cf f24">预估外送费${{shopGoodsInfo.shopData.distribution.money}}</text> -->
							<!-- <text class="c9 f20 t-d-l">$6</text> -->
						</view>
					</view>
					<!-- <view v-if="alomostMoney>0" class="c9 mr20">
						<text>差${{alomostMoney}}起送</text>
					</view> -->
					<!-- 单点不送 -->
					<view v-if="validate&&validate.show" class="mr20">
						<view  @click.stop="handleGoAdd(validate.onClick)">{{validate.text||''}}</view>
					</view>
					<view v-else class="f-c h100 c0 wei f30 wxh mr20"
						:style="{background:tColor,color:fontColor}" @click.stop="goToPay"><text>选好了</text></view>
				</block>
			</view>
			<view v-if="isIpx" class='bgf'></view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
	} from "vuex";
	import utils from '@/common/utils.js'
	import Discount from './Discount.vue'
	export default {
		components: { Discount },
		props: {
			storeId: {
				type: [String, Number],
				default: ''
			},
			cartInfo: {
				type: Object,
				default: () => { data: [] }
			},
			background: {
				height: {
					type: String,
					default: "#f5f5f5"
				}
			},
			height: {
				type: [Number, String],
				default: 100
			},
			tableinfo:'',
			sjxx: {
				type: Object,
				default () {
					return {}
				}
			},
		},
		data() {
			return {
				show: false,
			}
		},
		computed: {
			...mapState(['shopGoodsInfo','canOrder']),
			alomostMoney() {
				try {
					return (this.shopGoodsInfo.shopData.distribution.startMoney - this.cartInfo.price).toFixed(2)
				} catch (e) {
					//TODO handle the exception
					return 0
				}
			},
			scrollHeight() {
				return this.wHeight / 2 - (this.height + 60) / this.pxToRpxRate - 57
			},
			reduceArr() {
				if (this.shopGoodsInfo.discount.reduce instanceof Array) return false
				return this.shopGoodsInfo.discount.reduce
			},
			goodsTotalNum() {
				var count = 0
				if (!this.cartInfo) return
				if (this.cartInfo.data) {
					for (let i of this.cartInfo.data) {
						count += +i.num
					}
				}
				return count

			},
			discount() {
				if (!this.reduceArr) return
				if (this.reduceArr.data.type === '1') {
					// 循环满减
					return {
						reduceMoney: this.cartInfo.reduce,
						money: utils.countMoney(this.reduceArr.data.fullMoney - (this.cartInfo.oldPrice % this.reduceArr
							.data.fullMoney)),
						againReduce: this.reduceArr.data.money
					}
				}
				if (this.reduceArr.data.type === '2') {
					//判断递增满减阶级
					let moneyArr = this.reduceArr.data.moneyArr
					let index = moneyArr.findIndex(i => +i.fullMoney > +this.cartInfo.oldPrice)
					let info = moneyArr[index]
					if (index === -1) { // 最大阶级
						return {
							reduceMoney: this.cartInfo.reduce,
							reduceFullMoney: true
						}
					}
					return {
						reduceMoney: this.cartInfo.reduce, //已减
						money: utils.countMoney(info.fullMoney - this.cartInfo.oldPrice), //再满
						againReduce: info.money - this.cartInfo.reduce // 可再减
					}
				}
			},
			validate(){
					if(!this.cartInfo.data) return false
					// 单点不送
					if(this.canOrder.singleIds.length>0){
						let single = true
						this.cartInfo.data.forEach(item=>{
							if(!this.canOrder.singleIds.some(i=>item.goodsId===i)){
								//some结果为false说明找到了【单点不送】以外的商品
								single=false
							}
						})
						if(single){
							return {
								show:single,
								text:'单点不送'
							}
						}
						
					}
					// 必点
					if(this.canOrder.mustIds.length>0){
						let must = true
						this.cartInfo.data.forEach(item=>{
							if(this.canOrder.mustIds.some(i=>item.goodsId===i)){
								//some结果为false说明找到了【单点不送】以外的商品
								must=false
							}
						})
						if(must){
							return {
								show:must,
								text:'未点必选品',
								onClick:'mustCategory'
							}
						}
					}
				
				return false
			},
		},
		mounted() {
			let query = uni.createSelectorQuery().in(this)
			// 确认动画结束位置
			query.select('#aniEnd').boundingClientRect(res => {
				this.$emit('getAniEndDot', res.left + (res.width / 2), res.top + res.height)
			}).exec()
		},
		methods: {
			handleGoAdd(fnName){
				if(fnName === 'mustCategory'){
					if(this.$parent.$parent){
						this.$parent.selectCategory(this.$parent.$parent.mustCategoryIndex)
					}
				}
			},
			goToPay() {
				if (this.isLogin) {
					// uni.setStorageSync('storeAddress', {
					// 	lat: this.shopGoodsInfo.shopData.lat,
					// 	lng: this.shopGoodsInfo.shopData.lng,
					// 	name: this.shopGoodsInfo.shopData.name,
					// 	address: this.shopGoodsInfo.shopData.address,
					// 	icon: this.shopGoodsInfo.shopData.icon
					// })
					// let discount = {
					// 	reduceMoney:this.cartInfo.reduce//活动满减
						
					// }
					console.log('tableinfo',this.tableinfo,this.sjxx)
					uni.setStorageSync('carInfo', {
						sjxx: {
							discount: this.sjxx.discount,
							moreSet: this.sjxx.moreSet,
							outSet: this.sjxx.moreSet.distributionSupport,
							shopData: this.sjxx.shopData
						},
					})
					this.go('navigateTo','/yb_o2ov2/shop/in/car?tableInfo=' + encodeURIComponent(JSON.stringify(this.tableinfo)))
					// this.go('navigateTo', `/yb_o2ov2/home/<USER>
				} else {
					this.go('navigateTo', '/yb_o2ov2/my/login')
				}
			},
			async clear() {
					await this.util.modal('確認清除購物車嗎', '清除購物車').then(res => {
						this.util.request({
							url: this.api.clearCart,
							method: 'POST',
							data: {
								storeId: this.storeId,
								tableId:this.tableinfo.id,
								key: 'ins',
								item: '2' //1外卖 2店内 3快餐
							}
						})
					})
					this.util.message('刪除成功', 1, 1000)
					this.$parent.refresh()
				this.show = false
			},
			addCart(good) {
				this.$parent.addCart(undefined, good.goodsId, undefined, undefined, good.id)
			},
			removeCart(good) {
				this.$parent.removeCart(good.goodsId, undefined, undefined, good.id)
			},
		}
	}
</script>

<style scoped lang="scss">
	.cartBox {
		position: relative;
		width: 100%;
		top: 0;
		z-index: 2;
	}

	.countPoint {
		position: absolute;
		width: 40rpx;
		height: 40rpx;
		top: 0;
		left: 110rpx;
		text-align: center;
		color: #ffffff;
		border-radius: 50%;
		background: red;
	}
	.wxh{
		width: 170rpx;
		height: 60rpx;
		border-radius: 30rpx;
	}
</style>
