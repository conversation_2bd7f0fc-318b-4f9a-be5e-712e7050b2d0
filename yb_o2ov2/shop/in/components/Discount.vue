<template>
	<view>
		<view v-if="goodsTotalNum===0 && reduceArr" class="f-c f20" style="height: 60rpx;">
			<!-- 循环满减 -->
			<block v-if="reduceArr.data.type === '1'">
				<text>每满{{reduceArr.data.fullMoney}}减{{reduceArr.data.money}}</text>
			</block>
			<!-- 阶梯满减 -->
			<block v-else-if="reduceArr.data.type === '2'">
				<text v-for="(text,index) in reduceArr.data.moneyArr" :key='text.fullMoney'>
					<text>{{`满${text.fullMoney}减${text.money}`}}</text>
					<text v-if="index !== reduceArr.data.moneyArr.length-1">,</text>
				</text>
			</block>
		</view>
		<view v-else class="f-c f20" style="height: 60rpx;">
				<text>已减</text>
				<text style="color:#ff5454">{{discount.reduceMoney || 0}}元</text>
				<block v-if="!discount.reduceFullMoney">
					<text>再买</text>
					<text style="color:#ff5454">{{discount.money || 0}}元</text>
					<text>可再减</text>
					<text style="color:#ff5454">{{discount.againReduce || 0}}元</text>
					<text style="color:#ff5454">[去凑单]</text>
				</block>
			<!-- 无商品显示满减规则 -->
		</view>
	</view>
</template>

<script>
	export default {
		props:{
			reduceArr:{
				type:Object,
				default:()=>{}
			},
			discount:{
				type:Object,
				default:()=>{}
			},
		},
		data(){
			return{}
		}
	}
</script>

<style>
</style>
