<template>
	<u-mask :show="show" @click="$emit('update:show', false)">
		<view class="specsMask">
			<view class="rect" @tap.stop>
				<view class="f32 wei mb20">{{goodsInfoSpecs.goodName}}</view>
				<scroll-view scroll-y="true" :style="{height:(wHeight/2.5).toFixed(0)+'px'}">
					<!-- 规格 -->
					<view class="mb50" v-if="specsData.specsData&&specsData.specsData.length !==0">
						<view class="f24 c9">{{ $t('supplier.spec') }}</view>
						<view class="f-raw">
							<view v-for="(item,index) in specsData.specsData" :key="item.id" class="spec"
								:class="specsDataActive === index?'active':'inactive'"
								@click="changeActive('specsData',index)">{{item.specsName}}</view>
						</view>
					</view>
					<!-- 自定义 -->
					<block v-if="specsData.attrData&&specsData.attrData.length !==0">
						<view class="mb50" v-for="(item,index) in specsData.attrData" :key="item.index">
							<view class="f24 c9">{{item.attrName}}</view>
							<view class="f-raw">
								<view
									v-for="(spec,current) in filteredAttrStr(item.attrStr)"
									:key="spec"
									class="spec" :class="attrDataActive[index]===current?'active':'inactive'"
									@click="changeActive('attrData',index,current)">{{spec.name}}
								</view>
							</view>
						</view>
					</block>
					<!-- 选加 能多选-->
					<view class="mb50" v-if="specsData.meterialData&&specsData.meterialData.length !==0">
						<view class="f24 c9">{{ $t('supplier.selectPlus') }}</view>
						<view class="f-raw">
							<view v-for="(item,index) in specsData.meterialData" :key="item.id" class="spec"
								:class="meterialDataAvtive.includes(index)?'active':'inactive'"
								@click="changeActive('meterialData',index)">{{item.materialName}}
								<text>${{item.SalesPrice}}</text>
							</view>
						</view>
					</view>
				</scroll-view>
				<view class="selected-spec">
					{{ $t('supplier.selectSpec') }}：
					<text class="c0 wei">{{specText}}</text>
				</view>
				<view class="f-x-bt pt20" style="height: 80rpx;">
					<view>
						<text class="wei">{{ $t('supplier.total') }}</text>
						<text class="f28" style="color: #F45439;">$</text>
						<text class="f48" style="color: #F45439;">{{totalMoney}}</text>
						<!-- <text class="t-d-l c9 f24 ml10">$20</text> -->
					</view>
					<view class="p-r" id="add">
						<view v-if="goodsNum === 0" class="bs10 wei f-y-c" style="padding: 5rpx 10rpx;"
							:style="{background:categorySet.delColor,color:categorySet.addColor}" @click.stop="addCart">
							<view class="mr10" style="margin-top: -4rpx;">+</view>
							<view>{{ $t('supplier.add') }}</view>
						</view>
						<view v-else class="f-e-bt" style="width: 120rpx;">
							<view v-if="goodsNum<=0" style="width: 20rpx;height: 100%;"></view>
							<block v-else>
								<view class="f-y-e pb10" style="height: 60rpx;" @click="removeCart">
									<view class="bs10 f-c" style="height: 36rpx;width: 36rpx;text-align: center"
										:style="{border: `1px solid ${categorySet.delColor}`}">
										<!-- 减号 -->
										<text class="iconfont iconminus f20 bs10 wei" style="padding: 4rpx 0;"
											:style="{color:categorySet.addColor2}"></text>
									</view>
								</view>
								<view style="padding-bottom: 5rpx;">{{goodsNum}}</view>
							</block>
							<view class="f-y-e pb10" style="height: 60rpx;" @click="addCart">
								<view class="bs10 f-c" style="height: 36rpx;width: 36rpx;text-align: center;"
									:style="{background: categorySet.delColor}">
									<!-- 加号 -->
									<text class="iconfont iconplus f20 bs10 wei" style="padding: 4rpx 0;"
										:style="{color: categorySet.addColor}"></text>
								</view>
							</view>
						</view>
					</view>
				</view>
				<!-- <view class="close">X</view> -->
			</view>
		</view>
	</u-mask>
</template>

<script>
	import {
		mapState,
	} from 'vuex'
	export default {
		props: {
			show: {
				type: Boolean,
				default: false
			},
			specsData: {
				type: Object,
				default: () => {}
			},
			cartList: {
				type: Array,
				default: () => []
			},
			goodsInfoSpecs: {
				type: Object,
				default: () => {}
			},
			goodsId: {
				type: [String],
				default: ''
			}
		},
		data() {
			return {
				specsDataActive: 0,
				attrDataActive: [],
				meterialDataAvtive: [],
				meterialMoney: 0,
				specText: '',
				totalMoney: 0,
				dot: {},
				goodsNum: 0,
				goodsSpecList: [], //记录规格
				spec: {
					attribute: '',
					groupId: '',
					material: []
				},
				cartId: null
			}
		},
		watch: {
			cartList(val) {
				this.goodsSpecList = []
				this.getGoodsNum()
			},
			specsData(val) {
				this.spec = {
					attribute: '',
					groupId: '',
					material: []
				}
				//初始化
				if(val.attrData){
					this.attrDataActive=[]
					val.attrData.forEach((item, index) => {
						this.attrDataActive[index] = 0
					})
				}
				this.meterialMoney = 0
				this.meterialDataAvtive =[]
				this.refreshData()
			}
		},
		computed: {
			...mapState({
				categorySet: state => state.shopGoodsInfo.categorySet
			}),
			filteredAttrStr() {
				return (attrStr) => attrStr.filter(spec => spec.name !== '')
			}
		},
		created() {

		},
		mounted() {
			let query = uni.createSelectorQuery().in(this)
			query.select('#add').boundingClientRect(res => {
				this.dot = {
					x: res.left,
					y: res.top
				}
			}).exec()
		},
		methods: {
			refreshData() {
				this.getSpecText()
				this.getGoodsNum()
				this.getTotalMoney()
			},
			changeActive(spec, index, current) {
				switch (spec) {
					case 'specsData': //一维数组 单选
						this.specsDataActive = index
						break;
					case 'attrData': //二维数组 单选
						this.$set(this.attrDataActive, index, current)
						break;
					case 'meterialData': //一维数组 多选
						let _index = this.meterialDataAvtive.findIndex(i => i === index)
						if (_index === -1) {
							this.meterialMoney += (+this.specsData.meterialData[index].SalesPrice)
							this.meterialDataAvtive.push(index)
						} else {
							this.meterialMoney += -this.specsData.meterialData[index].SalesPrice
							this.meterialDataAvtive.splice(_index, 1)
						}
						break;
				}
				this.refreshData()
			},
			getGoodsNum() {
				// 获取该规格的产品个数
				this.goodsSpecList = this.cartList.filter(i => i.goodsId === this.goodsId)
				let arr = this.goodsSpecList
				// console.log('arr',arr)
				// console.log('spec',this.spec)
				for (let key of arr) {
					let groupId = key.groupId || ''
					if (groupId !== this.spec.groupId) continue
					let attribute = key.attribute || ''
					if (attribute !== this.spec.attribute) continue
					let material = key.material || JSON.stringify([])
					if (material !== JSON.stringify(this.spec.material)) continue
					this.cartId = key.id
					this.goodsNum = key.num
					return
				}
				this.cartId = null
				this.goodsNum = 0
			},
			getTotalMoney() {
				//计算总价
				let SalesPrice = (this.specsData.specsData.length !== 0 && +this.specsData.specsData[this.specsDataActive]
					.SalesPrice) || +this.goodsInfoSpecs.goodOriginPrice
				this.totalMoney = SalesPrice + this.meterialMoney
			},
			getSpecText() {
				// 获取选择文字  修改商品规格参数
				let specsDataText = ''
				if (this.specsData.specsData.length) { //有规格进行一下操作 ，无规格跳过
					let specsDataItem = this.specsData.specsData[this.specsDataActive]
					specsDataText = `${specsDataItem.specsName}+` || ''
					this.spec.groupId = specsDataItem.id
				}


				this.spec.attribute = []
				let attrDataText = ''
				let attrDataActiveLength = this.attrDataActive.length - 1
				this.attrDataActive.forEach((specIndex, titleIndex) => {
					let name = this.specsData.attrData[titleIndex].attrStr[specIndex].name
					attrDataText += `${name}+`
					this.spec.attribute.push(name)
				})
				this.spec.attribute = this.spec.attribute.toString()

				this.spec.material = []
				let meterialDataText = ''
				let meterialDataAvtiveLength = this.meterialDataAvtive.length - 1
				this.meterialDataAvtive.forEach((item, index) => {
					let meterialDataItem = this.specsData.meterialData[item]
					meterialDataText += this.specsData.meterialData[item].materialName
					this.spec.material.push({
						materialId: meterialDataItem.id,
						num: 1
					})
					if (meterialDataAvtiveLength !== (index)) {
						meterialDataText += '+'
					}
				})
				this.specText = specsDataText + attrDataText + meterialDataText
			},
			addCart() {
				this.$emit('getXY', this.dot.x, this.dot.y)
				this.$emit('startAni')
				this.$parent.addCart(undefined, this.goodsId, undefined, undefined, this.cartId, this.spec)
			},
			removeCart() {
				this.$parent.removeCart(this.goodsId, undefined, undefined, this.cartId, this.spec)
			},
		},
	}
</script>

<style scoped lang="scss">
	.specsMask {
		position: relative;
		width: 100%;
		height: 100%;

		.rect {
			position: absolute;
			width: 700rpx;
			top: 50%;
			left: 50%;
			padding: 20rpx;
			transform: translate(-50%, -50%);
			border-radius: 20rpx;
			background: #FFF;

			.spec {
				text-align: center;
				font-size: 24rpx;
				padding: 5rpx 10rpx;
				margin-right: 20rpx;
				margin-top: 20rpx;
				border-radius: 10rpx;
				min-width: 200rpx;
				height: 60rpx;
				line-height: 50rpx;
			}

			.active {
				font-weight: bold;
				color: #ff964d;
				background: #fefbf2;
				border: 1px solid #ff964d;
			}

			.inactive {
				color: #000;
				background: #fff;
				border: 1px solid #f9f9f9;
			}
		}

		.close {
			position: absolute;
			left: 50%;
			bottom: -90rpx;
			transform: translateX(-50%);
		}

		.selected-spec {
			color: #999;
			padding: 10rpx;
			font-size: 24rpx;
			width: 700rpx;
			margin-left: -20rpx;
			padding-left: 20rpx;
			background: #f5f5f5;
		}
	}

	.moveX {
		// infinite
		animation: x .2s cubic-bezier(0.06, .46, 0, 1.04);
	}

	.moveY {
		animation: y .2s linear;
	}

	@keyframes x {
		0% {
			transform: translateX(0);
		}

		;

		100% {
			transform: translateX(-40vh);
		}
	}

	@keyframes y {
		0% {
			transform: translateY(0);
		}

		;

		100% {
			transform: translateY(15vh);
		}
	}
</style>
