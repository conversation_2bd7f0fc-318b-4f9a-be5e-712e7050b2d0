<template>
	<view>
		<!-- 返回 -->
	<!-- 	<view v-if="storeSet.storeModel!='1'" class="p-f" :style="{top:`${menuButtonTop}px`,left:`20rpx`,zIndex:4,marginTop:'6rpx'}" @click="go('back')">
			<text class="iconfont iconback f30 cf" :style="{color:opacity>=1?'#000':'#fff'}"></text>
		</view> -->
		<view >
			<!-- 上半部 -->
			<view class="info">
				<!-- 背景遮罩图 -->
				<view class="bg-img" :style="{background:tColor,color:fontColor,zIndex:2}">
					<view class="posi-a" :style="{top:`${menuButtonTop}px`,left:`20rpx`,zIndex:4,marginTop:'22rpx',opacity:opacity2,display:display}" v-if="shopData.name">
						<view class="f36 t-o-e" style="width: 710rpx;">{{shopData.name}}</view>
						<view class="f-bt mt10">
							<view class="f-1 f-y-c">
								<view v-if="storeInfo.tableInfo" class="f-y-c f30" :style="{color:fontColor}">
									<text>{{storeInfo.tableInfo.typeName}}</text>
									<text class="ml10">{{storeInfo.tableInfo.name}}</text>
									<text v-if="paIx"><text class="wei">·{{paIx}}</text>人用餐</text>
								</view>
							</view>
							<view class="f-row">
								<view class="f-c">
									<text  @click="go('navigateTo',`/yb_o2ov2/index/index`)" class="iconfont icon-shouye f42 mr30" :style="{color:fontColor}"></text>
									<!-- <text @click="tzdd" class="iconfont icon-xiaoxi2 f42 mr30" :style="{color:fontColor}"></text> -->
									<text @click="go('navigateTo',`/yb_o2ov2/home/<USER>" class="iconfont icon-sousuo f42 mr30" :style="{color:fontColor}"></text>
									<text @click="tzdd" class="iconfont iconagainorder f42" :style="{color:fontColor}"></text>
								</view>
							</view>
						</view>
					</view>
				</view>
				<!-- 搜索框 -->
				<view class="p-f bf" style="top:0;width: 100%;z-index: 3;"
					:style="{height:statusNavBarHeight+'px',opacity:opacity}">
					<view class="search" :style="{marginTop:menuButtonTop+'px'}" @click="go('navigateTo',`/yb_o2ov2/home/<USER>">
						<text class="iconfont iconsearch f28 ml20 c9"></text>
						<input disabled="" type="text" value="" placeholder="请输入商品名称" />
					</view>
				</view>
				<!-- 收藏 -->
				<!-- <view class="p-f"
				:style="{top:`${menuButtonTop + 6/pxToRpxRate}px`,left:`${menuButtonLeft - 80/pxToRpxRate}px`}"
				 style="z-index: 3;" @click="collectionStore()">
					<text class="iconfont  f44 ml20"
					 :class="isCollection === 1?'icontcollectionfill':'icontcollection'"
					 :style="{color:isCollection === 1?tColor:opacity>=1?'#000':'#fff'}"
					 ></text>
				 </view> -->
				<!-- //必须设置高度才有动画的效果 -->
				<view class="card p-r" style="transition:1s ease;z-index: 2;" :style="{background:'#ffffff'}">
					<!-- logo -->
					<!-- <view class="bs20 p-a" style="right: 30rpx;top:-30rpx;width: 140rpx;height: 140rpx;">
						<image class="wh" :src="shopData.icon" mode="scaleToFill" style=""></image>
					</view> -->
						<!-- 店铺信息 start-->
						<view class="f-bt">
							<view>
								<!-- <view class="f-bt">
									<view class="f-1 f-y-c">
										<view v-if="storeInfo.tableInfo" class="f-y-c f32 c0">
											<text class="iconfont icondndc mr10 c0 f40"></text>
											<text>{{storeInfo.tableInfo.typeName}}</text>
											<text class="ml10 wei">{{storeInfo.tableInfo.name}}</text>
											<text v-if="paIx"><text class="wei">·{{paIx}}</text>位</text>
										</view>
									</view>
									<view class="f-row ml30">
										<view class="f-c">
											<text @click="tzdd" class="iconfont iconagainorder f48 c3"></text>
										</view>
									</view>
								</view> -->
								<!-- <view class="f36 wei" :class="showStoreTitle?'':'t-o-e'" style="width: 500rpx;" @click="showStoreTitle = !showStoreTitle">{{shopData.name}}</view> -->
								<!-- <view class="f24 c9 m10 f-y-c">
									<text class="iconfont iconstar f24" style="color:#ff5454"></text>
									<text class="wei mr10" style="color: #ff5454;">{{shopData.score}}</text>
									<text class="f20 mr10">月售{{shopData.outSales}}</text>
								</view> -->
							</view>
						</view>
						<!-- 折扣 end-->
						<!-- 公告 -->
						<view class="f24 c9 f-x-bt pb20">
							<view class="f-1 t-o-e">公告:{{moreSet.notice}}</view>
							<!-- <view class="f-y-c">更多信息
							</view> -->
						</view>
				</view>
			</view>
			<view>
				<!-- <view class="tabNav" :style="tabNavStyle">
					<tab-nav gutter="60" :activeColor="tColor" inactiveColor="#666666" :height="tabNavHeight" :bg-color="'#ffffff'" :current-index="current" :list="tabs"
						@change="tabsChange"></tab-nav>
				</view> -->
				<!-- 商品list -->
					<GoodsList ref="GoodsList" :storeId="params.storeId" :tableId="params.tableId" :tableinfo='storeInfo.tableInfo' :sjxx='sjxx' :scroll="handlerScroll" :callbackScroll.sync="scrollTop" ></GoodsList>
			</view>
		</view>
		

		<view v-if="showTable" class="tablec z9999 posi-f udlr">
			<view class="tcbg posi-a wh">
				<mg-img :src="dndcConfig.background||sjxx.shopData.icon"></mg-img>
			</view>
			<view class="posi-r h100 f-col">
				<view class="f-g-1 f-c-c cf">
					<view class="f-y-c">
						<view class="tcimg mr30">
							<mg-img :src="system.icon"></mg-img>
						</view>
						<view class="tctt">{{system.name}}</view>
					</view>
					<view class="tctbt f34 p03">欢迎来到{{sjxx.shopData.name}}</view>
				</view>
				<view class="f-g-0 tbbd o-h p-r">
					<view class="p-a wh bf bs15 f-col tbbdc" :class="{totop:showTotop}">
						<view class="w100 f40 c0 t-l mt20">客官，您几位？</view>
						<view class="f-y-c mt10">
							桌号：<text>{{storeInfo.tableInfo.typeName || 'A6'}}</text><text class="ml10">{{storeInfo.tableInfo.name}}</text>
						</view>
						<view class="tbbdvc f-y-c o-x-s">
							<view @click="dcParr(i+1)" class="f-g-0 tbbdv f30 f-c" :class="{'bva':i+1==paIx}" v-for="(v,i) in parr" :key='i'>{{v}}{{i>9?'':'人'}}</view>
						</view>
						<view class="ljdc f-c b-l-f0f0 f30 cf" @click="ljdc">开始点单</view>
					</view>
				</view>
				<view class="f-g-0 p3 f-c cf">{{system.name+'提供技术支持'}}</view>
			</view>
			<mg-modal :vs='true' :ismr="true" v-model="showxzrs" width="630rpx" :z-index="3000">
				<view class="bf bs10">
					<view class="p3 f30">
						<view class="t-c mb20">选择人数</view>
						<mg-input cname="p23" t='number' max="2" v-model="zdyrs" last='1' ht='人数' pr='请输入' />
					</view>
					<view class="f-row hlt" style="height: 100rpx;">
						<button hover-class='be' class="tcbtn f-1 bf f30 f-c c9" @click="showxzrs=false">取消</button>
						<button :style="{color:'#FF6735'}" hover-class='be' class="tcbtn f-1 bf f30 f-c" @click="qdxzrs">确定</button>
					</view>
				</view>
			</mg-modal>
		</view>
		<Load :show="showLoad"></Load>
	</view>
</template>

<script>
	import {
		mapState,
		mapMutations,
		mapActions
	} from 'vuex'
	import utils from '@/common/utils.js'
	import mgImg from '@/components/common/mg-img.vue'
	import mgModal from '@/components/common/modal.vue'
	import mgInput from '@/components/common/mg-input.vue'
	import MgSwiper from '@/components/common/swiper.vue'
	import GoodsList from './components/GoodsList.vue'
	import TabNav from '@/components/TabNav.vue'

	import {
		utilMixins
	} from "@/common/util-mixins.js"
	export default {
		components: {
			mgImg,
			mgModal,
			mgInput,
			MgSwiper,
			GoodsList,
			TabNav,
		},
		computed: {
				tabNavStyle() {
					//部分机型rpx转化px丢失精度过大 所以-1px
					return `position:sticky;top:${this.statusNavBarHeight-1}px;z-index: 3;width: 100%;`
				},
				// reduceArr(){
				// 	if(this.discount.reduce instanceof Array) return false
				// 	return this.discount.reduce
				// },
				// deliveryArr(){
				// 	if(this.discount.delivery instanceof Array) return false
				// 	return this.discount.delivery
				// },
				...mapState({
					storeSet: state => state.config.storeSet,
					user: state => state.user,
				}),
				...mapState('dndc', {
					 dndcConfig: state => state.dndcConfig || {},
				}),
			},
			data() {
				return {
					timeText:{
						time:'',
						text:[]
					},
					mustCategoryIndex:'',//用来跳转必选商品的分类位置
					params:{
						storeId:null,
						tableId:null,
					},
					showStoreTitle:false,//暂时用来显示点名全称
					couponsPopupData:{},//优惠券信息
					vipCouponsData:{},//优惠券信息
					collectionData:{},//收藏有礼
					false:true,//收藏有礼弹窗
					isCollection:2,//店铺收藏
					goodsId:false,//从大牌臻选进来会传goodsid
					shopData:{}, //商户信息
					moreSet:{distributionSupport:[]}, //后台设置
					categorySet:{}, // 总店设置
					discount:{}, // 折扣信息
					showLoad:true,
					handlerScroll:'',//将scroll信息传给子组件
					couponsShow:false,//领取优惠券弹窗
					opacity:0, //搜索框透明度
					opacity2:1, //搜索框透明度
					display:'block', //搜索框透明度
					scrollTop: '', // 整个页面的scroll的滚动位置
					tempScrollTop:0,//记录页面滚轴位置
					tabNavHeight: 81, //tabNav高度单位rpx
					current: 0,
					tabs: [{
						name: '全部商品'
					}, {
						name: '评价'
					}, {
						name: '商店'
					}],
					scrollCurrentTopArr:[0,0,0], //记录页面滚动条历史位置
					scrollDot:'',
					
					showTable: false,
					showTotop: false,
					showxzrs: false,
					sjxx: {},
					storeInfo: {},
					parr: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '更多'],
					paIx: 1,
				}
			},
			watch:{
				scrollTop(val){
					console.log(val)
					uni.pageScrollTo({
						  scrollTop: val,
						  duration: 50
					})
				}
			},
			onReachBottom(){
				this.nextPage()
			},
			onPageScroll(e){
				this.scroll(e)
			},
			async onLoad(option) {
				this.showLoad=true
				// this.params = {
				// 	storeId:option.storeId,
				// 	lat:this.latLng.latitude,
				// 	lng:this.latLng.longitude
				// }
				// if(option.goodsId){//从大牌臻选进来
				// 	this.goodsId = option.goodsId
				// }
				console.log(11,option)
				if(option.tableId || option.scene){
					this.GetPxToRpxRate()
					this.getSystem()
					this.getLoginInfo()
					this.setConfig()
					await this.getLocInfo()
					
					let tableId = utils.getOptions(option, {
						key: 'tableId',
					})
					console.log('option',option)
					// let tableId = 6
					this.query = option
					this.tableId = tableId
					if (!tableId) {
						return this.tips()
					}
					let res = await this.util.request({
						'url': this.api.tsczxq,
						method: 'POST',
						data: {
							tableId: tableId,
							lat: this.latLng.latitude || '',
							lng: this.latLng.longitude || '',
						}
					})
					if (res) {
						res.data.tableId = tableId
						this.storeInfo = {
							id: res.data.storeId,
							tableInfo: res.data,
						}
						this.storeId = res.data.storeId
						this.params.storeId = res.data.storeId
						this.params.tableId = tableId
						console.log('storeId',this.storeId,this.params.tableId)
						this.params = {
							storeId:this.storeId,
							tableId:tableId,
							lat:this.latLng.latitude,
							lng:this.latLng.longitude
						}
						console.log(123,this.params)
					} else {
						return this.tips()
					}
				}else{
					return this.tips()
				}
				await this.init()
				setTimeout(()=>{
					this.showLoad=false
				},300)
			},
			onShow() {
				this.collectionData.day = this.formatDate(3) //收藏有礼
				// 获取购物车信息 需要等storeId传递到子组件
				this.refresh()
			},
			onReady() {
				// this.getScrollDot()
			},
			methods: {
				...mapActions('dndc', ['getDndcConfig']),
				...mapActions(["setSystemInfo"]),
				async setConfig(){
					await this.getDndcConfig()
					// await this.getConfig({
					// 	name: 'payConfig',
					// 	api: this.api.config,
					// 	data: { ident: 'payConfig' }
					// })
					// await this.getConfig({
					// 	name:'orderSet',
					// 	api:this.api.orderSet,
					// 	data:{}
					// })
					// await this.getConfig({
					// 	name:'currency',
					// 	api:this.api.config,
					// 	data:{ident: 'currency'}
					// })
				},
				ljdc() {
					if (this.paIx == '') return this.util.message('请选择人数', 3)
					this.storeInfo.tableInfo.pnum = this.paIx
					this.showTable = false
				},
				dcParr(i) {
					if (i == 11) return this.showxzrs = true
					this.paIx = i
				},
				qdxzrs() {
					if (this.zdyrs == '') return this.util.message('请输入人数', 3)
					this.storeInfo.tableInfo.pnum = this.paIx = this.zdyrs
					this.showTable = false
				},
				tips() {
					uni.showModal({
						title: '提示',
						content: '未获取到桌位信息或者超出商店范围',
						showCancel: false,
						success: res => {
							this.go('reLaunch','/yb_o2ov2/index/index')
						},
					});
				},
				
				
				getTimeText(){
					// if(this.shopData.businessState.msg === '')return
					// let time = ''
					// let text = ''
					// try{
					// 	time = this.shopData.businessState.msg.match(/[0-9]{2}/g)[0]+':'+this.shopData.businessState.msg.match(/[0-9]{2}/g)[1]
					// 	text = this.shopData.businessState.msg.split(time)
					// }catch(e){
					// 	console.log(e)
					// 	//TODO handle the exception
					// }
					
					// this.timeText.time = time
					// this.timeText.text = text
				},
				formatDate(day){
					let timeStamp = new Date().valueOf() + day*24*60*60*1000
					return new Date(timeStamp).format("yyyy-MM-dd")
				},
				collectionStore:utils.debounceImmediate(async function(){
					let {data} =  await this.util.request({
						url: this.api.scjk,
						method: 'GET',
						data: {
							collectionId: this.params.storeId,
							type:'1'
						}
					})
					if(this.isCollection === 1){
						this.isCollection=2
						this.util.message('取消收藏成功', 3)
					}else{
						this.isCollection=1
						if(data&&data.money){
							this.collectionData = data
							this.collectionData.day = this.formatDate(3)
							this.giftShow = true
						}else{
							this.util.message('收藏成功', 3)
						}
					}
				}),
				nextPage(){
					if(this.current === 1){ // 评价
						this.$refs.CommentList.nextPage()
					}
					return
				},
				...mapMutations(["setShopGoodsInfo","setCanOrder"]),
				async init(){
					//获取商品列表 门店信息
					await this.fetchData()
					//获取优惠券信息
					this.getCoupons()
					this.refresh()
					setTimeout(async () => {
						this.addFwjl({
							storeId: this.params.storeId,
							origin: '2'
						})
					}, 1000)
					if(this.user.isVip){
						this.getVipCoupons()
					}
				},
				async refresh(){
					// //获取购物车信息 需要等storeId传递到子组件
					this.$nextTick(()=>{
						this.$refs.GoodsList.getCartList()
					})
				},
				async getCoupons(){
					let {data} =  await this.util.request({
						url: this.api.qtsjjh,
						method: 'GET',
						data:{
							storeId: this.params.storeId,
							location:1
						},
						is_login: 0
					})
					this.couponsPopupData = data
				},
				async getVipCoupons(){
					let {data} =  await this.util.request({
						url: this.api.vipsc,
						method: 'GET',
						data:{
							storeId: this.params.storeId
						}
					})
					this.vipCouponsData = data
				},
				async vipcoupon(){
					if(!this.isLogin){
						this.go('navigateTo',`/yb_o2ov2/my/login`)
						return
					}
					uni.showModal({
						title: '提示',
						content: '您确定要兑换此红包吗？',
						success: async (res) => {
							if (res.confirm) {
								let vipData = await this.util.request({
									url: this.api.vipcpe,
									mask: '兑换中',
									method: 'POST',
									data: {
										storeId: this.params.storeId,
										couponId: this.vipCouponsData.id
									}
								})
								this.getVipCoupons()
								if(vipData.code==1){
									return this.util.message(vipData.msg || vipData.data, 1, 2000)
								}else{
									return this.util.message(vipData.msg || vipData.data, 3, 2000)
								}
							}
						}
					});
				},
				async fetchData(){
					await this.getStoreInfo()
					await this.getGoodsList()
					
				},
				async getStoreInfo(){
					this.params.goodsType = 2
					let {data} =  await this.util.request({
						url: this.api.getStoreInfo,
						method: 'GET',
						data: this.params
					})
					
					this.sjxx = data
					console.log('sjxx',this.sjxx)
					if (data.moreSet.orderMode == 1) {
						console.log(11,this.storeInfo.tableInfo)
						if (this.storeInfo.tableInfo.orderInfo) {
							this.showLoading = false
							if (!this.query.tableId) {
								this.go('redirectTo', '/yb_o2ov2/shop/in/order-dl?id=' + this.storeInfo.tableInfo.orderInfo.id)
							}
						} else {
							if (this.storeInfo.tableInfo.state == 1) {
								this.showTable = true
								this.showLoading = false
								setTimeout(() => {
									this.showTotop = true
								}, 100)
							} else {
								this.showLoading = false
							}
						}
					} else {
						this.showTable = true
						this.showLoading = false
						setTimeout(() => {
							this.showTotop = true
						}, 100)
					}
				
					this.shopData = data.shopData
					this.isCollection = data.shopData.isCollection
					this.moreSet = data.moreSet
					this.categorySet = data.categorySet
					this.discount = data.discount
					this.getTimeText()
					this.setShopGoodsInfo(data)
				},
				async getGoodsList(){
					let {data} =  await this.util.request({
						url: this.api.shopGoodsInfo,
						method: 'GET',
						data: {storeId:this.params.storeId,goodsType: 2}
					})
					this.$refs.GoodsList.backupCategory(data)
					//单点不送 商品ids
					let singleIds = []
					data.forEach(category=>{
						category.goods.forEach(item=>{
							if(item.aloneType === '1'){
								singleIds.push(item.id)
							}
						})
					})
					//必选分类下的所有商品（cartlist没有返回商品的分类id 所以需要分类下所有商品id）
					let mustIds = []
					data.forEach((category,index)=>{
						if(category.isRequire === '1'){
							this.mustCategoryIndex = index
							category.goods.forEach(item=>{
								mustIds.push(item.id)
							})
						}
					})
					this.setCanOrder({singleIds,mustIds}) //设置单点不送 和必选商品的id
				},
				// async tabsChange(e) {
				// 	this.current = e;
				// 	this.scrollCurrentTopArr[this.current] = this.tempScrollTop
				// 	if(this.tempScrollTop>this.scrollDot && this.scrollCurrentTopArr[e] < this.scrollDot){
				// 		this.scrollTop = this.scrollDot
				// 	}
				// 	if(this.tempScrollTop<=this.scrollDot){
				// 		this.scrollTop =  this.tempScrollTop
				// 	}
				// 	if(e === 1 && !this.$refs.CommentList.init){
				// 		await this.$refs.CommentList.fetchData()
				// 		this.scrollTop = 0 //计算吸顶评价分类必须的一步
				// 		this.$nextTick(()=>{
				// 			this.$refs.CommentList.getFixedDot()
				// 		})
				// 	}
				// },
				scroll(e) {
					var top = e.scrollTop
					this.tempScrollTop = top
					// this.opacity = (top - 200 / this.pxToRpxRate) / 50 > 1 ? 1 : (top - 200 / this.pxToRpxRate) /50
					this.opacity = top>50?1:0
					this.opacity2 = top>50?0:1
					this.display = top>50?'none':'block'
					this.handlerScroll = top
				},
				getScrollDot(){
					// 以下页面跳转指的是v-show
					// 当前ScrollTop超过这个点(ScrollDot) 跳转页面的历史scroll位置最低也得是ScrollDot
					// 小于ScrollDot 跳转页面的scroll位置等于当前页面的ScrollTop
					let query = uni.createSelectorQuery().in(this);
					// query.select('.tabNav').boundingClientRect((res) => {
					// 	this.scrollDot = res.bottom-Math.floor(this.statusNavBarHeight + this.tabNavHeight / this.pxToRpxRate)
					// 	// console.log('res',res)
					// }).exec()
					
				},
				async GetPxToRpxRate(){
					if(!this.pxToRpxRate){
						await this.setSystemInfo()
					}
				},
				tzdd() {
					this.go('navigateTo','/yb_o2ov2/shop/in/indd')
				},
			},
			// onShareAppMessage() {
			// 	let p = `yb_o2ov2/home/<USER>
			// 	return this.util.mpShare({
			// 		t: this.shopData.name,
			// 		p,
			// 	})
			// },
			// onShareTimeline(e) {
			// 	return {
			// 		title: this.system.shareTitle,
			// 		imageUrl: this.getImgS(this.system.shareIcon),
			// 	}
			// },
		}
</script>

<style scoped lang="scss">
	// @import '@/yb_o2ov2/index/goods.scss';

	.headerc {
		height: 90rpx;
	}

	.tablec {

		.tcbg {
			&::before {
				content: "";
				position: absolute;
				left: 0;
				right: 0;
				top: 0;
				bottom: 0;
				// background: linear-gradient(0deg, hsla(0, 0%, 100%, 0), rgba(0, 0, 0, .8));
				background: rgba(0, 0, 0, .55);
			}
		}

		.tcimg {
			width: 100rpx;
			height: 100rpx;
		}

		.tctt {
			font-size: 70rpx;
		}

		.tctbt {
			margin-top: 40rpx;
		}

		.tbbd {
			height: 496rpx;
			margin: 0 30rpx;
			border-radius: 15rpx;
		}

		.tbbdc {
			padding: 0 30rpx;
			bottom: 0;
			transition: all 0.6s linear;
			transform: translateY(100%);
		}

		.totop {
			transform: none;
		}

		.tbbdvc {
			margin: 70rpx 0 70rpx 0;
		}

		.tbbdv {
			width: 140rpx;
			height: 78rpx;
			border-radius: 78rpx;
		}

		.bva {
			background: #FD9800;
			color: #fff;
		}

		.ljdc {
			width: auto;
			background: #FF6735;
			height: 110rpx;
			border-radius: 110rpx;
		}
	}
	
	.collection{
		border-radius: 20rpx;
		border: 1px solid #f5f6f9;
		padding: 15rpx;
	}
	.info {
		position: relative;
		padding-top: 250rpx;
	}
	
	.bg-img {
		position: absolute;
		overflow: hidden;
		width: 100%;
		height: 100%;
		top: 0;
		z-index: -1;
	}
	
	.card {
		position: relative;
		border-radius: 20rpx 20rpx 0 0;
		padding:20rpx 20rpx 0;
	}
	.search {
		width: 340rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		margin-left: 100rpx;
		background: #f5f5f5;
		border-radius: 60rpx;
		line-height: 60rpx;
	}
	.label-coupon {
		font-size: 20rpx;
		height: 36rpx;
		// line-height: 38rpx;
		color: #c57771;
		border: 1rpx solid #f0dbdb;
		border-radius: 8rpx;
		padding: 0rpx 8rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
</style>
