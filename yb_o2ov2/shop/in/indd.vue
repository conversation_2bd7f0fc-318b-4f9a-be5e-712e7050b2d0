<template>
	<view class="">
		<view class="fixedtop bf5">
			<tab-nav gutter="80":activeColor="tColor" fontSize="30" inactiveTextColor="#666"
				:current-index="aIdx" :list="tabs" @change="changeTab" :isScroll="false"></tab-nav>
		</view>
		<view class="ccbd p3">
			<dn-order @refresh='dnrefresh' :ltop='0' v-model='laIdx' :labelarr='[]' :otype="1" :datalist="dataList"></dn-order>
			<mescroll-empty v-if="dataList.length===0" :option="{icon:'/static/empty/9.png',tip:'~ 暂无店内订单 ~'}"></mescroll-empty>
			<u-loadmore v-if="dataList.length!==0" @loadmore="nextPage" :status="status" />
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapActions
	} from 'vuex'
	import TabNav from '@/components/TabNav.vue'
	import dnOrder from '@/yb_o2ov2/index/component/dn-order.vue'
	import {
		sljz
	} from "@/common/util-mixins.js"
	export default {
		name: 'myCoupon',
		components: {
			TabNav,
			dnOrder,
		},
		data() {
			return {
				aIdx: 0,
				tabs: [{
					name: '全部',
					s: '',
				}, {
					name: '待付款',
					s: '1',
				}, {
					name: '已支付',
					s: '2',
				}, {
					name: '已关闭',
					s: '3',
				}],
				params: {
					page: 1,
					size: 10,
					state: 1,
				},
				refreshLoading:false,
				status:'loading',
			}
		},
		async onLoad(options) {
			this.getSystem()
			this.util.setNT('商店订单')
			await this.getLoginInfo()
			this.aIdx = +options.idx || 0
			this.changeTab(this.aIdx)
			this.isLoad = true
		},
		onShow() {
			if (this.isLoad) {
				this.changeTab(this.aIdx)
			}
		},
		mixins: [sljz],
		computed: {},
		methods: {
			...mapActions(["getConfig"]),
			dnrefresh(e) {
				this.refresh();
			},
			refresh() {
				this.changeTab(this.aIdx)
			},
			changeTab(e) {
				this.isget = this.mygd = false
				this.params.page = 1
				this.params.state = this.tabs[e].s
				this.dataList = []
				this.getList()
			},
			async getList(type) {
				this.status = 'loading'
				let {
					data
				} = await this.util.request({
					'url': this.api.tsdd,
					method: 'POST',
					data: this.params,
				})
				if(type !=='nextPage' || this.params.page === 1){
					this.dataList = data
				}else{
					if(data.length === 0){
						this.status = 'nomore'
						return
					}
					this.dataList = this.dataList.concat(data)
				}
				this.status = 'loadmore'
			},
			nextPage() {
				if (this.status === 'loading') { return }
				this.params.page++
				this.getList('nextPage')
			},
		},
	}
</script>

<style scoped lang="scss">
	.wxts {
		height: 70rpx;
	}

	.ccbd {
		margin-top: 96rpx;
	}
</style>
