<template>
	<view class="wh" style="background: #f5f5f5;">
		<!-- navigation -->
		<view class="t-c p-f w100 bf" style="z-index: 2;top: 0;"
			:style="{height:`${statusNavBarHeight}px`,paddingTop:`${menuButtonTop}px`}">
			<!-- 返回 -->
			<view class="p-a" :style="{top:`${menuButtonTop}px`,left:`20rpx`,zIndex:4}" @click="go('back')">
				<text class="iconfont iconback f40"></text>
			</view>
			<text class="f32 wei">{{payVipset.name}}</text>
		</view>
		<view class="w100 p3  p-r" style="background: #14110F;" :style="{marginTop:`${statusNavBarHeight}px`}">
			<view class="f-raw">
				<!-- 用户avatar -->
				<view class="bsf mr30" style="width: 100rpx;height: 100rpx;">
					<image class="wh" :src="user.portrait || '/static/no.png'" mode=""></image>
				</view>
				<view class="f36 wei f-col color-text">
					<view v-if="isLogin">{{user.userName}} <text class="f22 nowei ml10"
							v-if="user.userTel">({{user.userTel.replace(/(\d{3})\d{4}(\d{4})/,'$1****$2')}})</text>
					</view>
					<view v-else @click="go('navigateTo','/yb_o2ov2/my/login')">登录/注册</view>
					<view class="f24 nowei">{{payVipset.title}}</view>
				</view>
			</view>
			<view class="p-a f22 f-y-c color-text" style="top: 48rpx;right: 30rpx;" @click="go('navigateTo',`/yb_o2ov2/vip/buyvip-detailed-list`)">
				<text>购买记录</text><text class="iconfont iconinto f24 color-text"></text>
			</view>
		</view>
		<view class="w100" style="height: 300rpx">
			<image class="wh" :src="payVipset.vipBg" mode="aspectFill"></image>
		</view>
		<!-- 套餐 -->
		<scroll-view scroll-y :style="{height:`${wHeight-100}px`}" class="p3">
			<view class="f-y-c p2 bs20 color-text" style="background:linear-gradient(45deg, #29292Add, #29292A);">
				<view class="mr10" style="width: 50rpx;height: 50rpx;"><image class="wh" src="static/img_vip/no_vip/1-2.png" mode=""></image></view>
				<text>{{payVipset.introduction}}</text>
				<!-- <text style="color:#FF664F;">20元（4张5元红包）</text> -->
			</view>
			<view class="f36 m30">选择套餐</view>
			<scroll-view scroll-x class="w100 flex ws-n">
				<view v-for="(v,i) in tcarr" :key='i' class="mr20 dis-in" >
					<view class="plans-card" @click="selectPlans(i,v)" :class="index===i?'active':'inactive'">
						<view class="f36">
							<text>{{v.title}}</text>
						</view>
						<view class="f30">
							<text>$</text>
							<text class="f60">{{v.first==1 && ffhysj.state==1?v.firstMoney:v.money}}</text>
						</view>
						<view>
							<text class="t-d-l nowei c9">${{v.scribingMoney}}</text>
						</view>
					</view>
				</view>
			</scroll-view>
			<view class="vip-btn mt30" @click="ljkt">
				<text>$</text>
				<text class="f48">{{first==1 && ffhysj.state==1?firstMoney:money}}</text>
				<!-- <text class="ml20 f42" v-if="index===0">开通连续包月</text> -->
				<text class="ml20 f42">开通{{title}}</text>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	import {
		mapState,
		mapActions
	} from 'vuex'
		import utils from '@/common/utils';
		import {
			utilMixins
		} from "@/common/util-mixins.js"
	export default {
		data(){
			return {
				loading: false,
				xztcinfo: '',
				query: {},
				tcarr: [],
				index:0,
				money:'',
				title:'',
				day:'',
				first:'',
				firstMoney:'',
				xzidx: 0,
				gmxz: true,
				ffhysj: {},
			}
		},
		mixins: [utilMixins],
		async onLoad(){
			await Promise.all([this.getSystem(),
				this.getLoginInfo()
			])
			this.refreshUser({
				nomask: 1,
				get: 1,
				now: 1,
			})
			await this.getConfig({
				name: 'payVipset',
				api: this.api.config,
				data: {
					ident: 'payVip'
				}
			})
			this.util.setNT(this.payVipset.name)
			this.getDjlb()
		},
		computed:{
			...mapState({
				payVipset: state => state.config.payVipset,
			}),
		},
		methods: {
			...mapActions(['getConfig']),
			selectPlans(index,item) {
				this.index = index
				this.money = item.money
				this.title = item.title
				this.day = item.day
				this.first = item.first
				this.firstMoney = item.firstMoney
				// index=item
			},
			async getDjlb() {
				let res = await Promise.all([this.util.request({
					'url': this.api.ffhykqy,
				}), this.getHysj()])
				this.tcarr = this.payVipset.dayArr
				this.money = this.payVipset.dayArr[0].money || ''
				this.title = this.payVipset.dayArr[0].title || ''
				this.day = this.payVipset.dayArr[0].day || ''
				this.first = this.payVipset.dayArr[0].first || ''
				this.firstMoney = this.payVipset.dayArr[0].firstMoney || ''
			},
			async getHysj() {
				let res = await this.util.request({
					'url': this.api.ffhysj,
				})
				this.ffhysj = res.data
			},
			ljkt: utils.throttle(async function(e) {
				// if (!this.gmxz) {
				// 	return this.util.message('请阅读并同意购买须知', 3)
				// }
				// if (this.loading) return
				this.loading = true
				let orderRes = await this.util.request({
					'url': this.api['ffhyxd'],
					method: 'POST',
					mask: '下单中',
					data: {
						day: this.day
					},
				})
				if (orderRes.code==1) {
					let data = {}
					data.orderId = orderRes.data
					data.orderType = 8,
					data.money = this.first == 1 && this.ffhysj.state == 1 ? this.firstMoney : this.money,
					this.setPayInfo(data)
					this.go('redirectTo', '/yb_o2ov2/home/<USER>/index')
					this.loading = false
				} else {
					this.loading = false
					return this.util.message(orderRes.msg || orderRes.data, 3, 2000)
				}
			}, 1000)
		},
	}
</script>

<style scoped lang="scss">
	.vip-btn {
		height: 100rpx;
		width: 690rpx;
		text-align: center;
		font-weight: bold;
		line-height: 100rpx;
		border-radius: 100rpx;
		color: #ffffff;
		background: linear-gradient(45deg, #DA9E58, #C58541);
	}
	.color-text{
		color: #F2D499;
	}
	.plans-card{
		width: 200rpx;
		height: 290rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		border-radius: 20rpx;
		font-weight: bold;
		color:#8B511E;
	}
	.inactive{
		border: 1px solid #e0e0e0;
	}
	.active{
		border: 2px solid #FFD399;
		background: #FFD39922;
	}
</style>
