<template>
	<view>
		<!-- 头部 start -->
		<view class="posi-s w100 "
			:style="{top:0,height:`${statusNavBarHeight}px`,paddingTop:`${menuButtonTop}px`,zIndex:3,background:'#f5f5f5'}">
			<!-- 返回 -->
			<view class="p-a" :style="{top:`${menuButtonTop}px`,left:`20rpx`,zIndex:4}" @click="go('back')">
				<text class="iconfont iconback f40"></text>
			</view>
			<view class="t-c f32 wei">
				<view>购买记录</view>
			</view>
		</view>
		<!-- 头部 end -->
		<scroll-view scroll-y="true" @scrolltolower="nextPage" :style="{height:`${scrollHeight}px`}">
			<view class="p02 bf">
				<view v-if="showType==0 || item.type == showType" 
					v-for="item in list" :key="item"
					class="f-x-bt p20" style="border-top: 1px solid #f5f6f9;">
					<view>
						<view class="f28">{{item.payModeName}}</view>
						<view class="c9 f24" v-if="item.payAt">{{payTime(item.payAt)}}</view>
					</view>
					<view class="f-c-c">
						<view class="f-y-c">
							<text class="wei f36">${{item.money}}</text>
						</view>
					<!-- 	<view class="c9">
							{{item.type==1?'自动续费':'购买'}}
						</view> -->
					</view>
				</view>
			</view>
			<u-loadmore @loadmore="nextPage" :status="status" />
		</scroll-view>
	</view>
</template>

<script>
	import {
		pagingLoad
	} from "@/common/util-mixins.js"
	import TabNav from '@/components/TabNav.vue'
	import utils from '@/common/utils.js'
	export default {
		/**
		 * pagingLoad
		 * 参数【object】params，结果【array】list， api接口地址 【string】 api
		 * 输出function 刷新refresh 请求数据fetchData 下一页nextPage
		 * **/
		mixins: [pagingLoad],
		components: {
			TabNav
		},
		computed: {
			scrollHeight() {
				return this.wHeight - this.statusNavBarHeight
			}
		},
		data() {
			return {
				temp: [{
					name: '超级吃货月卡',
					type: 1
				}, {
					name: '超级吃货月卡',
					type: 1
				}, {
					name: '超级吃货月卡',
					type: 2
				}, {
					name: '超级吃货月卡',
					type: 1
				}],
				list: [],
				refreshLoading:false,
				status:'loading',
				params: {
					page: 1,
					size: 10,
					type: 1,
				},
				showType: 0,
				current: 0,
				bgColor: '#fff',
				tabs: [{
					name: '全部'
				}, {
					name: '收入'
				}, {
					name: '支出',
				}]
			}
		},
		onLoad() {
			// this.fetchData(this.requestModel)
			this.params.page = 1
			this.fetchData()
		},
		methods: {
			async refresh() {
				this.refreshLoading = true
				this.params.page = 1
				await this.fetchData()
				setTimeout(()=>{
					this.refreshLoading = false
				},500)
			},
			async fetchData(type) {
				this.status = 'loading'
				let { data } = await this.util.request({
					'url': this.api.vipmyOrder,
					data: this.params,
				})
				if(type !=='nextPage' || this.params.page === 1){
					this.list = data
				}else{
					if(data.length === 0){
						this.status = 'nomore'
						return
					}
					this.list = this.list.concat(data)
				}
				this.status = 'loadmore'
				// console.log(11,this.list)
			},
			nextPage() {
				if (this.status === 'loading') { return }
				this.params.page++
				this.fetchData('nextPage')
			},
			payTime(time) {
				let result = utils.timeToDate(time)
				return result
			},
			// requestModel() {
			// 	return new Promise((resolve) => {
			// 		setTimeout(() => {
			// 			let temp = this.temp
			// 			resolve(temp)
			// 		}, 1000)
			// 	}).then(res => {
			// 		this.list = this.list.concat(res)
			// 		return res
			// 	})
			// },
			// tabsChange(e) {
			// 	console.log(this.current)
			// 	this.showType = e
			// }
		}
	}
</script>

<style>
</style>
