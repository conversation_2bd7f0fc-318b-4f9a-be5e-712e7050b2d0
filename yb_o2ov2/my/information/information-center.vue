<template>
	<view class="p-r bf5">
		<!-- 头部 start -->
		<view class="p-a t0 w100 " :style="{top:0,height:`${statusNavBarHeight}px`,paddingTop:`${menuButtonTop}px`,zIndex:3,background:'#fff'}">
			<!-- 返回 -->
			<view class="p-a" :style="{top:`${menuButtonTop}px`,left:`20rpx`,zIndex:4}" @click="go('back')">
				<text class="iconfont iconback f40"></text>
			</view>
			<view class="t-c f32 wei">
				<view>资讯中心</view>
			</view>
		</view>
		<!-- 头部 end -->
		<!-- height 80rpx -->
<!-- 		<view class="w100" :style="{paddingTop:`${statusNavBarHeight}px`}">
			<tab-nav :activeColor="tColor" :isScroll="false" :list="tabs"
				@change="tabsChange"></tab-nav>
		</view> -->
<!-- 		<scroll-view scroll-y @scrolltolower="nextPage" :style="{height:`${scrollHeight}px`,paddingTop:`${statusNavBarHeight}px`}">
			<u-waterfall v-model="flowList" ref="uWaterfall">
				<template v-slot:left="{leftList}">
					<view v-for="(item, index) in leftList" :key="item.id" class="mt20">
						<WFE :element="item" @onGoTo="handlerGoTo"></WFE>
					</view>
				</template>
				<template v-slot:right="{rightList}">
					<view v-for="(item, index) in rightList" :key="item.id" class="mt20">
						<WFE :element="item" @onGoTo="handlerGoTo"></WFE>
					</view>
				</template>
			</u-waterfall>
			<u-loadmore @loadmore="nextPage" :status="status" />
		</scroll-view> -->
		<scroll-view scroll-y @scrolltolower="nextPage" :style="{height:`${scrollHeight}px`,paddingTop:`${statusNavBarHeight}px`}">
			<view @click="handlerGoTo(v.id)" class="weui-cell f-sh bf" v-for="(v,i) in flowList" :key='i'>
				<view class="f-1 f-y-bt">
					<view>
						<view class="f26 t-o-e">{{v.title}}</view>
						<view v-if="v.introduction" class="f24 t-o-e c9">{{v.introduction}}</view>
					</view>
					<view class="c9 f24 l-n">{{timeToDate(v.createdAt,'MM月dd日')}}</view>
				</view>
				<view class="img ml30 bs10">
					<mg-img :src="v.icon"></mg-img>
				</view>
			</view>
			<u-loadmore @loadmore="nextPage" :status="status" />
		</scroll-view>
	</view>
</template>

<script>
	import TabNav from '@/components/TabNav.vue'
	import WFE from "./components/WaterFallElement.vue"
	import mgImg from '@/components/common/mg-img.vue'
	import {
		utilMixins,
		sljz
	} from "@/common/util-mixins.js"
	export default {
		components: {
			TabNav,WFE,mgImg
		},
		data(){
			return {
				params:{
					page:1,
					size:10,
				},
				status:'loading',
				flowList:[],
				tabs: [{
					name: '全部'
				}, {
					name: '获取'
				}, {
					name: '消耗',
				}],
			}
		},
		mixins: [utilMixins, sljz],
		computed: {
			scrollHeight() {
				// return this.wHeight - this.statusNavBarHeight - 80/this.pxToRpxRate
				return this.wHeight
			}
		},
		onLoad() {
			this.fetchData()
		},
		methods:{
			async handlerGoTo(id){
				let {data} = await this.util.request({
						url: this.api.zxxq,
						data: {id:id}
					})
				uni.setStorageSync('noticeDetail',data)
				this.go('navigateTo',`/yb_o2ov2/home/<USER>
			},
			refresh(){
				this.$refs.uWaterfall.clear();
				this.params.page = 1
				this.fetchData()
			},
			async fetchData(){
				this.status='loading'
				let {data} = await this.util.request({
						url: this.api.zxlb,
						data: this.params
					})
					if(this.params.page === 1){
						this.flowList = data
					}else{
						if(data.length === 0){
							this.status = 'nomore'
							return
						}
						this.flowList = this.flowList.concat(data)
					}
				this.status='loadmore'
			},
			nextPage(){
				if(this.status === 'loading'){return}
				this.params.page++
				this.fetchData()
			},
		}
	}
</script>

<style scoped lang="scss">
	.img {
		width: 178rpx;
		height: 123rpx;
	}
	.weui-cell {
		padding: 35rpx 35rpx 30rpx;
	}
	
	.weui-cell:last-child:after {
		display: block;
	}
	
	.weui-cell:after {
		left: 112rpx;
	}
</style>
