<template>
	<view>
		<view class="bs20 bf mla" style="width: 340rpx;" @click="goToGoodsDetail(element)">
			<view class="w100" style="height: 360rpx;"><image class="wh" :src="element.icon" mode="aspectFill"></image></view>
			<view class="p02 pt20 pb10">
				<view class="wei">{{element.title}}</view>
				<view class="f24 c9 t-o-e"><text class="mr20">阅读量:{{element.views}}</text></view>
				<view class="f-x-bt f24 mt10">
					<view class="t-o-e c9 f-y-c" style="width: 240rpx;">
						<view style="width: 45rpx;height: 45rpx;" class="bsf f-c mr10">
							<image class="wh" :src="element.icon" mode="aspectFill"></image>
						</view>
						<view class="t-o-e">{{element.storeName}}</view>
					</view>
					<!-- <view style="color: #ff6560;">{{element.store.score}}分</view> -->
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props:{
			element: {
				type: Object,
				default: () => {}
			}
		},
		methods: {
			goToGoodsDetail(e) {
				this.$emit('onGoTo',e.id)
			}
		},
	}
</script>

<style>
</style>
