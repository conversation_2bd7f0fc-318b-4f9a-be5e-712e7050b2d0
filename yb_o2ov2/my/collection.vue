<template>
	<view class="p1" v-if="collectionList">
		<scroll-view scroll-y="true" class="wh" style="padding-bottom: 120rpx;">
			<u-swipe-action v-for="(item, index) in collectionList" @click="click" :show="item.show" :index="index" :options="options" @open="open" :key="item.id">
				<view class="flex p2 bf" style="border-bottom: 1px solid #e6e6e6;"  @click="go('navigateTo',`/yb_o2ov2/home/<USER>">
					<view style="width: 120rpx;height: 120rpx;" class="bs10 mr20">
						<image class="wh" :src="item.store.icon" mode=""></image>
					</view>
					<view>
						<view class="wei t-o-e f30">{{item.store.name}}</view>
						<view class="f24 c6 mt10">
							<text v-if="item.store.outSales>=0">销量{{item.store.outSales}} </text>
							<text v-if="item.store.distribution && item.store.distribution.startMoney>=0" class="ml20">起送${{item.store.distribution.startMoney}}</text></view>
						<view class="p-r f24 mt10 flex f-y-c f-w o-h pr40">
							<view v-if="item.store.discount.reduce.length !== 0"
								class="p-r f24 f-y-c f-w o-h" :style="expand===index?'height:auto':''">
								<block v-if="item.store.discount.reduce && item.store.discount.reduce.data.type === '1'">
									<!-- 循环满减 -->
									<view class="label-coupon f-y-c" style="color: #FA3534;border: 1rpx solid #FA3534;">
										<text>每满{{item.store.discount.reduce.data.fullMoney}}减{{item.store.discount.reduce.data.money}}</text>
									</view>
								</block>
								<block v-else-if="item.store.discount.reduce && item.store.discount.reduce.data.type === '2'">
									<!-- 阶梯满减 -->
									<view class="label-coupon f-y-c" style="color: #FA3534;border: 1rpx solid #FA3534;">
										<block v-if="ids<3" v-for="(text,ids) in item.store.discount.reduce.data.moneyArr"
											:key='ids'>
											<text>{{`满${text.fullMoney}减${text.money}`}}</text>
											<text v-if="ids !== item.store.discount.reduce.data.moneyArr.length - 1 && ids < 2"
												class="m01 f18" style="color: #d28f50;">|</text>
										</block>
									</view>
								</block>
							</view>
						</view>
					</view>
				</view>
			</u-swipe-action>
			<mescroll-empty v-if="collectionList.length===0" :option="{icon:'/static/empty/9.png',tip:'~ 暂无收藏 ~'}"></mescroll-empty>
		</scroll-view>
	</view>
</template>

<script>
	// import TabNav from '@/components/TabNav.vue'
	export default {
		components: {
			// TabNav
		},
		computed: {
			// scrollHeight() {
			// 	return this.wHeight - this.statusNavBarHeight - 80 / this.pxToRpxRate
			// }
		},
		data() {
			return {
				params:{
					page:1,
					size:10,
				},
				list: [{id: 1,show: false},{id: 2,show: false},{id: 3,show: false},{id: 4,show: false},{id: 5,show: false}],
				collectionList:[],
				options: [
					{
						text: '删除',
						style: {
							backgroundColor: '#dd524d'
						}
					}
				],
				showType: 0,
				current: 0,
				bgColor: '#fff',
			}
		},
		onLoad() {
			this.fetchData()
		},
		methods: {
			tabsChange(index){
				this.params.type = index
				this.refresh()
			},
			refresh(){
				this.params.page = 1
				this.fetchData()
			},
			async fetchData(type){
				let {data} =  await this.util.request({
					url: this.api.myCollection,
					method: 'GET',
					data:this.params
				})
				if(type !=='nextPage'){
						this.collectionList = data
					}else{
						if(data.length === 0){
							this.status = 'nomore'
							return
						}
						if(this.params.page === 1){
							this.collectionList = data
						}else{
							this.collectionList = this.collectionList.concat(data)
						}
					}
				this.status='loadmore'
			},
			nextPage(){
				if(this.status === 'loading'){return}
				this.params.page++
				this.fetchData('nextPage')
			},
			async click(index, index1) {
				if(index1 == 0) {
					 await this.util.modal('确认取消收藏该商店吗','取消收藏').then(res=>{
						this.util.request({
							url: this.api.scjk,
							method: 'GET',
							data:{
								collectionId:this.collectionList[index].store.id,
								type:'1'
							}
						})
					})
					this.collectionList.splice(index, 1);
					this.$u.toast(`取消成功`);
				}
			},
			open(index) {
				this.list[index].show = true;
				this.list.map((val, idx) => {
					if(index != idx) this.list[idx].show = false;
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	.label{
		color: #ff1e00;
		border: 1rpx solid #ff1e00;
		border-radius: 10rpx;
		padding: 0rpx 6rpx;
		margin-right: 10rpx;
	}
	.label-coupon {
		font-size: 20rpx;
		height: 36rpx;
		// line-height: 38rpx;
		color: #c57771;
		border: 1rpx solid #f0dbdb;
		border-radius: 8rpx;
		padding: 0rpx 8rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
</style>
