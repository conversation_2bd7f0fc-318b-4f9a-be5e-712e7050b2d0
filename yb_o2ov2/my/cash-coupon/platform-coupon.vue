<template>
	<view class="p02" style="padding-bottom: 220rpx;" :style="{paddingTop:`${statusNavBarHeight+20}px`}">
		<!-- navigation -->
		<view class="w100 t-c wei f32 bf p-f" style="top: 0;left: 0;z-index: 10;"
			:style="{height:`${statusNavBarHeight}px`,paddingTop:`${menuButtonTop}px`}">
			<view class="p-a" :style="{top:`${menuButtonTop}px`,left:`20rpx`,zIndex:4}" @click="go('back')">
				<text class="iconfont iconback f40"></text>
			</view>
			<view class="t-c f32 wei">
				<view>{{system.custom.systemRedbag || '平台红包'}}</view>
			</view>
		</view>
		<view class="p-f w100 bf" style="left: 0;z-index: 10;" :style="{top:`${statusNavBarHeight}px`}">
			<tab-nav :height="80" :activeColor="tColor" :bg-color="bgColor" :current-index="current" :list="tabs"
				@change="tabsChange"></tab-nav>
		</view>
		<!-- 可使用 -->
		<view class="mt60">
			<view v-for="(item,index) in list" :key="item" class="bf p2 bs20 mt20 p-r">
				<text class="p-a cf f20 label wei">{{item.lab || item.couponName}}</text>
				<view class="f-y-c" style="border-bottom: 1px dashed #ddd;">
					<view class="f-col f-g-1">
						<view class="f30 wei mt10">{{item.couponName}}</view>
						<view class="f24 c6 mt10">{{item.useExplain}}</view>
					</view>
					<view class="f-col t-r pb20" style="color: #FF5837;">
						<view class="f24">$<text class="f24 wei firstCase dis-in">{{item.money}}</text></view>
						<view class="f24" v-if="item.fullMoney<=0">无门槛</view>
						<view class="f24" v-else>满{{item.fullMoney}}可用</view>
					</view>
				</view>
				<view class="f-s f24 mt20 c9 mt10 f-bt f-y-c" @click="selectIndex = selectIndex===index?'':index">
					<view style="width: 490rpx;transition: .2s;" :style="selectIndex === index?'height:auto':'overflow: hidden;text-overflow: ellipsis;white-space: nowrap;'">
						<text v-if="item.receiveType=='3'">限首单使用,</text>
						<text v-if="item.receiveType=='3'">限新用户首单使用,</text>
						<text>{{item.explain}}</text>
						<!-- 1.领取2签到3新客专享4.下单返红包5支付有礼6发券宝7.弹窗优惠券8生日有礼9券包10兑换码11收藏有礼12会员卡13付费会员卡 -->
					</view>
					<view class="ml10"><text class="iconfont iconinto f20 c9" style="transition: .2s;" :style="selectIndex === index?'transform:rotate(-90deg)':'transform:rotate(90deg)'"></text></view>
					<view>
						<view v-if="item.useState === '1' "  class="btn-use" @click.stop="qsy(item)">待使用</view>
						<view v-else-if="item.useState === '2'" class="btn-use">已使用</view>
						<view v-else-if="item.useState === '3'" class="btn-use">已失效</view>
					</view>
				</view>
			</view>
<!-- 			<view v-for="(item,index) in 12" :key="item" class="bf p2 bs20 mt20 p-r" :style="index===0?'background:#F9ECC2;color:#6F4B25':''">
				<text class="p-a cf f20 label wei" :style="index===0?'background:#FAD088;color:#6F4B25':''">{{index===0?'会员红包':'天天神券'}}</text>
				<view class="f-y-c" style="border-bottom: 1px dashed #ddd;">
					<view class="f-col f-g-1">
						<view class="f30 wei">{{index===0?'会员红包':'天天神券'}}</view>
						<view class="f24" :class="index !=0?'c6':''">有效期至2021.08.13</view>
					</view>
					<view class="f-col mr20 t-c pb20" style="color: #FF5837;">
						<view class="f24">$<text class="f24 wei firstCase dis-in">6 .6</text></view>
						<view class="f24">满20可用</view>
					</view>
				</view>
				<view class="f-s f24 mt20" :class="index !=0?'c9':''" @click="selectIndex = selectIndex===index?'':index">
					<view style="width: 490rpx;transition: .2s;" :style="selectIndex === index?'height:auto':'overflow: hidden;text-overflow: ellipsis;white-space: nowrap;'">限商品现价+包装费-所有优惠满20元使用。限天天神券商店使用。限登录和收餐手机号为15287109711使用。</view>
					<view class="ml10"><text class="iconfont iconinto f24 c9" style="transition: .2s;" :style="selectIndex === index?'transform:rotate(-90deg)':'transform:rotate(90deg)'"></text></view>
					<view class="btn-use">去使用</view>
				</view>
			</view> -->
			<mescroll-empty v-if="list.length===0" :option="{icon:'/static/empty/8.png',tip:'~ 暂无红包 ~'}"></mescroll-empty>
			<u-loadmore v-if="list.length!==0" @loadmore="nextPage" :status="status" />
		</view>
	</view>
</template>

<script>
	import TabNav from '@/components/TabNav.vue'
	export default {
		components:{TabNav},
		data() {
			return{
				params:{
					state:1,//  1 可使用 2 已使用 3 已过期
					item:1,// 1平台红包 2 商店代金券
					page: 1,
					size: 10,
				},
				status:'loading',
				list:[],
				current:0,
				selectIndex:'',
				tabs: [{
					name: '待使用'
				}, {
					name: '已使用'
				}, {
					name: '已失效'
				}]
			}
		},
		onLoad() {
			this.fetchData()
		},
		computed: {
			
		},
		methods:{
			tabsChange(index){
				this.params.state = index + 1
				this.refresh()
			},
			refresh(){
				this.params.page = 1
				this.fetchData()
			},
			async fetchData(type){
				let {data} =  await this.util.request({
					url: this.api.wdyhq,
					method: 'GET',
					data:this.params
				})
				if(type !=='nextPage'){
						this.list = data
					}else{
						if(data.length === 0){
							this.status = 'nomore'
							return
						}
						if(this.params.page === 1){
							this.list = data
						}else{
							this.list = this.list.concat(data)
						}
					}
				this.status='loadmore'
			},
			nextPage(){
				if(this.status === 'loading'){return}
				this.params.page++
				this.fetchData('nextPage')
			},
			qsy(v){
				if(v.storeArr.length && v.storeArr[0].id){
					this.go('navigateTo',`/yb_o2ov2/home/<USER>
				}else{
					return
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	.firstCase{
		font-size:200%;
	}
	// .firstCase::first-letter{
	// 	font-size:200%;
	// }
	.payBox {
		position: fixed;
		bottom: 80rpx;
		width: 700rpx;
		height: 90rpx;
		display: flex;
		justify-content: space-between;
		color: #FFF;
		background: #333;
		border-radius: 50rpx;
		line-height: 90rpx;
		overflow: hidden;
	}
	.btn-use{
		width: 100rpx;
		font-size: 24rpx;
		border-radius: 10rpx;
		padding: 2rpx 4rpx;
		margin-left: 25rpx;
		text-align: center;
		color: #fff;
		background:linear-gradient(45deg,#ec8b3b,#ff1803) ;
	}
	.label{
		left: 0;
		top: 0;
		padding:4rpx 12rpx;
		border-radius: 0 0 20rpx 0;
		background: rgba(255,88,55,1);
	}
</style>
