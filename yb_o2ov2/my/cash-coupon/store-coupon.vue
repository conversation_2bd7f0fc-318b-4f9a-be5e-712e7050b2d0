<template>
	<view class="p02" style="padding-bottom: 220rpx;" :style="{paddingTop:`${statusNavBarHeight+80/pxToRpxRate}px`}">
		<!-- navigation -->
		<view class="w100 t-c wei f32 bf p-f" style="top: 0;left: 0;z-index: 10;"
			:style="{height:`${statusNavBarHeight}px`,paddingTop:`${menuButtonTop}px`}">
			<view class="p-a" :style="{top:`${menuButtonTop}px`,left:`20rpx`,zIndex:4}" @click="go('back')">
				<text class="iconfont iconback f40"></text>
			</view>
			<view class="t-c f32 wei">
				<view>商店代金券</view>
			</view>
		</view>
		<view class="p-f w100 bf" style="left: 0;z-index: 10;" :style="{top:`${statusNavBarHeight}px`}">
			<tab-nav :height="80" :activeColor="tColor" :bg-color="bgColor" :current-index="current" :list="tabs"
				@change="tabsChange"></tab-nav>
		</view>
		<!-- 可使用 -->
		<view class="mt30">
			<view v-for="(item,index) in list" :key="item" class="bf p2 bs20 mt10 p-r flex">
				<view class="mr20 mt10 bs20" style="width: 140rpx;height: 140rpx;">
					<image class="wh" :src="item.icon" mode="" ></image>
				</view>
				<view class="f-g-1">
					<view class="f-bt" style="border-bottom: 1px dashed #f5f6f9;">
						<view class="f-col pt10">
							<view class="f30 wei">{{item.couponName}}</view>
							<view class="f22 c6">{{item.useExplain}}</view>
						</view>
						<view class="f-col mr20 t-c pb10" style="color: #FF5837;margin-top: -16rpx;">
							<view class="f24">$<text class="f50 wei">{{Number(item.money)}}</text></view>
							<view class="f22" style="margin-top: -6rpx;">{{Number(item.fullMoney) === 0?'无门槛':`满${Number(item.fullMoney)}可用`}}</view>
						</view>
					</view>
					<view class="f-s c9 f20 mt10" @click="selectIndex = selectIndex===index?'':index">
						<view style="width: 340rpx;transition: .2s;" :style="selectIndex === index?'height:auto':'overflow: hidden;text-overflow: ellipsis;white-space: nowrap;'">可与其他活动优惠同时享受，在线付款专享</view>
						<view class="ml10"><text class="iconfont iconinto f20 c9" style="transition: .2s;" :style="selectIndex === index?'transform:rotate(-90deg)':'transform:rotate(90deg)'"></text></view>
						<view v-if="item.useState === '1' "  class="btn-use" @click.stop="go('navigateTo',`/yb_o2ov2/home/<USER>">待使用</view>
						<view v-else-if="item.useState === '2'" class="btn-use">已使用</view>
						<view v-else-if="item.useState === '3'" class="btn-use">已失效</view>
					</view>
				</view>
			</view>
			<view class="foot-btnc">
				<button @click="go('navigateTo',`/yb_o2ov2/my/cash-coupon/dh`)" :disabled="loading" class="tcbtn f-1 bf f-c f32 cf" :style="{background:tColor}">兑换卡券</button>
			</view>
			<!-- 空布局 -->
			<mescroll-empty v-if="list.length===0" :option="{icon:'/static/empty/5.png',tip:'~ 暂无代金券 ~'}"></mescroll-empty>
			<u-loadmore v-if="list.length!==0" @loadmore="nextPage" :status="status" />
		</view>
	</view>
</template>

<script>
	import TabNav from '@/components/TabNav.vue'
	export default {
		components:{TabNav},
		data(){
			return{
				params:{
					state:1,//  1 可使用 2 已使用 3 已过期
					item:2,// 1平台红包 2 商店代金券
					page: 1,
					size: 10,
				},
				status:'loading',
				list:[],
				current:0,
				selectIndex:'',
				tabs: [{
					name: '待使用'
				}, {
					name: '已使用'
				}, {
					name: '已失效'
				}]
			}
		},
		onLoad() {
			this.fetchData()
		},
		onShow() {
			this.fetchData()
		},
		methods:{
			tabsChange(index){
				this.params.state = index + 1
				this.refresh()
			},
			refresh(){
				this.params.page = 1
				this.fetchData()
			},
			async fetchData(type){
				let {data} =  await this.util.request({
					url: this.api.wdyhq,
					method: 'GET',
					data:this.params
				})
				if(type !=='nextPage'){
						this.list = data
					}else{
						if(data.length === 0){
							this.status = 'nomore'
							return
						}
						if(this.params.page === 1){
							this.list = data
						}else{
							this.list = this.list.concat(data)
						}
					}
				this.status='loadmore'
			},
			nextPage(){
				if(this.status === 'loading'){return}
				this.params.page++
				this.fetchData('nextPage')
			}
		}
	}
</script>

<style scoped lang="scss">
	.confirmBox {
		position: fixed;
		bottom: 0rpx;
		left: 0;
		width: 750rpx;
		height: 150rpx;
		background: #FFF;
		padding: 20rpx;
		padding-top: 0;
	}
	.btn-use{
		width: 100rpx;
		border-radius: 10rpx;
		font-size: 24rpx;
		margin-left: 25rpx;
		text-align: center;
		color: #fff;
		background:linear-gradient(45deg,#ec8b3b,#ff1803) ;
		
	}
</style>
