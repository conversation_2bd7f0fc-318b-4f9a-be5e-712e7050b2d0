<template>
	<view>
		<!-- 头部 start -->
		<view class="page-navigation-bar">
			<view class="go-icon">
				<text class="iconfont iconback f40 " @click="go('reLaunch','/yb_o2ov2/my/index')"></text>
			</view>
			<text>{{$t('user.page_title')}}</text>
		</view>
		<!-- 头部 end -->
		<view :style="{paddingTop:`${statusNavBarHeight}px`}">
			<u-cell-group :title="$t('user.info')" :titleStyle="{color:'#333',padding:'10rpx 28rpx 10rpx 26rpx;'}">
				<!-- <u-cell-item  title="头像" :titleStyle="{color:'#666'}" @click="chooseImg">
					<view slot="right-icon" class="bsf" style="width: 80rpx;height: 80rpx;"><image class="wh" :src="user.portrait || '/static/no.png'" mode=""></image></view>
				</u-cell-item> -->
				<u-cell-item :title="$t('user.avater')" :titleStyle="{color:'#666'}" :arrow='false'>
					<view slot="right-icon" class="bsf" style="width: 80rpx;height: 80rpx;">
						<image class="wh" :src="formUserData.portrait || '/static/no.png'" mode=""></image>
					</view>
				</u-cell-item>
				<u-cell-item :title="$t('user.nickname')" :titleStyle="{color:'#666'}"
					:value="formUserData.nickname" :arrow='false'></u-cell-item>
			</u-cell-group>
			<u-cell-group :title="$t('user.account_binding')"
				:titleStyle="{color:'#333',padding:'10rpx 28rpx 10rpx 26rpx;'}">
				<u-cell-item :titleStyle="{color:'#666'}" :title="$t('user.phone')"
					:value="formUserData.userTel" @click="showLTel = true"></u-cell-item>
				<!-- <u-cell-item :titleStyle="{color:'#666'}" :title="$t('user.lineAccount')"
					:value="formUserData.accountline"></u-cell-item> -->
				<u-cell-item :titleStyle="{color:'#666'}" :title="$t('user.language')" :value="currentLanguage"
					@click="handlerLanguage"></u-cell-item>

				<!-- 	<u-cell-item :titleStyle="{color:'#666'}"  title="登录密码" value="未设置">
					<view slot="icon" class="iconfont iconpassword f34 mr10"></view>
				</u-cell-item> -->
			</u-cell-group>
		</view>
		<u-popup v-model="showLanguage" mode="bottom" border-radius="20" :safe-area-inset-bottom="true">
			<u-cell-item :titleStyle="{color:'#666'}" :title="$t('user.traditional_taiwanese')" @click="handler('zh-TW')"></u-cell-item>
			<u-cell-item :titleStyle="{color:'#666'}" :title="$t('user.english')" @click="handler('en-US')"></u-cell-item>
		</u-popup>
		<u-popup v-model="showLTel" mode="bottom" border-radius="20" :safe-area-inset-bottom="true">
			<view class="phone-popup">
				<!-- <view class="popup-header">
					<view class="popup-title">{{$t('user.phone')}}</view>
					<view class="popup-subtitle">請輸入新的手機號碼並驗證</view>
				</view> -->

				<view class="popup-content">
					<!-- 手机号输入 -->
					<view class="input-group">
						<view class="input-label">{{$t('user.phone')}}</view>
						<u-input
							v-model="formUserData.userTel"
							:placeholder="$t('user.enter_phone')"
							type="number"
							maxlength="11"
							class="phone-input"
						/>
					</view>

					<!-- 验证码输入 -->
					<view class="input-group">
						<view class="input-label">{{$t('user.verification_code')}}</view>
						<view class="verification-row">
							<u-input
								v-model="verificationCode"
								:placeholder="$t('user.enter_verification_code')"
								type="number"
								maxlength="6"
								class="code-input"
							/>
							<u-button
								:disabled="!canSendCode || !formUserData.userTel"
								@click="sendVerificationCode"
								size="small"
								:type="canSendCode ? 'primary' : 'info'"
								class="send-code-btn"
							>
								{{ sendCodeText }}
							</u-button>
						</view>
					</view>
				</view>

				<view class="popup-footer">
					<u-button @click="cancelPhoneChange" class="cancel-btn">{{$t('common.showModal_cancelText')}}</u-button>
					<u-button type="primary" @click="submitTel" class="confirm-btn">{{$t('common.confirm')}}</u-button>
				</view>
			</view>
		</u-popup>
		<!-- 退出登录 -->
		<!-- 		<button class="btni p-a-xc f32 wei c0" :style="{background:tColor,color:fontColor}"
			@click="signOut">退出登录</button> -->
	</view>
</template>

<script>
	import utils from '@/common/utils.js'
	export default {
		data() {
			return {
				formUserData: {
					nickname: '',
					portrait: '',
					userTel: '',
					accountline: ''
				},
				showLanguage: false,
				showLTel: false,
				storage: null,
				currentLanguage: '',
				// 验证码相关
				verificationCode: '',
				sendCodeText: this.$t('user.get_verification_code'),
				canSendCode: true,
				countdown: 60
			}
		},
		created() {
			// 根据平台初始化存储方式
			// #ifdef H5
			this.storage = {
				getStorageSync: (key) => {
					return localStorage.getItem(key)
				},
				setStorageSync: (key, data) => {
					localStorage.setItem(key, data)
				}
			}
			// #endif
			
			// #ifndef H5
			this.storage = uni
			// #endif

			// 初始化语言设置
			this.currentLanguage = this.storage.getStorageSync('language') === 'en-US' ? this.$t('user.english') : this.$t('user.traditional_taiwanese')
		},
		onLoad() {
			this.fetchData()
		},
		methods: {
			chooseImg() {
				uni.chooseImage({
					sizeType: ['compressed'],
					count: 1,
					success: res => {
						this.src = res.tempFilePaths[0]
					}
				})
			},
			async signOut() {
				try {
					await this.util.modal('退出登录后将无法查看订单，重新登录即可查看订单', '退出登录')
				} catch (e) {
					return
				}
			},
			async fetchData() {
				const {
					data
				} = await this.util.request({
					url: this.api.xgyh,
					method: 'GET',
				})
				this.formUserData = data
			},
			handlerLanguage() {
				this.showLanguage = true
			},
			handler(type){
				// 检查是否与当前语言相同
				const currentLang = this.storage.getStorageSync('language');
				if (currentLang === type) {
					this.util.message(this.$t('user.same_language'), 3);
					this.showLanguage = false;
					return;
				}

				this.$i18n.changeLocale(type)
				this.showLanguage = false
				this.util.message(this.$t('user.switch_success'), 3, 1000)
				try {
					this.storage.setStorageSync('language', type);
					this.currentLanguage = type === 'en-US' ? this.$t('user.english') : this.$t('user.traditional_taiwanese')
				} catch (e) {
					console.log('语言切换报错')
				}
				// location.reload()
			},
			async submit() {
				await this.util.request({
					url: this.api.xgyh,
					method: 'POST',
					data: this.formUserData
				}).then(res => {
					if (res.code == 1) {
						this.util.message(res.msg || '修改成功', 3, 1000)
					}
				})
			},
			// 发送验证码
			async sendVerificationCode() {
				if (!this.formUserData.userTel) {
					this.util.message(this.$t('user.enter_phone'), 3);
					return;
				}

				try {
					const res = await this.util.request({
						url: this.api.sendSms, // 需要根据实际API调整
						method: 'POST',
						data: {
							userTel: this.formUserData.userTel,
							type: 'changePhone' // 修改手机号类型
						}
					});

					if (res.code === 1) {
						this.util.message(this.$t('user.verification_code_sent'), 1);
						this.startCountdown();
					} else {
						this.util.message(res.msg || '發送失敗，請重試', 3);
					}
				} catch (error) {
					this.util.message('發送失敗，請檢查網絡連接', 3);
				}
			},

			// 开始倒计时
			startCountdown() {
				this.canSendCode = false;
				this.countdown = 60;
				this.sendCodeText = `${this.countdown}s`;

				const timer = setInterval(() => {
					this.countdown--;
					this.sendCodeText = `${this.countdown}s`;

					if (this.countdown <= 0) {
						clearInterval(timer);
						this.canSendCode = true;
						this.sendCodeText = '重新發送';
					}
				}, 1000);
			},

			// 取消手机号修改
			cancelPhoneChange() {
				this.showLTel = false;
				this.verificationCode = '';
				this.canSendCode = true;
				this.sendCodeText = this.$t('user.get_verification_code');
				this.countdown = 60;
			},

			async submitTel() {
				if (!this.formUserData.userTel) {
					this.util.message(this.$t('user.enter_phone'), 3);
					return;
				}

				if (!this.verificationCode) {
					this.util.message(this.$t('user.enter_verification_code'), 3);
					return;
				}

				try {
					const res = await this.util.request({
						url: this.api.xgyh,
						method: 'POST',
						data: {
							userTel: this.formUserData.userTel,
							verificationCode: this.verificationCode
						}
					});

					if (res.code === 1) {
						this.util.message(res.msg || '修改成功', 1);
						this.showLTel = false;
						this.verificationCode = '';
						this.canSendCode = true;
						this.sendCodeText = this.$t('user.get_verification_code');
						this.countdown = 60;
						// 刷新用户数据
						this.fetchData();
					} else {
						this.util.message(res.msg || '修改失敗', 3);
					}
				} catch (error) {
					this.util.message('修改失敗，請檢查網絡連接', 3);
				}
			},
			getUserInfo() {
				const userInfo = this.storage.getStorageSync('userInfo')
				if (userInfo) {
					this.userInfo = JSON.parse(userInfo)
				}
			},
			saveUserInfo(data) {
				this.storage.setStorageSync('userInfo', JSON.stringify(data))
			}
		}
	}
</script>

<style scoped lang="scss">
	.btni {
		position: fixed;
		bottom: 80rpx;
		height: 100rpx;
		width: 700rpx;
		border-radius: 50rpx;
		line-height: 100rpx;
	}
	.p-30 {
		padding: 30rpx;
	}
	.mb-20 {
		margin-bottom: 20rpx;
	}
	.mt-30 {
		margin-top: 30rpx;
	}
	.flex-x-b {
		display: flex;
		justify-content: space-between;
	}

	// 手机号弹窗样式
	.phone-popup {
		padding: 40rpx 30rpx 30rpx;
		background: #fff;
		border-radius: 20rpx 20rpx 0 0;

		.popup-header {
			text-align: center;
			margin-bottom: 40rpx;

			.popup-title {
				font-size: 36rpx;
				font-weight: bold;
				color: #333;
				margin-bottom: 10rpx;
			}

			.popup-subtitle {
				font-size: 28rpx;
				color: #666;
			}
		}

		.popup-content {
			.input-group {
				margin-bottom: 30rpx;

				.input-label {
					font-size: 30rpx;
					color: #333;
					margin-bottom: 15rpx;
					font-weight: 500;
				}

				.phone-input {
					border: 2rpx solid #e5e5e5;
					border-radius: 12rpx;
					padding: 20rpx;
					font-size: 32rpx;

					&:focus {
						border-color: #007aff;
					}
				}

				.verification-row {
					display: flex;
					align-items: center;
					gap: 20rpx;

					.code-input {
						flex: 1;
						border: 2rpx solid #e5e5e5;
						border-radius: 12rpx;
						padding: 20rpx;
						font-size: 32rpx;

						&:focus {
							border-color: #007aff;
						}
					}

					.send-code-btn {
						flex-shrink: 0;
						width: 180rpx;
						height: 80rpx;
						font-size: 26rpx;
						border-radius: 12rpx;

						&:disabled {
							background-color: #f5f5f5;
							color: #999;
							border-color: #e5e5e5;
						}
					}
				}
			}
		}

		.popup-footer {
			display: flex;
			gap: 20rpx;
			margin-top: 40rpx;

			.cancel-btn {
				flex: 1;
				height: 88rpx;
				border: 2rpx solid #e5e5e5;
				background: #fff;
				color: #666;
				border-radius: 12rpx;
				font-size: 32rpx;
			}

			.confirm-btn {
				flex: 1;
				height: 88rpx;
				background: #007aff;
				color: #fff;
				border-radius: 12rpx;
				font-size: 32rpx;
				border: none;
			}
		}
	}
</style>