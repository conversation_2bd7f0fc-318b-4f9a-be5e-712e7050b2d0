<template>
	<view>
		<!-- 头部 start -->
		<view class="page-navigation-bar">
			<view class="go-icon">
				<text class="iconfont iconback f40 " @click="go('reLaunch','/yb_o2ov2/my/index')"></text>
			</view>
			<text>{{$t('user.page_title')}}</text>
		</view>
		<!-- 头部 end -->
		<view :style="{paddingTop:`${statusNavBarHeight}px`}">
			<u-cell-group :title="$t('user.info')" :titleStyle="{color:'#333',padding:'10rpx 28rpx 10rpx 26rpx;'}">
				<!-- <u-cell-item  title="头像" :titleStyle="{color:'#666'}" @click="chooseImg">
					<view slot="right-icon" class="bsf" style="width: 80rpx;height: 80rpx;"><image class="wh" :src="user.portrait || '/static/no.png'" mode=""></image></view>
				</u-cell-item> -->
				<u-cell-item :title="$t('user.avater')" :titleStyle="{color:'#666'}" :arrow='false'>
					<view slot="right-icon" class="bsf" style="width: 80rpx;height: 80rpx;">
						<image class="wh" :src="formUserData.portrait || '/static/no.png'" mode=""></image>
					</view>
				</u-cell-item>
				<u-cell-item :title="$t('user.nickname')" :titleStyle="{color:'#666'}"
					:value="formUserData.nickname" :arrow='false'></u-cell-item>
			</u-cell-group>
			<u-cell-group :title="$t('user.account_binding')"
				:titleStyle="{color:'#333',padding:'10rpx 28rpx 10rpx 26rpx;'}">
				<u-cell-item :titleStyle="{color:'#666'}" :title="$t('user.phone')"
					:value="formUserData.userTel" @click="showLTel = true"></u-cell-item>
				<!-- <u-cell-item :titleStyle="{color:'#666'}" :title="$t('user.lineAccount')"
					:value="formUserData.accountline"></u-cell-item> -->
				<u-cell-item :titleStyle="{color:'#666'}" :title="$t('user.language')" :value="currentLanguage"
					@click="handlerLanguage"></u-cell-item>

				<!-- 	<u-cell-item :titleStyle="{color:'#666'}"  title="登录密码" value="未设置">
					<view slot="icon" class="iconfont iconpassword f34 mr10"></view>
				</u-cell-item> -->
			</u-cell-group>
		</view>
		<u-popup v-model="showLanguage" mode="bottom" border-radius="20" :safe-area-inset-bottom="true">
			<u-cell-item :titleStyle="{color:'#666'}" :title="$t('user.traditional_taiwanese')" @click="handler('zh-TW')"></u-cell-item>
			<u-cell-item :titleStyle="{color:'#666'}" :title="$t('user.english')" @click="handler('en-US')"></u-cell-item>
		</u-popup>
		<u-popup v-model="showLTel" mode="bottom" border-radius="20" :safe-area-inset-bottom="true">
			<view class="p-30">
				<view class="f32 mb-20">{{$t('user.phone')}}</view>
				<u-input v-model="formUserData.userTel" :placeholder="$t('user.enter_phone')" />
				<view class="flex-x-b mt-30">
					<u-button @click="showLTel = false">{{$t('common.showModal_cancelText')}}</u-button>
					<u-button type="primary" @click="submitTel">{{$t('common.confirm')}}</u-button>
				</view>
			</view>
		</u-popup>
		<!-- 退出登录 -->
		<!-- 		<button class="btni p-a-xc f32 wei c0" :style="{background:tColor,color:fontColor}"
			@click="signOut">退出登录</button> -->
	</view>
</template>

<script>
	import utils from '@/common/utils.js'
	export default {
		data() {
			return {
				formUserData: {
					nickname: '',
					portrait: '',
					userTel: '',
					accountline: ''
				},
				showLanguage: false,
				showLTel: false,
				storage: null,
				currentLanguage: ''
			}
		},
		created() {
			// 根据平台初始化存储方式
			// #ifdef H5
			this.storage = {
				getStorageSync: (key) => {
					return localStorage.getItem(key)
				},
				setStorageSync: (key, data) => {
					localStorage.setItem(key, data)
				}
			}
			// #endif
			
			// #ifndef H5
			this.storage = uni
			// #endif

			// 初始化语言设置
			this.currentLanguage = this.storage.getStorageSync('language') === 'en-US' ? this.$t('user.english') : this.$t('user.traditional_taiwanese')
		},
		onLoad() {
			this.fetchData()
		},
		methods: {
			chooseImg() {
				uni.chooseImage({
					sizeType: ['compressed'],
					count: 1,
					success: res => {
						this.src = res.tempFilePaths[0]
					}
				})
			},
			async signOut() {
				try {
					await this.util.modal('退出登录后将无法查看订单，重新登录即可查看订单', '退出登录')
				} catch (e) {
					return
				}
			},
			async fetchData() {
				const {
					data
				} = await this.util.request({
					url: this.api.xgyh,
					method: 'GET',
				})
				this.formUserData = data
			},
			handlerLanguage() {
				this.showLanguage = true
			},
			handler(type){
				// 检查是否与当前语言相同
				const currentLang = this.storage.getStorageSync('language');
				if (currentLang === type) {
					this.util.message(this.$t('user.same_language'), 3);
					this.showLanguage = false;
					return;
				}

				this.$i18n.changeLocale(type)
				this.showLanguage = false
				this.util.message(this.$t('user.switch_success'), 3, 1000)
				try {
					this.storage.setStorageSync('language', type);
					this.currentLanguage = type === 'en-US' ? this.$t('user.english') : this.$t('user.traditional_taiwanese')
				} catch (e) {
					console.log('语言切换报错')
				}
				// location.reload()
			},
			async submit() {
				await this.util.request({
					url: this.api.xgyh,
					method: 'POST',
					data: this.formUserData
				}).then(res => {
					if (res.code == 1) {
						this.util.message(res.msg || '修改成功', 3, 1000)
					}
				})
			},
			async submitTel() {
				if (!this.formUserData.userTel) {
					this.util.message(this.$t('user.enter_phone'), 3)
					return
				}
				await this.util.request({
					url: this.api.xgyh,
					method: 'POST',
					data: {
						userTel: this.formUserData.userTel
					}
				}).then(res => {
					if (res.code == 1) {
						this.util.message(res.msg || '修改成功', 3)
						this.showLTel = false
					}
				})
			},
			getUserInfo() {
				const userInfo = this.storage.getStorageSync('userInfo')
				if (userInfo) {
					this.userInfo = JSON.parse(userInfo)
				}
			},
			saveUserInfo(data) {
				this.storage.setStorageSync('userInfo', JSON.stringify(data))
			}
		}
	}
</script>

<style scoped lang="scss">
	.btni {
		position: fixed;
		bottom: 80rpx;
		height: 100rpx;
		width: 700rpx;
		border-radius: 50rpx;
		line-height: 100rpx;
	}
	.p-30 {
		padding: 30rpx;
	}
	.mb-20 {
		margin-bottom: 20rpx;
	}
	.mt-30 {
		margin-top: 30rpx;
	}
	.flex-x-b {
		display: flex;
		justify-content: space-between;
	}
</style>