<template>
	<view class="">
		<view class="dlhd f-c-c">
			<view class="hdimg bsf">
				<image class="wh" :src="system.icon" mode=""></image>
			</view>
			<view class="c9 f26 p23">{{system.name}}</view>
		</view>
		<view class="foot-btnc posi-r">
			<button v-if="system.erviceType=='2' && system.serviceUrl" @click="zxkf" class="foot-btn" :style="{background:tColor}">在线客服</button>
			<button v-else open-type='contact' class="foot-btn" :style="{background:tColor}">在线客服</button>
		</view>
		<view class="foot-btnc posi-r" @click='util.makeTel(system.tel)'>
			<button class="foot-btn f-c" :style="{background:'transparent',border:'1px solid '+tColor,color:tColor}">
				<text :style='{color:tColor}' class='iconfont icondh mr20 f42'></text>联系电话</button>
		</view>
		<view class="posi-f b0 w100" style="height: 100rpx;" @click="bbh"></view>
	</view>
</template>

<script>
	import {
		mapState,
		mapActions
	} from 'vuex'
	export default {
		name: 'kf',
		components: {
			
		},
		data() {
			return {
				showSq: true
			}
		},
		onLoad(options) {
			// this.getSystem({
			// 	normal: 1
			// })
		},
		computed: {},
		methods: {
			zxkf() {
				// this.go({
				// 	t: 1,
				// 	url: '/yb_o2ov2/other/web-view?src=' + 'https://work.weixin.qq.com/kfid/kfcd5751f35cd3261b5'
				// })
				// #ifdef  MP-WEIXIN
				wx.openCustomerServiceChat({
				  extInfo: {url: this.system.serviceUrl},
				  corpId: this.system.corpId,
				  success(res) {},
				  fail(e){
					  // console.log(e)
				  }
				})
				// #endif	
			},
			bbh() {
				uni.showModal({
					title: '小程序版本号',
					content: '22.05.07'
				})
			},
		},
	}
</script>

<style scoped lang="scss">
	.kfimg {
		width: 400rpx;
		height: 400rpx;
	}

	.dlhd {
		padding: 130rpx 30rpx 80rpx;
	}

	.hdimg {
		width: 150rpx;
		height: 150rpx;
	}
</style>
