<template>
	<view class="page">
		<view class="page-navigation-bar">
			<view class="go-icon">
				<text class="iconfont iconback f40 " @click="handleBack"></text>
			</view>
			<text>{{$t('login.agree1')}}</text>
		</view>
		<view class="bf p23">
			消費者條款:

			<li>
				1.消費者可以在優力旺 U幣Go團隊的合作店家購物取得U幣，團隊裡的任何店家皆有提供購物自取贈U幣活動。
			</li>
			<li>
				2.消費者所得之U幣皆可在團隊裡任何店家自由購物抵扣，所集之U幣不得兌換現金，一年內未有任何消費，U幣歸零。
			</li>
			<li>
				3.團隊平台會不定時推出各種獎勵優惠給消費者。
			</li>
			<li>
				4.消費者如違反購買規則會被拉黑，且U幣消失，例如:訂餐後無任何理由，亦無告知店家，而不取餐者。
			</li>
			<li>
				5.開啟你的手機定位，可以幫你找到最接近的店家，加入我們 line ID: @un886，認證即成為會員。
			</li>
			<li>
				6.消費者在此平台消費一切皆以店家為主，消費者如有任何消費糾紛皆應與店家洽談，平台僅協助客戶與店家對接事宜。
			</li>
			<!-- <rich-text  :text="content" /> -->
			<!-- <mp-html :content="content" /> -->
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapActions
	} from 'vuex'
	import RichText from '@/components/RichText.vue'
	import util from '../../../common/we7/util.js';
	import api from '../../../common/api';
	export default {
		name: 'gywm',
		components: {
			RichText,
		},
		data() {
			return {
				loading: false,
				content: '',
			}
		},
		// async onLoad(options) {
		// 	this.getSystem()
		// 	this.util.setNT(options.t)
		// 	console.log('aaa',this.currency);
			
		// 	//1、用户服务协议，2、隐私权政策、3、到店自取服务协议,4积分商城积分规则5隐私政策
		// 	let c = ''
		// 	switch (+options.p) {
		// 		case 1:
		// 			await this.getDndcConfig()
		// 			c = this.ptagreement
		// 			break;
		// 		case 2:
		// 			await this.getDndcConfig()
		// 			c = this.yhagreement;
		// 			break;
		// 		case 3:
		// 			c = this.currency.selfAgreement;
		// 			break;
		// 		case 4:
		// 			let rest = await this.util.request({
		// 				'url': this.api.config,
		// 				data: {
		// 					ident: 'integralShop '
		// 				}
		// 			})
		// 			c = rest.data.details;
		// 			break;	
		// 		case 5:
		// 			await this.getDndcConfig()
		// 			c = this.yszz;
		// 			break;
		// 		case 6:
		// 			c = uni.getStorageSync('fwb');
		// 			break;
		// 		break;
		// 		case 13:
		// 			let agreement = await this.util.request({
		// 				'url': this.api.config,
		// 				data: {
		// 					ident: 'distributionSet '
		// 				}
		// 			})
		// 			c = agreement.data.agreement;
		// 			break;	
		// 		break;
		// 		case 14:
		// 			let storageset = await this.util.request({
		// 				'url': this.api.getStoreConfig,
		// 				data: {
		// 					ident: 'storageset',
		// 					storeId: 188
		// 				}
		// 			})
		// 			c = storageset.data.details;
		// 			break;
		// 		case 15:
		// 			c = uni.getStorageSync('cunjiu');
		// 			break;
		// 		break;
		// 	}
		// 	this.content = c
		// },
		computed: {
			...mapState({
				currency: state => state.config.currency,
			}),
		},
		methods: {
			handleBack() {
				const pages = getCurrentPages();
				if (pages.length > 1) {
					this.go('back');
				} else {
					// 如果没有上一页，跳转到首页
					this.go('reLaunch', '/yb_o2ov2/index/index');
				}
			},
			// async getDndcConfig(){
			// 	let res = await util.request({
			// 		'url': api.config,
			// 		data: {
			// 			ident: 'agreement'
			// 		},
			// 	})
			// 	this.ptagreement = res.data.ptagreement ? res.data.ptagreement :''
			// 	this.yhagreement = res.data.yhagreement ? res.data.yhagreement :''
			// 	this.yszz = res.data.yszz ? res.data.yszz :''
			// },
		},
	}
</script>
<style scoped lang="scss">
	.page {
		width: 100vw;
		height: 100vh;

		.content {
			width: 100%;
			margin-top: 40rpx;
			padding: 0 30rpx;
			box-sizing: border-box;

			.item {
				display: flex;
				width: 100%;
				height: 100rpx;
				justify-content: space-between;
				align-items: center;
				margin-top: 20rpx;
				background-color: #fff;
				border-radius: 20rpx;
				padding: 20rpx;
				box-sizing: border-box;

				.label {
					width: 30%;
					font-weight: 400;
					font-size: 28rpx;
					color: #333333;
				}

				.value {
					flex: 1;
					display: flex;
					justify-content: flex-end;
					align-items: center;
				}

				.ipt {
					height: 100%;
					font-weight: 400;
					font-size: 28rpx;
					color: #333333;
					text-align: end;
				}
			}

			.but {
				margin-top: 80rpx;
			}
		}
	}
</style>
