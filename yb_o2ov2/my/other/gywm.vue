<template>
	<view class="page">
		<view class="page-navigation-bar">
			<view class="go-icon">
				<text class="iconfont iconback f40 " @click="handleBack"></text>
			</view>
			<!-- <text>{{$t('issueAnInvoice.invoice_type')}}</text> -->
		</view>
		<view v-if="content.length" class="bf p23">
			<!-- <rich-text  :text="content" /> -->
			<mp-html :content="content" />
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapActions
	} from 'vuex'
	import RichText from '@/components/RichText.vue'
	import util from '../../../common/we7/util.js';
	import api from '../../../common/api';
	export default {
		name: 'gywm',
		components: {
			RichText,
		},
		data() {
			return {
				loading: false,
				content: '',
			}
		},
		async onLoad(options) {
			this.getSystem()
			this.util.setNT(options.t)
			console.log('aaa',this.currency);
			
			//1、用户服务协议，2、隐私权政策、3、到店自取服务协议,4积分商城积分规则5隐私政策
			let c = ''
			switch (+options.p) {
				case 1:
					await this.getDndcConfig()
					c = this.ptagreement
					break;
				case 2:
					await this.getDndcConfig()
					c = this.yhagreement;
					break;
				case 3:
					c = this.currency.selfAgreement;
					break;
				case 4:
					let rest = await this.util.request({
						'url': this.api.config,
						data: {
							ident: 'integralShop '
						}
					})
					c = rest.data.details;
					break;	
				case 5:
					await this.getDndcConfig()
					c = this.yszz;
					break;
				case 6:
					c = uni.getStorageSync('fwb');
					break;
				break;
				case 13:
					let agreement = await this.util.request({
						'url': this.api.config,
						data: {
							ident: 'distributionSet '
						}
					})
					c = agreement.data.agreement;
					break;	
				break;
				case 14:
					let storageset = await this.util.request({
						'url': this.api.getStoreConfig,
						data: {
							ident: 'storageset',
							storeId: 188
						}
					})
					c = storageset.data.details;
					break;
				case 15:
					c = uni.getStorageSync('cunjiu');
					break;
				break;
			}
			this.content = c
		},
		computed: {
			...mapState({
				currency: state => state.config.currency,
			}),
		},
		methods: {
			handleBack() {
				const pages = getCurrentPages();
				if (pages.length > 1) {
					this.go('back');
				} else {
					// 如果没有上一页，跳转到首页
					this.go('reLaunch', '/yb_o2ov2/index/index');
				}
			},
			async getDndcConfig(){
				let res = await util.request({
					'url': api.config,
					data: {
						ident: 'agreement'
					},
				})
				this.ptagreement = res.data.ptagreement ? res.data.ptagreement :''
				this.yhagreement = res.data.yhagreement ? res.data.yhagreement :''
				this.yszz = res.data.yszz ? res.data.yszz :''
			},
		},
	}
</script>
<style scoped lang="scss">
	.page {
		width: 100vw;
		height: 100vh;

		.content {
			width: 100%;
			margin-top: 40rpx;
			padding: 0 30rpx;
			box-sizing: border-box;

			.item {
				display: flex;
				width: 100%;
				height: 100rpx;
				justify-content: space-between;
				align-items: center;
				margin-top: 20rpx;
				background-color: #fff;
				border-radius: 20rpx;
				padding: 20rpx;
				box-sizing: border-box;

				.label {
					width: 30%;
					font-weight: 400;
					font-size: 28rpx;
					color: #333333;
				}

				.value {
					flex: 1;
					display: flex;
					justify-content: flex-end;
					align-items: center;
				}

				.ipt {
					height: 100%;
					font-weight: 400;
					font-size: 28rpx;
					color: #333333;
					text-align: end;
				}
			}

			.but {
				margin-top: 80rpx;
			}
		}
	}
</style>
