<template>
	<view class="mh100">
		<view class="gyhead posi-r">
			<view class="wh" :style="{background:tColor}"></view>
			<view class="posi-a t270 wh">
				<view class="m03">
					<view class="bf mb20 bs20 p3 minhe">
						<view class="iconwidth bs10">
							<mg-img :src="system.icon"></mg-img>
						</view>
						<view class="f-c c0 f32 mt90 f26 c3">{{system.name}}</view>
						<view class="f28 c6 mt30">
							<rich-text :text="system.introduction"></rich-text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapActions
	} from 'vuex'
	import RichText from '@/components/RichText.vue'
	export default {
		name: 'gywm',
		components: {
			RichText,
		},
		data() {
			return {
				loading: false,
				content: '',
				systemData: '',
			}
		},
		async onLoad(options) {
			
		},
		computed: {
			
		},
		methods: {},
	}
</script>
<style scoped lang="scss">

	.gyhead {
		width: 100%;
		height: 300rpx;
	}

	.t270 {
		top: 100rpx;
	}

	.iconwidth {
		width: 120rpx;
		height: 120rpx;
		top: 0;
		left: 50%;
		transform: translateX(-50%);
		position: absolute;
		//margin-top: -90rpx;
	}

	.minhe {
		min-height: 800rpx;
		margin-top: 60rpx;
	}
</style>
