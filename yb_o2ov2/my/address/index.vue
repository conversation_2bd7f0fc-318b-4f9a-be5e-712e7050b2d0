<template>
	<view class="bf wh">
		<scroll-view scroll-y="true" class="wh" style="padding-bottom: 120rpx;">
			<view class="page-navigation-bar">
				<view class="go-icon">
					<text class="iconfont iconback f40 " @click="go('back','/yb_o2ov2/my/index')"></text>
				</view>
				<text>{{$t('address.address_title')}}</text>
			</view>
			<template v-if="myAddress.length>0">
				<u-swipe-action v-for="(item, index) in myAddress" @click="click" :show="item.show" :index="index"
					:options="options" @open="open" :key="item.id">
					<view class="f-x-bt p23" style="border-bottom: 1px solid #fafafa;">
						<view class="f-col">
							<view class="f30 f-y-c">
								<text v-if="item.label" class="label"
									:style="{background:'#ecf7fd',color:'#5cb3e6'}">{{item.label}}</text>
								<text class="f30 wei">{{ item.address }}{{item.details}}</text>
							</view>
							<view class="c8 mt10 f24">
								<text class="mr10">{{item.userName}}</text>
								<text class="mr60">({{item.sex=='1'?$t('address.mr'):$t('address.ms')}})</text>
								<text>{{item.userTel}}</text>
							</view>
						</view>
						<view @click="go('navigateTo',`/yb_o2ov2/my/address/edit?id=${item.id}`)"><text
								class="iconfont iconedit f50"></text></view>
					</view>
				</u-swipe-action>
			</template>
			<mescroll-empty v-if="myAddress.length===0"
				:option="{icon:'/static/empty/6.png',tip:`~ ${$t('address.no_address')} ~`}"></mescroll-empty>
		</scroll-view>
		<view @click="go('navigateTo','/yb_o2ov2/my/address/edit')" class="t-c p-f w100 f28 bf wei c0"
			:style="{background:'#FFCC00',color:fontColor}"
			style="bottom: 0;left: 0;height: 120rpx;line-height: 100rpx;border-top: 1px solid #f5f6f9;">
			+{{$t('address.add_address')}}</view>
		<Load :show="showLoad"></Load>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				myAddress: [],
				list: [{
					id: 1,
					show: false
				}, {
					id: 2,
					show: false
				}, {
					id: 3,
					show: false
				}, {
					id: 4,
					show: false
				}, {
					id: 5,
					show: false
				}],
				disabled: false,
				btnWidth: 180,
				show: false,
				options: [{
					text: '删除',
					style: {
						backgroundColor: '#dd524d'
					}
				}],
				showLoad: true,
			};
		},
		onShow() {
			this.fetchData()
		},
		methods: {
			async fetchData() {
				let {
					data
				} = await this.util.request({
					url: this.api.getMyAddress,
					method: 'GET'
				})
				this.myAddress = data
				this.showLoad = false
			},
			async click(index, index1) {
				if (index1 == 0) {
					await this.util.modal('确认删除该收货地址吗', '删除地址').then(res => {
						this.util.request({
							url: this.api.scshdz,
							method: 'POST',
							data: {
								id: this.myAddress[index].id,
							}
						})
					})
					this.myAddress.splice(index, 1);
					// this.util.message('删除成功', 1, 1000)
					this.$u.toast(`删除成功`);
				}
			},
			// 如果打开一个的时候，不需要关闭其他，则无需实现本方法
			open(index) {
				// 先将正在被操作的swipeAction标记为打开状态，否则由于props的特性限制，
				// 原本为'false'，再次设置为'false'会无效
				this.list[index].show = true;
				this.list.map((val, idx) => {
					if (index != idx) this.list[idx].show = false;
				})
			}
		}
	};
</script>

<style scoped lang="scss">
	.label {
		width: 60rpx;
		height: 30rpx;
		border-radius: 5rpx;
		margin-right: 10rpx;
		font-size: 20rpx;
		font-weight: bold;
		line-height: 30rpx;
		text-align: center;
	}
</style>