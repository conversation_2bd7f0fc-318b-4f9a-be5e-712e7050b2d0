<template>
	<view class="page">
		<view class="page-navigation-bar">
			<view class="go-icon">
				<text class="iconfont iconback f40 " @click="go('back')"></text>
			</view>
			<text>{{$t('address.address_title')}}</text>
		</view>
		<view class="form-box">
			<u-form :model="form" ref="uForm" :label-style="{fontWeight:'bold'}">
				<u-form-item :label="$t('address.contacts')+':'" label-width="150">
					<view class="f-x-bt">
						<u-input style="width: 290rpx;" v-model="form.userName"
							:placeholder="$t('address.contacts_placeholder')" />
						<u-radio-group v-model="form.sex">
							<u-radio v-for="(item, index) in gender" active-color='#FFCC00' label-size="24" :key="index"
								:name="item.value" :disabled="item.disabled">
								{{ item.name }}
							</u-radio>
						</u-radio-group>
					</view>
				</u-form-item>
				<u-form-item :label="$t('address.phone')+':'" label-width="150"><u-input type="number"
						v-model="form.userTel" :placeholder="$t('address.phone_placeholder')" /></u-form-item>
				<u-form-item :label="$t('address.address')+':'" label-width="150">
					<u-cell-item @click="openMap" :border-bottom="false" paddingLeft="0">
						<text slot="icon"><text v-if="!form.address"
								class="c9 mr10 iconfont iconposition f28"></text></text>
						<text slot="title">
							<text v-if="!form.address">{{$t('address.address_placeholder')}}</text>
							<block v-else>
								<text class="dis-b f30">{{form.address.replace('台湾省', '台湾')}}</text>
								<!-- <text class="dis-b c9 f26">{{form.addressName}}</text> -->
							</block>
						</text>
					</u-cell-item>

				</u-form-item>
				<!-- <u-form-item :label="$t('address.house')+':'" label-width="150"><u-input v-model="form.details"
						:placeholder="$t('address.house_placeholder')" /></u-form-item> -->
				<u-form-item :label="$t('address.label')+':'" label-width="150">
					<view class="f-y-c">
						<text v-for="item in labelList" :key="item" class="label"
							:class="form.label===item?'active':'inactive'" @click="form.label=item">{{item}}</text>
					</view>
				</u-form-item>

			</u-form>
			<view class="but">
				<button class="btn" :style="{background:'#FFCC00',color:'#fff'}" type="default"
					@click="saveAddress">{{$t('address.save_Address')}}</button>
				<button v-if="type==='edit'" class="btn" type="default"
					@click="delAddress">{{$t('address.delete_Address')}}</button>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapActions
	} from 'vuex'
	export default {
		data() {
			return {
				type: 'add',
				form: {
					id: '',
					address: '',
					details: '',
					label: '',
					userName: '',
					userTel: '',
					sex: 1,
					lat: '',
					lng: ''
				},
				gender: [{
						name: this.$t('address.mr'),
						value: 1
					},
					{
						name: this.$t('address.ms'),
						value: 0
					}
				],
				labelList: [this.$t('address.family'), this.$t('address.company'), this.$t('address.school')]
			};
		},
		onLoad(option) {
			if (option.id) {
				this.util.setNT('编辑收货地址')
				this.type = 'edit'
				this.form.id = option.id
				this.fetchData()
			} else {
				this.util.setNT('新增收货地址')
			}
			this.setAddressForm(this.form)
		},
		onShow() {
			this.form = {
				...this.form,
				...this.tempFormData
			}
		},
		onHide() {
			this.setAddressForm(this.form)
		},
		computed: {
			...mapState('address', ['tempFormData']),
		},
		methods: {
			...mapActions('address', ['setAddressForm']),
			async fetchData() {
				let {
					data
				} = await this.util.request({
					url: this.api.saveUserAddress,
					method: 'GET',
					data: {
						id: this.form.id
					}
				})
				this.form = data
			},
			openMap() {
				// #ifdef  MP-WEIXIN
				uni.chooseLocation({
					success: (res) => {
						this.form.address = res.address
						this.form.details = res.name
						this.form.lat = res.latitude
						this.form.lng = res.longitude
					}
				})
				// #endif

				// #ifdef H5
				this.go('navigateTo', '/yb_o2ov2/home/<USER>/map?type=address')
				// #endif
			},
			async saveAddress() {
				let {
					data,
					code,
					msg
				} = await this.util.request({
					url: this.api.saveUserAddress,
					method: 'POST',
					data: this.form
				})
				if (code == 1) {
					this.util.message(msg, 1, 1000)
					setTimeout(() => {
						this.go('back')
					}, 1000)
				} else {
					this.util.message(msg, 3, 1000)
				}
			},
			async delAddress() {
				await this.util.modal('确认删除该收货地址吗', '删除地址').then(res => {
					this.util.request({
						url: this.api.scshdz,
						method: 'POST',
						data: {
							id: this.form.id
						}
					})
				})
				this.util.message('删除成功', 1, 1000)
				setTimeout(() => {
					this.go('back')
				}, 1000)
			}
		}
	};
</script>

<style scoped lang="scss">
	.page {
		display: flex;
		width: 100vw;
		height: 100vh;
		flex-direction: column;
	}

	.form-box {
		flex: 1;
		display: flex;
		width: 100%;
		flex-direction: column;
		background-color: #fff;
		padding: 0 30rpx;
		box-sizing: border-box;
	}

	.but {
		flex: 1;
		display: flex;
		align-items: center;
	}

	.btn {
		width: 100%;
	}

	.label {
		width: 100rpx;
		height: 50rpx;
		margin-right: 20rpx;
		line-height: 50rpx;
		border-radius: 10rpx;
		text-align: center;
	}

	.active {
		border: 1px solid #FFCC00;
		background: #FFCC00;
	}

	.inactive {
		border: 1px solid #f5f6f9;
	}
</style>