<template>
	<view>
		<!-- 头部 start -->
		<view class="posi-s w100 "
			:style="{top:0,height:`${statusNavBarHeight}px`,paddingTop:`${menuButtonTop}px`,zIndex:3,background:tColor,color:fontColor}">
			<!-- 返回 -->
			<view class="p-a" :style="{top:`${menuButtonTop}px`,left:`20rpx`,zIndex:4}" @click="go('back')">
				<text class="iconfont iconback f40"></text>
			</view>
			<view class="t-c f32 wei">
				<view>金豆明细</view>
			</view>
		</view>
		<!-- 头部 end -->
		<view class="w100">
			<!-- height 80rpx -->
			<tab-nav :activeColor="tColor" :isScroll="false" :bg-color="bgColor" :current-index="current" :list="tabs"
				@change="tabsChange"></tab-nav>
		</view>
		<scroll-view scroll-y="true" @scrolltolower="nextPage" :style="{height:`${scrollHeight}px`}">
			<view class="p02 bf">
				<view
					v-for="item in list" :key="item"
					class="f-x-bt p20" style="border-top: 1px solid #f5f6f9;"
					@click="go('navigateTo',`/yb_o2ov2/my/wallet/balance-detail?type=${item.type}`)">
					<view>
						<view>{{item.note}}</view>
						<view class="c9 f24">{{item.createdAt}}</view>
					</view>
					<view class="f-y-c">
						<text class="wei f36" :style="{color:tColor}">{{item.type==1?'+':'-'}}{{item.integral}}</text>
						<text class="iconfont iconinto c9 f24 ml10"></text>
					</view>
				</view>
			</view>
			<u-loadmore @loadmore="nextPage" :status="status" />
		</scroll-view>
	</view>
</template>

<script>
	import TabNav from '@/components/TabNav.vue'
	export default {
		components: {
			TabNav
		},
		computed: {
			scrollHeight() {
				return this.wHeight - this.statusNavBarHeight - 80 / this.pxToRpxRate
			}
		},
		data() {
			return {
				params:{
					page:1,
					size:10,
					type:0
				},
				list: [],
				showType: 0,
				current: 0,
				bgColor: '#fff',
				tabs: [{
					name: '全部'
				}, {
					name: '获取'
				}, {
					name: '消耗',
				}]
			}
		},
		onLoad() {
			this.fetchData()
		},
		methods: {
			tabsChange(index){
				this.params.type = index
				this.refresh()
			},
			refresh(){
				this.params.page = 1
				this.fetchData()
			},
			async fetchData(type){
				let {data} =  await this.util.request({
					url: this.api.integralDetails,
					method: 'GET',
					data:this.params
				})
				if(type !=='nextPage'){
						this.list = data
					}else{
						if(data.length === 0){
							this.status = 'nomore'
							return
						}
						if(this.params.page === 1){
							this.list = data
						}else{
							this.list = this.list.concat(data)
						}
					}
				this.status='loadmore'
			},
			nextPage(){
				if(this.status === 'loading'){return}
				this.params.page++
				this.fetchData('nextPage')
			}
		}
	}
</script>

<style>
</style>
