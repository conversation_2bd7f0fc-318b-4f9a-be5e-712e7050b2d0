<template>
	<view>
		<!-- 头部 start -->
		<view class="p-f w100 "
			:style="{top:0,height:`${statusNavBarHeight}px`,paddingTop:`${menuButtonTop}px`,background:changedColor,color:fontColor,zIndex:3}">
			<!-- 返回 -->
			<view class="p-a" :style="{top:`${menuButtonTop}px`,left:`20rpx`,zIndex:4}" @click="go('back')">
				<text class="iconfont iconback f40"></text>
			</view>
			<view class="t-c f32 wei">
				<view>{{pageTitile}}</view>
			</view>
		</view>
		<!-- 渐变背景 -->
		<view class="p-a t0 w100" style="height: 400rpx;z-index: -1;"
			:style="{background:bgColor,top:`${statusNavBarHeight}px`}"></view>
		<!-- main-content -->
		<view class="p2" :style="{paddingTop:`${statusNavBarHeight}px`}">
			<!-- 卡片 -->
			<view class="p3 mt60 p-r" :style="{background:tColor,borderRadius:`20rpx`}">
				<view class="f-raw wei" @click="go('navigateTo','/yb_o2ov2/my/integral/integral-detailed-list')">{{pageTitile}}<text
						class="iconfont iconinto f24"></text></view>
				<view class="wei l-h1 mt10" style="font-size: 100rpx;font-weight: 1400;">{{user.integral}}</view>
				<view class="mt40 wei">签到、下单、评论得{{jfName}}</view>
				<!-- 装饰图 -->
				<view class="p-a r0" style="width: 266rpx;height: 323rpx;top: -40rpx;transform: scale(.9);">
					<image class="wh" :src="base64Img.head" mode=""></image>
					<!-- 点击区域 -->
					<view class="p-a w100 b0" @click="" style="height: 100rpx;"></view>
				</view>
				
			</view>
			<!-- 做任务 -->
			<view class="card">
				<view class="title">
					<text class="main">做任务赚{{jfName}}</text>
					<text class="sub">今天能赚<text style="color:red">{{toDayNum}}</text>{{jfName}}</text>
				</view>
				<view class="f-col">
					<!-- 任务1 -->
					<view class="f-bt mb30 mt10" v-if="config.open=='1'">
						<view class="bs10 mr15 p-r" style="width: 120rpx;height: 120rpx;background: #FDF4CF;">
							<!-- <image class="wh" src="../../../static/no.png" mode=""></image> -->
							<image class="wh" :src="base64Img.task" mode=""></image>
							<view class="w100 p-a t-c wei" style="bottom: 10rpx;">+{{totalIntegral}}</view>
						</view>
						<view class="f-col f-g-1">
							<text class="f30 wei l-h1">连续签到得{{jfName}}</text>
							<text class="f22 c9 mt10">每天仅限1次</text>
						</view>
						<view class="f-c-c">
							<!-- <view class="click-sign" v-if="goSignin=='1'"  @click="ljqd" :disabled="disabled">立即签到</view> -->
							<view class="click-on gradientBg"  @click="ljqd">去签到</view>
							<!-- <view class="click-off" v-else>今日已签到</view> -->
						</view>
					</view>
					<!-- 任务2 -->
					<view class="f-bt mb30" v-if="currency.orderIntegralType!=3">
						<view class="bs10 mr15 p-r" style="width: 120rpx;height: 120rpx;">
							<image class="wh" :src="base64Img.task" mode=""></image>
							<view class="w100 p-a t-c wei t-o-e" style="bottom: 10rpx;" v-if="currency.integral">+{{currency.integral}}</view>
							<!-- tag -->
						<!-- 	<view class="w100 p-a t-c wei f18 t0 pt10"
								style="transform: rotate(-45deg) translateX(-32rpx) translateY(-36rpx);"
								:style="{background:tColor}">最高</view> -->
						</view>
						<view class="f-col f-g-1">
							<text class="f30 wei l-h1">下单得积分</text>
							<text class="f22 c9 mt10">订单完成后可获得相应的积分</text>
						</view>
						<view class="f-c-c">
							<!-- <view class="click-off">10点可领</view> -->
							<view class="click-on gradientBg" @click="go('reLaunch', '/yb_o2ov2/index/index')">去下单</view>
						</view>
					</view>
					<!-- 任务3 -->
					<view class="f-bt mb30"  v-if="currency.commentIntegralOpen!=2">
						<view class="bs10 mr15 p-r" style="width: 120rpx;height: 120rpx;">
							<image class="wh" :src="base64Img.task" mode=""></image>
							<view class="w100 p-a t-c wei t-o-e" style="bottom: 10rpx;" v-if="currency.commentType.commentIntegral">+{{currency.commentType.commentIntegral}}</view>
							<!-- tag -->
							<!-- <view class="w100 p-a t-c wei f18 t0 pt10"
								style="transform: rotate(-45deg) translateX(-32rpx) translateY(-36rpx);"
								:style="{background:tColor}">最高</view> -->
						</view>
						<view class="f-col f-g-1">
							<text class="f30 wei l-h1">评价得积分</text>
							<text class="f22 c9 mt10">每天仅限1次</text>
						</view>
						<view class="f-c-c">
							<view class="click-on gradientBg" @click="goComment">去评价</view>
						</view>
					</view>
				</view>
				<!-- 底部 -->
				<view class="t-c f24 c9 pb10">更多任务，敬请期待~</view>
			</view>
			<!-- 签到 -->
<!-- 			<view class="card"  v-if="config.open=='1'">
				<view class="f-x-bt mb20 f28" style="color: #C1A972;">
					<view v-if="config.continuity.length&&jsqd.totalDay>0" class="f-g-1">已经签到 <text class="cfc m01">{{jsqd.totalSignDay}}</text>
						天，再签到 <text class="cfc m01">{{jsqd.totalDay}}</text>天就能领大奖啦</view>
					<view class="f24">开启提醒</view>
				</view>
				<view class="p-r" v-if="jsqd && jsqd.weeklist.length">
					<view class="w100 p-a" style="height: 1px;top:25rpx;background: #FBE4B7;z-index: 0;"></view>
					<view class="f-x-bt p-r">
						<view class="f-col"  v-for="(v,i) in jsqd.weeklist" :key="i">
							<view style="width: 50rpx;height: 50rpx;">
								<image v-if="v.sign==1" class="wh" :src="base64Img.signSuccess" mode=""></image>
								<image v-else-if="v.hasjl&&(v.type==3||v.type==4)" class="wh" :src="base64Img.signRedGift" mode=""></image>
								<image v-else class="wh" :src="base64Img.signMoney" mode=""></image>
							</view>
							<view class="f24 mt10" style="color: #926D49;margin-left: -8rpx;" v-if="dataRecord">{{dataRecord[i].time.substring(5)}}</view>
						</view>
					</view>
				</view>
				
				<view class="p-r">
					<view class="w100 p-a" style="height: 1px;top:25rpx;background: #FBE4B7;z-index: 0;"></view>
					<view class="f-x-bt p-r">
						<view class="f-col"  v-for="i in 7" :key="i" @click="signed">
							<view style="width: 50rpx;height: 50rpx;">
								<image v-if="i===0" class="wh" :src="base64Img.signSuccess" mode=""></image>
								<image v-else-if="i===2||i===6" class="wh" :src="base64Img.signRedGift" mode=""></image>
								<image v-else class="wh" :src="base64Img.signMoney" mode=""></image>
							</view>
							<view class="f24 mt10" style="color: #926D49;margin-left: -8rpx;">{{getDay(i)}}</view>
						</view>
					</view>
				</view>
			</view> -->
			<!-- 红包兑换 -->
<!-- 			<view class="card">
				<view class="title"><text class="main">热门红包兑换</text></view>
				<view class="f-y-c f-w" >
					<view class="f-c-c mb30 mt30" style="width: 33%;" v-for="item in 6" :key="item">
						<view class="p-r" style="width: 120rpx;height: 120rpx;">
							<image class="wh" :src="base64Img.redGift" mode=""></image>
							<view class="w100 p-a t-c f20 wei crb" style="top:16rpx">$<text class="f34">2</text></view>
							<view class="w100 p-a t-c cf f22" style="bottom: 6rpx;">无门槛</view>
						</view>
						<text class="wei">无门槛红包</text>
						<text class="c9 f24">每天10点抢</text>
						<view class="click-on gradientBg f24 f-c">
							<image style="width: 30rpx;height: 30rpx;" :src="base64Img.iconMi" mode=""></image>
							<text>200兑</text>
						</view>
					</view>
				</view>
			</view> -->
		</view>
		<u-mask :show="show">
			<view class="p-a p-a-c w100" >
				<view class="mla" style="width: 650rpx;">
					<image class="wh" :src="base64Img.popup" mode="widthFix"></image>
				</view>
				<view @click="show = false" class="mt60 f-c"><text class="iconfont iconcancelorder f50 cf"></text></view>
			</view>
		</u-mask>
	</view>
</template>

<script>
	import {
		mapState,
	} from 'vuex'
	import utils from '@/common/utils'
	import {
		utilMixins
	} from "@/common/util-mixins.js"
	export default {
		data() {
			return {
				base64Img:{
					head:require('../src/img/head'),
					task:require('../src/img/task'),
					redGift:require('../src/img/redGift'),
					iconMi:require('../src/img/iconMi'),
					signMoney:require('../src/img/signMoney'),
					signSuccess:require('../src/img/signSuccess'),
					signRedGift:require('../src/img/signRedGift'),
					popup:require('../src/img/popup')
				},
				show:false,
				
				goSignin: '',
				dataRecord: '',
				totalSignDay: 0, //已连续签到多少天
				totalDay: 0,
				mySignData: '',
				config: {},
				dataList: [],
				showLoading: true,
				storeInfo: {},
				disabled: false,
				totalIntegral:'',
			}
		},
		mixins: [utilMixins],
		computed: {
			...mapState({
				currency: state => state.config.currency,
			}),
			bgColor() {
				return `linear-gradient(180deg, ${this.changedColor} 30%, rgba(245,245,245,0))`
			},
			changedColor(){
				return utils.jjldColor(this.tColor,30)
			},
			jfName() {
				return this.system.custom.integral
			},
			pageTitile() {
				return '我的' + this.jfName
			},
			toDayNum(){
				let ToIntegral = 0,ToCommentIntegral = 0
				if(this.currency.orderIntegralType =='1' || this.currency.orderIntegralType =='2' && this.currency.integral){
					ToIntegral = this.currency.integral
				}
				if(this.currency.commentIntegralOpen=='1' && this.currency.commentType.commentIntegral){
					ToCommentIntegral = this.currency.commentType.commentIntegral
				}
				return this.totalIntegral + ToIntegral+ ToCommentIntegral
			},
			jsqd() {
				if (this.dataRecord && this.config.oneDay) {
					let arr = this.dataRecord,
						totalSignDay = 0,
						nowtime = this.timeToDate(this.dateToTime()).substring(0, 10),
						index = arr.findIndex(item => item.time == nowtime)
					// console.log('本周签到记录', this.dataRecord,nowtime,)
					// console.log('今天', index)
					for (let i = 0; i < arr.length; i++) {
						if (i < index) {
							// console.log("已签到", i, arr[i])
							if (arr[i].sign == 1) {
								arr[i].type = 1
								arr[i].btnName = "已签到"
							} else {
								arr[i].type = 2
								// arr[i].btnName = "补签"
								// console.log("补签", i, arr[i])
							}
						} else if (i == index) {
							if (arr[i].sign == 1) {
								arr[i].type = 1
								arr[i].btnName = "已签到"
							} else {
								arr[i].type = 3
								arr[i].btnName = "签到"
								// console.log("签到", i, arr[i])
							}
						} else {
							arr[i].type = 4
							// arr[i].btnName = "待签到"
							// console.log("待签到", i, arr[i])
						}
						arr[i].name = i + 1
						//签到天数
						if (arr[i].sign == 1) {
							totalSignDay++
						}
					}
					totalSignDay = +totalSignDay
					//显示奖励
					for (let c in this.config.continuity) {
						let jlitem = arr.find((v, i) => i + 1 == this.config.continuity[c].days)
						if (jlitem) {
							jlitem.hasjl = 1
						}
					}
					let jlsz = this.config.continuity.sort(function(a, b) {
							return a.days - b.days
						}),
						zqitem = jlsz.find(v => v.days > totalSignDay)
					// console.log(jlsz, zqitem)
					let totalDay = zqitem ? zqitem.days - totalSignDay : 0,
						weeklist = arr
					// console.log('连续天数', totalSignDay)
					// console.log('签到记录2', arr)
					// console.log('返回数据', totalSignDay, totalDay, weeklist)
					return {
						totalSignDay,
						totalDay,
						weeklist
					}
				}
			}
		},
		async onLoad(){
			await this.getpageconfig()
			this.getSystem()
			var nowtime = utils.formatTime(new Date()),
				nowstmp = new Date().getTime();
			// console.log(nowtime, nowstmp)
			this.weekSignRecord()
			this.mySign()
		},
		methods: {
			signed() {
				this.show = true
			},
			getDay(day){
				return utils.addDate(day)
			},
			async getpageconfig() {
				let {
					data
				} = await this.util.request({
					'url': this.api.config,
					data: {
						ident: 'signin'
					}
				})
				this.config = data
				this.totalIntegral = (+data.oneDay[0].integral) + (+data.twoDay[0].integral) + (+data.threeDay[0].integral) + (+data.fourDay[0].integral) + (+data.fiveDay[0].integral) + (+data.sixDay[0].integral) + (+data.sevenDay[0].integral)
				if (data.open != 1) {
					uni.showModal({
						title: '提示',
						content: '签到功能已关闭',
						showCancel: false,
						// success: (res) => {
						// 	this.go({
						// 		t: 6,
						// 		url: '/yb_o2ov2/index/index'
						// 	})
						// }
					});
				}
			},
			//本周签到记录
			async weekSignRecord() {
				let {
					data
				} = await this.util.request({
					'url': this.api.bzqd,
					method: 'POST',
				})
				this.dataRecord = data.record
				this.showBtn()
				this.showLoading = false
			},
			//我的签到
			async mySign() {
				let {
					data
				} = await this.util.request({
					'url': this.api.wdqdsj,
					method: 'POST',
				})
				this.mySignData = data
				// console.log('我的签到', data)
			},
			async showBtn() {
				let dataType = this.jsqd.weeklist.map(v => ({
					type: v.type
				}))
				// console.log(dataType[this.jsqd.totalSignDay].type)
				if (dataType[this.jsqd.totalSignDay].type == 3) {
					// console.log('今天未签到')
					this.goSignin = '1'
				} else {
					// console.log('今天已签到')
					this.goSignin = '2'
				}
			},
			async ljqd() {
				this.go('navigateTo',`/yb_o2ov2/my/signin/index`)
				// if (!await this.checkLogin()) return
				// this.disabled = true
				// let day = this.jsqd.totalSignDay + 1
				// // console.log(day)
				// // return
				// if (this.goSignin == '1') {
				// 	let res = await this.util.request({
				// 		'url': this.api.qd,
				// 		method: 'POST',
				// 		data: {
				// 			day: day,
				// 		},
				// 	})
				// 	this.weekSignRecord();
				// 	this.mySign();
				// 	if (res) {
				// 		this.util.message('签到成功', 1)
				// 	} else {
				// 		this.disabled = false
				// 	}
				// 	// console.log(res.message)
				// } else {
				// 	this.disabled = false
				// }
			},
			goComment(){
				let pages = getCurrentPages(),
				url = {category: "平台首页",name: {id: "myOrder", name: "我的订单"},params: "platform"},
				type = 'navigateTo'
				if(pages[0].__route__==='yb_o2ov2/index/index'&&pages[1]===undefined){
					pages[0].$vm.changeTab(url)
				}else{
					this.go(type,`/yb_o2ov2/index/index?changeTab=${JSON.stringify(url)}`)
				}
			},
		},
	}
</script>

<style scoped lang="scss">
	.gradientBg{
		background: linear-gradient(45deg , #F7C356 , #FADA56);
	}
	.card {
		border-radius: 20rpx;
		overflow: hidden;
		padding: 20rpx;
		margin-top: 20rpx;
		background: #ffffff;

		.title {
			padding-bottom: 20rpx;
			margin-bottom: 30rpx;
			border-bottom: 1px solid #f5f6f9;

			.main {
				font-size: 34rpx;
				font-weight: bold;
				margin-right: 10rpx;
			}

			.sub {
				font-size: 24rpx;
				color: #999
			}
		}

		.click-sign {
			width: 126rpx;
			border-radius: 10rpx;
			border: 1px solid #000;
			padding: 6rpx 0;
			font-size: 24rpx;
			text-align: center;
			font-weight: bold;
			background: #efe1b273;
			color: #000;
		}

		.click-off {
			width: 126rpx;
			border-radius: 10rpx;
			padding: 6rpx 0;
			font-size: 24rpx;
			text-align: center;
			background: #BCBCBD;
			color: #fff;
		}

		.click-on {
			width: 126rpx;
			border-radius: 10rpx;
			padding: 6rpx 0;
			font-size: 24rpx;
			text-align: center;
			font-weight: bold;
			color: #000;
		}
	}
</style>
