<template>
	<view class="pb130 ovfl h100">
		<!-- 头部 start -->
		<view class="posi-s w100 "
			:style="{top:0,height:`${statusNavBarHeight}px`,paddingTop:`${menuButtonTop}px`,zIndex:3,background:tColor,color:fontColor}">
			<!-- 返回 -->
			<view class="p-a" :style="{top:`${menuButtonTop}px`,left:`20rpx`,zIndex:4}" @click="go('back')">
				<text class="iconfont iconback f40"></text>
			</view>
			<view class="t-c f32 wei">
				<view>商品詳情</view>
			</view>
		</view>
		<!-- 头部 end -->
		<u-swiper :list="swiper" name='icon'></u-swiper>
		<view class='p3 bf mb20'>
			<view class="f-x-bt">
				<view class='t-o-e f34 wei'>{{dlDatl.name}}</view>
			</view>
			<view class="f-bt mt10">
				<view class='cfa f30 mb10' :style="{color:tColor}">
					<view>
						<text v-if="dlDatl.score>0">{{dlDatl.score}}</text>
						<text v-if="dlDatl.score>0" class="f22 nowei c9">{{jfName}}</text>
						<text v-if="dlDatl.score>0 && dlDatl.money>0" class="nowei c9">+</text>
						<text v-if="dlDatl.money>0">{{dlDatl.money}}<text class="f22 c9">{{dw}}</text></text>
					</view>
					<view v-if="dlDatl.type==1" class="t-o-e t-d-l c9 f22 wei4">{{sl+dlDatl.price}}</view>
					<!-- <view v-else class="f-y-c c9 f22 nowei">红包金额：{{dlDatl.price}}</view> -->
				</view>
				<view>
					<view class="f24 c9" v-if="dlDatl.stock-dlDatl.convertNum>-1">剩余{{dlDatl.stock-dlDatl.convertNum}}件</view>
					<!-- <view class="f24 c9 t-r">库存：{{dlDatl.stock}}</view> -->
				</view>
			</view>
		</view>
		<view class="bf p3">
			<view class="f32 wei">商品詳情</view>
			<view v-if="dlDatl.notice" class="mt30">
				<rich-text :text="dlDatl.notice"></rich-text>
			</view>
		</view>

		<view class="dbbtnc" :style="{bottom: isIpx?'40rpx':0}">
			<view class="dbbtn">
				<button @click="ljdh" class="rt f-c" :style="{background:tColor}">立即兑换</button>
			</view>
		</view>
		<view v-if="isIpx" class='bgf'></view>
		<u-popup v-model="showTime" mode="bottom" closeable :height="popupHeight" border-radius="20">
			<view class="bf c3 bs2000 pb140">
				<view class='f24 p3 pb0 bor-b'>
					<view class="bf cout f-row pb30">
						<view class="f-g-0 coutl mr30">
							<view class="imgw bs10 bf7">
								<mg-img :src="dlDatl.icon" />
							</view>
						</view>
						<view class="f-g-1 f-y-bt">
							<view class="wei4 f32 t-o-e c3">{{dlDatl.name}}</view>
							<view class="f-x-bt mt20 cf7 f36 wei4">
								<view class="cfa" :style="{color:tColor}">
									<text v-if="dlDatl.score>0" class="">{{dlDatl.score}}</text>
									<text v-if="dlDatl.score>0" class="f22 nowei c9">{{jfName}}</text>
									<text v-if="dlDatl.score>0 && dlDatl.money>0" class="nowei c9">+</text>
									<text v-if="dlDatl.money>0">{{dlDatl.money}}<text class="f22 c9">{{dw}}</text></text>
								</view>
							</view>
						</view>
					</view>
				</view>
				<view class="f-x-bt p3 bor-b" v-if="dlDatl.type==1">
					<view class="f28 wei4">外送方式</view>
					<view>
						<view class="bf f-x-bt">
							<view @click="clickMode(v)" v-for="(v,i) in ModeArr" :key='i' class="pstypec f-c ml30" :style="{color:v.value==params.deliveryMode?tColor:'',borderColor:v.value==params.deliveryMode?tColor:''}">
								<text class="iconfont f28 mr10" :class="[v.icon]" :style="{color:v.value==params.deliveryMode?tColor:''}"></text>
								<text class="f24">{{v.name}}</text>
							</view>
						</view>
					</view>
				</view>
				<block v-if="dlDatl.type==1">
					<block v-if="params.deliveryMode==1">
						<view class="p3">
							<block v-if="wmAddress.details">
								<view class="f-s-ac" @click="showAddress=true">
									<text class="f20 mr10 f-s-0"
										style="padding: 0rpx 8rpx;color: #5cb3e6;background: #ecf7fd;border-radius: 5rpx;">{{wmAddress.label}}</text>
									<text class="wei f36 f-g-1 t-o-e">{{wmAddress.details}}</text>
									<text class="iconfont iconinto f24"></text>
								</view>
								<view class="c9 f28 mt10">
									<text class="mr20">{{wmAddress.userName}} {{wmAddress.sex}}</text>
									<text>{{wmAddress.userTel}}</text>
								</view>
							</block>
							<block v-else>
								<view class="f-s-ac" @click="go('navigateTo', '/yb_o2ov2/my/address/edit')">
									<text class="wei f38 f-g-1 t-o-e" :style="{color:tColor}">请添加收货地址</text>
									<text class="iconfont iconinto f24"></text>
								</view>
							</block>
						</view>
					</block>
					<block v-if="params.deliveryMode==2">
						<view class="p3">
							<block v-if="ziAddress.address">
								<view class="f-s-ac" @click="showAddress2=true">
									<text class="wei f36 f-g-1 t-o-e">{{ziAddress.address}}</text>
									<text class="iconfont iconinto f24"></text>
								</view>
								<view class="c9 f28 mt10">
									<text class="mr20">{{ziAddress.name}} ({{ziAddress.linkMan}})</text>
									<text>{{ziAddress.tel}}</text>
								</view>
							</block>
							<block v-else>
								<view class="f-s-ac" @click="showAddress2=true">
									<text class="wei f38 f-g-1 t-o-e" :style="{color:tColor}">请选择營業地址</text>
									<text class="iconfont iconinto f24"></text>
								</view>
							</block>
						</view>
					</block>
				</block>
				<view class="f-x-bt p3 bor-t">
					<view class="f28 wei4">兑换数量</view>
					<view>
						<!-- <number-box :min="1" :max="maxNum" @change="bindChange"></number-box> -->
						<u-number-box :min="1" :max="maxNum" @change="bindChange"></u-number-box>
					</view>
				</view>
				<view v-if="params.deliveryMode==1&&dlDatl.type==1" class="f-x-bt p3 bor-b bor-t">
					<view class="f28 wei4">外送运费</view>
					<view>{{sl+dlDatl.deliveryMoney}}</view>
				</view>
				<view class="p3 f28 t-r">
					<view>共{{params.convertNum}}份，合计
						<text v-if="dlDatl.score>0" class="cfa">{{xjMoney.totalScore}}</text>
						<text v-if="dlDatl.score>0">{{jfName}}</text>
						<text v-if="dlDatl.score>0 && dlDatl.money>0">+</text>
						<text v-if="dlDatl.money>0" class="cfa">{{sl+xjMoney.total}}</text>
					</view>
				</view>
				<view class="dbbtnc" :class="{mb30:isIpx}">
					<button :disabled="loading" :loading="loading" @click="qrdh" :style='{background:tColor}' class="w100 cf f34 qrdh">确认兑换</button>
				</view>
			</view>
		</u-popup>
		<!-- 选择外卖收货地址 -->
		<u-popup v-model="showAddress" mode="bottom" closeable :height="popupHeight" border-radius="20">
			<view class="p-r">
				<view class="f32 wei t-c bf posi-s" style="top:0;height: 100rpx;line-height: 100rpx;">选择收货地址</view>
				<view class="f32 t-c p-a"
					style="bottom:0;height: 130rpx;width: 750rpx;z-index: 1;background: linear-gradient(0deg, #fff, rgba(255,255,255,0));">
					<view class="mla  bs20" style="width: 700rpx;height: 70rpx;line-height: 70rpx;"
						:style="{background:tColor,color:fontColor}"
						@click="go('navigateTo', '/yb_o2ov2/my/address/edit')"> + 新增收货地址
					</view>
				</view>
				<scroll-view scroll-y="true" :style="{height:`${popupHeight-100}rpx`}">
					<view class="f-col" style="padding-bottom: 120rpx;">
						<view v-for="item in myAddressList" :key="item" class="p2 flex"
							@click="chooseAddress(item)">
							<text style="width: 40rpx;" class="iconfont iconposition f28 pt10"></text>
							<view class="f-g-1 pb30" style="border-bottom: 1px solid #f5f6f9;">
								<view class="f-s-ac">
									<text class="f20 mr10 f-s-0"
										style="padding: 0rpx 8rpx;color: #5cb3e6;background: #ecf7fd;border-radius: 5rpx;">{{item.label}}</text>
									<text class="wei f30 f-g-1 t-o-e">{{item.details}}</text>
									<text class="iconfont iconeditfill c9 f28" @click="go('navigateTo',`/yb_o2ov2/my/address/edit?id=${item.id}`)"></text>
								</view>
								<view class="c9 f24">
									<text class="mr20">{{item.userName}} {{item.sex}}</text>
									<text>{{item.userTel}}</text>
								</view>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</u-popup>
		<!-- 选择自取收货地址 -->
		<u-popup v-model="showAddress2" mode="bottom" closeable :height="popupHeight" border-radius="20">
			<view class="p-r">
				<view class="f32 wei t-c bf posi-s" style="top:0;height: 100rpx;line-height: 100rpx;">选择營業地址</view>
				<scroll-view scroll-y="true" :style="{height:`${popupHeight-100}rpx`}">
					<view class="f-col" style="padding-bottom: 120rpx;">
						<view v-for="item in zitiList" :key="item" class="p2 flex"
							@click="chooseAddress2(item)">
							<text style="width: 40rpx;" class="iconfont iconposition f28 pt10"></text>
							<view class="f-g-1 pb30" style="border-bottom: 1px solid #f5f6f9;">
								<view class="f-s-ac">
									<text class="wei f30 f-g-1 t-o-e">{{item.address}}</text>
									<text class="c9 f28">ID:{{item.id}}</text>
								</view>
								<view class="c9 f24">
									<text class="mr20">{{item.name}} ({{item.linkMan}})</text>
									<text>{{item.tel}}</text>
								</view>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</u-popup>
		<load v-if="showLoading"></load>
	</view>
</template>

<script>
	import {
		mapState,
		mapMutations,
		mapActions
	} from 'vuex'
	import RichText from '@/components/RichText.vue'
	import utils from '@/common/utils.js'
	import mgImg from '@/components/common/mg-img.vue'
	import {
		utilMixins
	} from "@/common/util-mixins.js"
	export default {
		name: 'js-details',
		components: {
			RichText,
			mgImg,
		},
		data() {
			return {
				swiper: [{icon: ''}],
				showLoading: true,
				loading: false, //加载
				payObj: {},
				showTime: false, //底部弹出层
				wmAddress: '', //收货地址
				myAddressList: [], //地址
				ziAddress: '', //營業地址
				params: {
					selfId: '', //自取id
					goodsId: '', //商品Id
					convertNum: '1', //兑换数量
					deliveryMode: '', //1快递2到店
					userAddId: '', //快递id
				},
				dlDatl: '',
				config: '',
				ModeArr: [],
				showGg: false,
				zitiList: {}, //自取点弹窗信息
				maxNum: 1,
				showAddress: false,
				showAddress2: false,
			}
		},
		async onLoad(options) {
			let id
			if (options.scene) {
				id = decodeURIComponent(options.scene)
			} else {
				id = options.id
			}
			this.id = id
			this.getSystem()
			// this.util.setNT(this.jfName + '商城-商品詳情')
			// this.getConfig()
			this.getData()
			// uni.$on('changeChoose', (e) => {
			// 	if (e.hasOwnProperty('addInfo')) {
			// 		this.refreshAddress(e.addInfo)
			// 	}
			// 	console.log('changeChoose', e)
			// })
			console.log('%c options ',
				'color: white; background-color: #2274A5', options);
		},
		onUnload() {
			// uni.$off('changeChoose');
		},
		onShow() {
			this.getziList()
		},
		mixins: [utilMixins],
		computed: {
			jfName() {
				return this.system.custom.integral
			},
			xjMoney() {
				if (this.dlDatl) {
					let num = +this.params.convertNum,
						score = +this.dlDatl.score,
						money = +this.dlDatl.money,
						yf = +(this.params.deliveryMode == 1 && this.dlDatl.type == 1 ? this.dlDatl.deliveryMoney || 0 : 0),
						total = 0,
						totalScore = 0
					total = +(num * money + yf).toFixed(2) //总金额
					totalScore = num * score //总
					// console.log(total, totalScore)
					return {
						total,
						totalScore
					}
				}
			},
			popupHeight() {
				return this.wHeight / 2 * this.pxToRpxRate
			},
		},
		methods: {
			...mapActions(["getConfig"]),
			// async getConfig() {
			// 	let {
			// 		data
			// 	} = await this.util.request({
			// 		'url': this.api.config,
			// 		data: {
			// 			ident: 'integral'
			// 		}
			// 	})
			// 	this.config = data
			// },
			async getData() {
				//获取商品詳情
				let {
					data
				} = await this.util.request({
					'url': this.api.jfspxq,
					mask: 1,
					data: {
						id: this.id
					},
				})
				this.swiper[0].icon = data.icon
				this.dlDatl = data
				await this.getLoginInfo()
				this.getziList()
				this.maxNum = data.stock
				this.params.goodsId = data.id
				if (data.deliveryMode.indexOf(1) > -1) {
					this.ModeArr.push({
						icon: 'iconddys',
						name: '快递外送',
						value: '1',
					})
				}
				if (data.deliveryMode.indexOf(2) > -1) {
					this.ModeArr.push({
						icon: 'iconshangjia',
						name: '到店自取',
						value: '2',
					})
				}
				if (this.ModeArr.length == 1 && data.deliveryMode.indexOf(2) > -1) {
					this.params.deliveryMode = 2
				} else {
					this.params.deliveryMode = 1
				}
				//如果兑换类型type为1为实物兑换，deliveryMode==1为快递发货
				//获取到詳情数据或打开遮罩
				this.showLoading = false
			},
			//获取地址
			async getziList() {
				if (this.dlDatl.type == '1') {
					//收货地址
					let res1 = await this.util.request({
						'url': this.api.wdshdz,
					})
					if (res1.data.length > 0) {
						this.wmAddress = res1.data[0]
						this.myAddressList = res1.data
					}
					//營業地址
					let res = await this.util.request({
						'url': this.api.jfscztlb,
					})
					this.zitiList = res.data
					this.ziAddress = res.data[0]
				}
			},
			refreshAddress(e) {
				this.wmAddress = e
			},
			//自取列表显示
			ztshow(e) {
				this.showGg = true
			},
			//自取列表选择
			chooseAdd(v) {
				this.ziAddress = v
				console.log('chooseAdd', this.ziAddress)
			},
			//选择快递外送还是到店自取
			clickMode(v) {
				this.util.showLoading('设置中...')
				this.params.deliveryMode = v.value
				setTimeout(() => {
					uni.hideLoading()
				}, 500)
				console.log(v.name)
			},
			chooseAddress(item) {
				this.wmAddress = item
				this.params.userAddId = item.id
				this.showAddress = false
			},
			chooseAddress2(item) {
				this.ziAddress = item
				this.params.selfId = item.id
				this.showAddress2 = false
			},
			//商品数量加减
			bindChange(value) {
				console.log('Changenum', value)
				this.params.convertNum = value.value
				// if (value>this.maxNum) {
				// 	return this.util.message('超出库存限制', 3)
				// }
			},
			//点击立即兑换
			async ljdh() {
				if(!this.isLogin){
					this.go('navigateTo',`/yb_o2ov2/my/login`)
					return
				}
				this.showTime = true
			},
			//确认兑换提交
			async qrdh() {
				// deliveryMode == 1是快递发货
				//dlDatl.type=='2'是虚拟兑换
				if (this.dlDatl.type == '2') {
					this.params.selfId = ''
					this.params.userAddId = ''
				} else {
					if (this.params.deliveryMode == '1') {
						console.log('收货地址', this.wmAddress)
						if (!this.wmAddress) {
							return this.util.message('请选择收货地址后下单', 3)
						}
						this.params.userAddId = this.wmAddress.id
						this.params.selfId = ''
					} else {
						console.log('營業地址', this.zitiList, this.ziAddress)
						if (!this.ziAddress) {
							return this.util.message('请选择營業地址后下单', 3)
						}
						this.params.userAddId = ''
						this.params.selfId = this.ziAddress.id
					}
				}
				console.log('%c options ',
					'color: white; background-color: #2274A5', this.params);
				this.loading = true
				let orderRes = await this.util.request({
					'url': this.api.jfspxd,
					method: 'POST',
					mask: '下单中',
					data: this.params,
				})
				console.log('ljdh orderRes', orderRes, this.xjMoney.total, this.xjMoney.totalScore)
				if (orderRes.code==1) {
					// console.log('有现金兑换', orderRes, this.xjMoney.total)
					this.util.message('提交成功', 1, 1000)
					utils.stfn(() => {
						if (this.xjMoney.total > 0) {
							this.getPayConfig()
							let data = {}
							data.orderId = orderRes.data
							data.orderType = 3,
							data.money = this.xjMoney.total,
							this.setPayInfo(data)
							this.go('redirectTo', '/yb_o2ov2/home/<USER>/index')
						} else {
							this.go('redirectTo', '/yb_o2ov2/my/integral/integral-record')
						}
					})
				} else {
					this.loading = false
					return this.util.message(orderRes.msg || orderRes.data, 3, 2000)
				}
			},
			getPayConfig() {
				this.getConfig({
					name: 'payConfig',
					api: this.api.config,
					data: { ident: 'payConfig' }
				})
			},

		},
		// onShareAppMessage() {
		// 	return {
		// 		title: this.co.name,
		// 		imageUrl: this.getSingleImg(this.co.media[0]),
		// 	}
		// },
	}
</script>

<style scoped lang="scss">
	.bor-b {
		border-bottom: 1px solid #EFF3F6;
	}

	.bor-t {
		border-top: 1px solid #EFF3F6;
	}

	.dbbtnc {
		position: fixed;
		bottom: 0;
		width: 100%;
		padding: 30rpx;

		.dbbtn {
			height: 96rpx;
			border-radius: 90rpx;
			overflow: hidden;
			display: flex;
			box-shadow: 0rpx 8rpx 18rpx 5rpx rgba(221, 221, 221, 0.9);

			.rt {
				color: #fff;
				width: 100%;
				height: 100%;
				border-radius: 0;
				font-size: 34rpx;
				padding: 0;
			}
		}
	}

	.imgw {
		width: 130rpx;
		height: 130rpx;
	}

	.pstypec {
		width: 180rpx;
		height: 58rpx;
		border: 1rpx solid #ddd;
		border-radius: 28px;
	}

	.qrdh {
		border-radius: 48rpx;
	}
	
	.pb140{
		padding-bottom: 140rpx;
	}
	.ovfl{
		overflow: scroll
	}
</style>
