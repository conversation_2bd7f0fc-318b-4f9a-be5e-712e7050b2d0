<template>
	<view class="mh100 pb130" v-if="jfName">
		<view class="bf">
			<view class="f-c-c p30">
				<view class="f28">当前{{jfName}}</view>
				<view class="f56 cf7 wei" :style="{color:tColor}">{{user.integral}}</view>
			</view>
			<view class="f-x-bt">
				<view class="f-c p3" @click='integralMall'>
					<text class="iconfont iconjfsc mr10 cf7 f34"></text>
					<text>{{jfName}}商城</text>
				</view>
				<view class="f-c p3" @click='integralRule'>
					<text class="iconfont iconjfgz mr10 cf7 f34"></text>
					<text>{{jfName}}规则</text>
				</view>
			</view>
		</view>
		<view class="bf mt30">
			<view class="f32 wei p3 bor-b">{{jfName}}记录</view>
			<view class='p3 bf bor-b' v-for="(v,i) in list" :key="i">
				<view class="f-x-bt">
					<view class='t-o-e f28'>{{v.note}}</view>
					<view class="cf7 f28" :style="{color:tColor}"><text v-if="v.type=='1'">+</text><text v-else if="v.type=='2'">-</text>{{v.integral}}</view>
				</view>
				<view class="f-x-bt">
					<view class='c9 f24 m10'>{{v.createdAt}}</view>
				</view>
			</view>
		</view>
		<mescroll-empty v-if="list.length===0" :option="{icon:'/static/empty/9.png',tip:'~ 暂无订单 ~'}"></mescroll-empty>
		<u-loadmore v-if="list.length!==0" @loadmore="nextPage" :status="status" />
	</view>
</template>

<script>
	import {
		mapState,
		mapActions
	} from 'vuex'
	
	export default {
		name: 'myIntegral',
		components: {
		},
		data() {
			return {
				params: {
					page: 1,
					size: 10,
				},
				refreshLoading:false,
				status:'loading',
				list: [],
				integral: '0',
			}
		},
		async onLoad() {
			this.getSystem().then(() => {
				this.util.setNT('我的' + this.jfName)
			})
			// await this.getLoginInfo()
			this.integral = this.user.integral
			this.params.page = 1
			this.getList()
		},
		computed: {
			jfName() {
				return this.system.custom.integral
			},
		},
		methods: {
			async refresh() {
				this.refreshLoading = true
				this.params.page = 1
				await this.getList()
				setTimeout(()=>{
					this.refreshLoading = false
				},500)
			},
			async getList(type) {
				this.status = 'loading'
				let {
					data
				} = await this.util.request({
					'url': this.api.jfmx,
					mask: 1,
					data: this.params,
				})
				if(type !=='nextPage' || this.params.page === 1){
					this.list = data
				}else{
					if(data.length === 0){
						this.status = 'nomore'
						return
					}
					this.list = this.list.concat(data)
				}
				this.status = 'loadmore'
			},
			nextPage() {
				if (this.status === 'loading') { return }
				this.params.page++
				this.getList('nextPage')
			},
			integralMall() {
				this.go('redirectTo','/yb_o2ov2/my/integral/integral-mall')
			},
			integralRule() {
				this.go('navigateTo',`/yb_o2ov2/my/other/gywm?t=${this.jfName}规则&p=4`)
			},
		},
	}
</script>

<style scoped lang="scss">
	.bor-b {
		border-bottom: 1px solid #EFF3F6;
	}
</style>
