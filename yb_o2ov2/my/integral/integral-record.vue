<template>
	<view class="pb130">
		<view class="w100 bf">
			<tab-nav :height="80" :activeColor="tColor" :bg-color="bgColor" :current-index="current" :list="tabs"
				@change="tabsChange"></tab-nav>
		</view>
		<view class='bf bs20 f24 p3 m23' v-for="(v,i) in list" :key="i"
			@click="go({t:1,url:'order-dl?id=' + v.id})">
			<view class="f-x-bt pb20 b-b-e c3">
				<view class="f24 wei4">订单号：{{v.outTradeNo}}</view>
				<view class="f24 wei4" v-if="v.state=='1'">尚未付款</view>
				<view class="f24 wei4 cf7" v-if="v.deliveryMode =='1' && v.state=='2'">待发货</view>
				<view class="f24 wei4 cf7" v-if="v.deliveryMode =='2' && v.state=='2'">待核销</view>
				<view class="f24 wei4 c1f" v-if="v.state=='3'">已发货</view>
				<view class="f24 wei4 c3f" v-if="v.state=='4'">已完成</view>
			</view>
			<view class="bf f-row p20">
				<view class="f-g-0 mr30">
					<view class="imgw bs10 bf7">
						<mg-img :src="v.goodsIcon" />
					</view>
				</view>
				<view class="f-g-1 f-y-bt">
					<view class="wei4 f30 t-o-e2 c6">{{v.goodsName}}</view>
					<view class="f-x-bt">
						<view class="cfa t-o-e wei4">
							<text v-if="v.score>0">{{v.score}}</text>
							<text v-if="v.score>0" class="f26 ml10 nowei">{{jfName}}</text>
							<text v-if="v.score>0 && v.money>0" class="nowei">+</text>
							<text v-if="v.money>0"><text class="f22">{{sl}}</text>{{v.money}}</text>
						</view>
					</view>
				</view>
			</view>
			<view class="p20 t-r mr20">
				<view class="f28 wei4 cfa">
					<text class="f24 c9 mr20">共计{{v.convertNum}}件</text>
					<text class="c3">实付：</text>
					<text v-if="v.score>0">{{v.score}}</text>
					<text v-if="v.score>0" class="f26 ml10 nowei">{{jfName}}</text>
					<text v-if="v.score>0 && v.money>0" class="nowei">+</text>
					<text v-if="v.money>0"><text class="f22">{{sl}}</text>{{v.money}}</text>
				</view>
			</view>
			<view class="pt20 mr20 b-t-e" v-if="v.state=='3' && v.deliveryMode=='1'">
				<view class="f28 c3 wei4 f-x-e">
					<view class="obtn b-d bwid bs30" :style="{color:tColor,borderColor:tColor}"
						@click.stop="qrsh(v.id)">確認取貨</view>
				</view>
			</view>
			<view v-if="v.deliveryMode=='2'">
				<view class="pt20 mr20 b-t-e f-x-bt f28">
					<view class="c3 wei4" :style="{color:tColor}">自取订单</view>
					<view v-if="v.selfCode">核销码：<text :style="{color:tColor}">{{v.selfCode}}</text></view>
				</view>
				<view class="f28 c3 wei4 f-x-e mt20" v-if="v.state!='4'">
					<view class="obtn b-d bwid bs30" :style="{color:tColor,borderColor:tColor}"
						@click.stop="wyqh(v.id)">我已取货</view>
				</view>
			</view>
		</view>
		<mescroll-empty v-if="list.length===0" :option="{icon:'/static/empty/9.png',tip:'~ 暂无订单 ~'}"></mescroll-empty>
		<u-loadmore v-if="list.length!==0" @loadmore="nextPage" :status="status" />
	</view>
</template>

<script>
	import TabNav from '@/components/TabNav.vue'
	import mgImg from '@/components/common/mg-img.vue'
	import {
		mapState,
		mapActions
	} from 'vuex'
	export default {
		name: 'integralRecord',
		components: {
			TabNav,mgImg,
		},
		data() {
			return {
				current: 0,
				refreshLoading:false,
				status:'loading',
				tabs: [{
					name: '未完成',
				}, {
					name: '已完成',
				}],
				list: [],
				params: {
					page: 1,
					size: 10,
					type: 1,
				},
			}
		},
		onLoad(options) {
			this.getSystem()
			// this.util.setNT('兑换记录')
			this.params.page = 1
			this.fetchData()
		},
		computed: {
			jfName() {
				return this.system.custom.integral
			},
		},
		methods: {
			tabsChange(index){
				this.params.type = index + 1
				this.refresh()
			},
			async refresh() {
				this.refreshLoading = true
				this.params.page = 1
				await this.fetchData()
				setTimeout(()=>{
					this.refreshLoading = false
				},500)
			},
			async fetchData(type) {
				this.status = 'loading'
				let { data } = await this.util.request({
					'url': this.api.jfdd,
					data: this.params,
				})
				if(type !=='nextPage' || this.params.page === 1){
					this.list = data
				}else{
					if(data.length === 0){
						this.status = 'nomore'
						return
					}
					this.list = this.list.concat(data)
				}
				this.status = 'loadmore'
				// console.log(11,this.list)
			},
			nextPage() {
				if (this.status === 'loading') { return }
				this.params.page++
				this.fetchData('nextPage')
			},
			async qrsh(id) {
				try {
					await this.util.modal('您確認取貨吗？')
					let res = await this.util.request({
						'url': this.api.jfqrsh,
						mask: 1,
						method: 'POST',
						data: {
							id: id
						},
					})
					if (res) {
						this.util.message('收货成功', 1, 1000)
						//utils.swnb(1000)
						this.fetchData()
					}
				} catch (e) {}
			},
			async wyqh(id) {
				try {
					await this.util.modal('是否已收到商品？')
					let res = await this.util.request({
						'url': this.api.jfqrsh,
						mask: 1,
						method: 'POST',
						data: {
							id: id
						},
					})
					if (res) {
						this.util.message('收货成功', 1, 1000)
						//utils.swnb(1000)
						this.fetchData()
					}
				} catch (e) {}
			},
		},
	}
</script>

<style scoped lang="scss">
	.imgw {
		width: 100rpx;
		height: 100rpx;
	}

	.bor-b {
		border-bottom: 1px solid #EFF3F6;
	}

	.pt110 {
		padding-top: 110rpx;
	}

	.bwid {
		width: 175rpx;
	}

	.c1f {
		color: #1FBF5F;
	}

	.c3f {
		color: #999;
	}
</style>
