<template>
	<view class="bf">
		<!-- 头部 start -->
		<view class="posi-s t0 w100 "
			:style="{top:0,height:`${statusNavBarHeight}px`,paddingTop:`${menuButtonTop}px`,zIndex:3,background:'#fff'}">
			<!-- 返回 -->
			<view class="p-a" :style="{top:`${menuButtonTop}px`,left:`20rpx`,zIndex:4}" @click="go('back')">
				<text class="iconfont iconback f40"></text>
			</view>
			<view class="t-c f32 wei">
				<view>{{pageTitile}}</view>
			</view>
		</view>
		<!-- 头部 end -->
		<scroll-view scroll-y @scrolltolower="nextPage" :style="{height:`${wHeight-statusNavBarHeight}px`}">
			<view class="f-row pb20 c0" style="border-bottom: 24rpx solid #F6F6F6;">
				<view class="f-1">
					<view class="p13">
						<view class="f26">可用{{jfName}}</view>
						<view class="f60 wei" style="padding-left: 0rpx;" :style="{color:tColor}">{{user.integral}}</view>
					</view>
					<view class="p30 f30 f-row l-h1" style="margin-top: 100rpx;">
						<view @click='myIntegral' class="f-1 t-c b-re" style="border-color: #333;">{{jfName}}明细</view>
						<view @click="integralRecord" class="f-1 t-c">兑换记录</view>
					</view>
				</view>
				<view class="f-1 f-c">
					<view class="topimg">
						<mg-img :src="`${onImgurl}/jfsct.png`"></mg-img>
					</view>
				</view>
			</view>
			<!-- 轮播图 -->
			<view class="p230">
				<u-swiper :list="swiper" name='icon'></u-swiper>
			</view>
			<!-- 公告 -->
			<block v-if="ggList.length">
				<view class="f-s-ac p230">
					<text class="f32 mr10 wei">公告</text>
					<u-notice-bar class="f-g-1" color="#666" fontSize="24" padding="18rpx 24rpx 18rpx 0" :volume-icon="false" type="none"
						mode="vertical" :list="ggList"></u-notice-bar>
				</view>
			</block>
			<!-- 热门推荐 -->
			<block v-if="hotList.length">
				<view class="p230">
					<view class="f32 wei">热门推荐</view>
				</view>
				<view class='bf f-row o-x-s p23'>
					<view @click="go('navigateTo','/yb_o2ov2/my/integral/integral-detail?id=' + v.id)"
						class="bf f-g-0 goodsc mr30 bs15 b-s-1" v-for="(v,i) in hotList" :key='i'>
						<view class="goodsimg">
							<mg-img :src="v.icon"></mg-img>
						</view>
						<view class='jfgb'>
							<view class="f-bt">
								<text v-if="v.type=='2'" class="bf2 cf f24 mr10 boxs f-g-0">红</text>
								<text class="t-o-e f-g-1">{{v.name}}</text>
							</view>
							<view class="cfa" :style="{color:tColor}">
								<view class="t-o-e f30">
									<text v-if="v.score>0">{{v.score}}</text>
									<text v-if="v.score>0" class="f22 nowei c9">{{jfName}}</text>
									<text v-if="v.score>0 && v.money>0" class="nowei c9">+</text>
									<text v-if="v.money>0">{{v.money}}<text class="f22 c9">{{dw}}</text></text>
								</view>
								<view class="t-o-e t-d-l c9 f22 wei4">${{v.price}}</view>
							</view>
						</view>
					</view>
				</view>
			</block>
			<!-- 商品列表 -->
			<view class="p03">
				<!-- height 80rpx -->
				<tab-nav :activeColor="tColor" :isScroll="false" :list="tabs"
					@change="tabsChange"></tab-nav>
			</view>
			<GoodsList :color='tColor' :list='dataList'></GoodsList>
			<mescroll-empty v-if="list.length===0" :option="{icon:'/static/empty/10.png',tip:'~ 暂无商品 ~'}"></mescroll-empty>
			<u-loadmore v-else @loadmore="nextPage" :status="status" />
		</scroll-view>
	</view>
</template>

<script>
	import {
		mapState,
		mapActions
	} from 'vuex'
	import TabNav from '@/components/TabNav.vue'
	import mgImg from '@/components/common/mg-img.vue'
	import GoodsList from './components/GoodsList.vue'
	export default {
		name: 'jfsc',
		components: {
			mgImg,TabNav,GoodsList
		},
		data() {
			return {
				dataList:[],//商品
				swiper: [],//轮播图
				hotList: [],//热门商品
				params: {
					typeId: '',
					page: 1,
					size: 10,
					sort: '',
				},
				ggList: [],//公告
				tabs: [],
				status:'loadmore',
			}
		},
		async onLoad(options) {
			await this.getLoginInfo()
			this.init()
		},
		onShow() {
			if (this.uId) {
				this.refreshUser({
					nomask: 1,
					get: 1,
					now: 1,
				})
			}
		},
		computed: {
			jfName() {
				return this.system.custom.integral
			},
			pageTitile() {
				return this.jfName + '商城'
			},
		},
		methods: {
			...mapActions('dndc', ['getSwitchConfig']),
			init(){
				//请求分类 、轮播图、公告信息
				this.getCategory()
				//商品列表
				this.getList()
				//热门商品列表
				this.getHotList()
			},
			myIntegral() {
				this.go('navigateTo','/yb_o2ov2/my/integral/my-integral')
			},
			integralRecord() {
				this.go('navigateTo','/yb_o2ov2/my/integral/integral-record')
			},
			tabsChange(e) {
				this.params.typeId = this.tabs[e].id
				this.dataList = []
				this.refresh()
			},
			refresh(){
				this.params.page = 1
				this.getList()
			},
			nextPage(){
				if(this.status !== 'loadmore')return
				this.params.page++
				this.getList()
			},
			//请求商品列表
			async getList() {
				let {
					data
				} = await this.util.request({
					'url': this.api.jfsplb,
					method: 'POST',
					data: this.params,
				})
				let list  = data
				if(this.params.page === 1){
						this.dataList = list
					}else{
						if(list.length < this.params.size){
							this.dataList = this.dataList.concat(list)
							this.status = 'nomore'
							return
						}
						this.dataList = this.dataList.concat(list)
					}
				this.status='loadmore'
			},
			//请求热门商品
			async getHotList() {
				let {
					data
				} = await this.util.request({
					'url': this.api.jfsplb,
					method: 'POST',
					data: {
						typeId: '',
						page: 1,
						size: 10,
						sort: '1',
					},
				})
				this.hotList = this.hotList.concat(data)
				this.showLoading = false
			},
			//请求分类数据
			async getCategory() {
				let res = await this.util.request({
					'url': this.api.jffl,
					method: 'POST',
					data: {},
				})
				this.swiper = res.data.ad.map(v => ({
					icon: v.icon,
				}))
				this.tabs = [{
					name: '全部',
					id: ''
				}].concat(res.data.typeList)
				this.ggList = res.data.payList.map((v) => (`${v.userName}成功兑换了${v.goodsName}`))
			}
		},
	}
</script>

<style scoped lang="scss">
	.topimg {
		width: 350rpx;
		height: 350rpx;
	}

	.jfgb {
		padding: 12rpx 20rpx;
	}

	.bf2 {
		background: #FF5012;
	}

	.imgc {
		width: 320rpx;
		height: 320rpx;
		margin-top: 30rpx;
	}

	.goodsc {
		width: 325rpx;
	}

	.goodsimg {
		width: 325rpx;
		height: 325rpx;
	}

	.boxs {
		border-radius: 8rpx;
		padding: 0 7rpx;
	}
</style>
