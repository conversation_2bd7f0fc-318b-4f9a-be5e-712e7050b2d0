<template>
	<view class="page">
		<view class="page-navigation-bar">
			<view class="go-icon">
				<text class="iconfont iconback f40 " @click="go('back','/yb_o2ov2/my/index')"></text>
			</view>
			<text>{{$t('my.food_procurement')}}</text>
		</view>
		<view class="search-box">
			<view class="search">
				<text class="iconfont iconsearch f28 c9"></text>
				<input class="search-ipt" type="text" v-model="searchData.keyWord" @input="search"
					:placeholder="$t('home.input_placeholder')" />
				<view class="search-select" @click="showSelect = true">
					<text class="select-text">{{selectedOption.name}}</text>
					<text class="iconfont icontriangle f28 c9"></text>
				</view>
			</view>
		</view>
		<view class="store-list">
			<scroll-view style="height: 100%;" scroll-y @scrolltolower="onPullUpBottom">
				<store-item :item="item" v-for="item in list" :key="item.id"></store-item>
				<mescroll-empty v-if="list.length===0&&status!=='loading'"
					:option="{icon:'/static/empty/9.png',tip:`~ ${$t('common.no_data')} ~`}"></mescroll-empty>
				<u-loadmore v-if="!(list.length===0&&status!=='loading')" @loadmore="nextPage" :status="status" />
				<footc></footc>
			</scroll-view>
		</view>

		<!-- 选择器弹窗 -->
		<u-popup v-model="showSelect" mode="bottom" border-radius="20" :safe-area-inset-bottom="true">
			<view class="select-popup">
				<view class="select-header">
					<view class="select-cancel" @click="showSelect = false">{{ $t('common.showModal_cancelText') }}</view>
					<view class="select-title">{{ $t('good.select_category') }}</view>
					<view class="select-confirm" @click="confirmSelect">{{ $t('common.showModal_confirmText') }}</view>
				</view>
				<scroll-view class="select-content" scroll-y="true">
					<view
						v-for="(option, index) in selectOptions"
						:key="option.id"
						class="select-option"
						:class="{ 'selected': tempSelectedIndex === index }"
						@click="selectOption(index)"
					>
						<text class="option-text">{{option.name}}</text>
						<text v-if="tempSelectedIndex === index" class="iconfont icongou f32 selected-icon"></text>
					</view>
				</scroll-view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import storeItem from "./components/storeItem.vue";
	import footc from '@/components/common/footc.vue';
	export default {
		components: {
			storeItem,
			footc
		},
		data() {
			return {
				list: [],
				pageData: {
					page: 1,
					size: 15
				},
				searchData: {
					keyWord: '',
					typeId: '', // 添加分类筛选参数
				},
				flag: true,
				status: 'loadmore',
				searchTimer: null,
				showSelect: false, // 控制选择器显示
				tempSelectedIndex: 0, // 临时选中的索引
				selectedIndex: 0, // 当前选中的索引
				selectOptions: [
					{ name: this.$t('good.classContent'), id: 0 }
				]
			}
		},
		computed: {
			// 当前选中的选项
			selectedOption() {
				return this.selectOptions[this.selectedIndex] || this.selectOptions[0]
			}
		},
		watch: {
			// 监听弹窗显示状态，打开时重置临时选中索引
			showSelect(newVal) {
				if (newVal) {
					this.tempSelectedIndex = this.selectedIndex
				}
			}
		},
		onLoad() {
			// 初始化临时选中索引为当前选中索引
			this.tempSelectedIndex = this.selectedIndex
			this.getList()
			this.getSupplierType()
		},
		methods: {
			onPullUpBottom() {
				this.getList()
			},
			async getList() {
				if (!this.flag) return
				this.flag = false
				this.status = "loading"
				const {
					data
				} =await this.util.request({
					url: this.api.supplierList,
					method: 'GET',
					data: {
						...this.pageData,
						...this.searchData
					}
				})
				this.list = this.list.concat(data.list)
				if (data && data.list.length > this.list.length) {
					this.flag = true
					this.status = 'loadmore'
					return
				}
				this.status = 'nomore'
			},
			async getSupplierType() {
				const {
					data
				} =await this.util.request({
					url: this.api.suppliercategoryList,
					method: 'GET'
				}) 
				this.selectOptions.push(...data)
			},
			search() {
				if (this.searchTimer) {
					clearTimeout(this.searchTimer)
				}

				this.searchTimer = setTimeout(() => {
					this.pageData.page = 1
					this.list = []
					this.flag = true
					this.getList()
				}, 500)
			},
			// 选择选项
			selectOption(index) {
				this.tempSelectedIndex = index
			},
			// 确认选择
			confirmSelect() {
				this.selectedIndex = this.tempSelectedIndex
				this.searchData.typeId = this.selectOptions[this.selectedIndex].id
				this.showSelect = false
				// 重新加载数据
				this.pageData.page = 1
				this.list = []
				this.flag = true
				this.getList()
			},
			// 下一页
			nextPage() {
				if (this.status === 'loading' || this.status === 'nomore') {
					return
				}
				this.pageData.page++
				this.getList()
			}
		}
	}
</script>

<style lang="scss" scoped>
	.page {
		display: flex;
		width: 100vw;
		height: 100vh;
		flex-direction: column;

		.search-box {
			width: 100%;
			padding: 30rpx;
			box-sizing: border-box;
			height: 124rpx;

			.search {
				display: flex;
				width: 100%;
				height: 64rpx;
				justify-content: flex-start;
				align-items: center;
				background-color: #fff;
				border-radius: 40rpx;
				padding-left: 20rpx;
				box-sizing: border-box;

				.search-ipt {
					flex: 1;
					height: 100%;
					margin-left: 10rpx;
				}

				.search-select {
					min-width: 120rpx;
					max-width: 400rpx;
					height: 100%;
					display: flex;
					align-items: center;
					justify-content: space-between;
					padding: 0 15rpx;
					background-color: #f8f8f8;
					border-radius: 40rpx;
					// margin-right: 20rpx;

					.select-text {
						font-size: 32rpx;
						color: #e25757;
						font-weight: bold;
						max-width: 280rpx;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
					}
				}
			}
		}
		
		.store-list{
			flex: 1;
			overflow-y: auto;
			padding: 0 30rpx;
			box-sizing: border-box;
		}
	}

	/* 选择器弹窗样式 */
	.select-popup {
		background: #fff;
		border-radius: 20rpx 20rpx 0 0;

		.select-header {
			display: flex;
			align-items: center;
			justify-content: space-between;
			height: 100rpx;
			padding: 0 30rpx;
			border-bottom: 1px solid #f5f5f5;

			.select-cancel, .select-confirm {
				font-size: 28rpx;
				color: #666;
			}

			.select-confirm {
				color: #007aff;
			}

			.select-title {
				font-size: 32rpx;
				font-weight: 500;
				color: #333;
			}
		}

		.select-content {
			height: 600rpx;

			.select-option {
				display: flex;
				align-items: center;
				justify-content: space-between;
				height: 88rpx;
				padding: 0 30rpx;
				border-bottom: 1px solid #f8f8f8;

				&:last-child {
					border-bottom: none;
				}

				&.selected {
					background-color: #f0f9ff;

					.option-text {
						color: #007aff;
					}

					.selected-icon {
						color: #007aff;
					}
				}

				.option-text {
					font-size: 30rpx;
					color: #333;
				}
			}
		}
	}
</style>