<template>
	<u-popup v-model="couponsShow" mode="bottom" zIndex="12">
		<view :style="{height: (wHeight/2)+'px'}">
			<tab-nav @change="changeTabNav" :activeColor="tColor" height="80" :isScroll="false" bg-color="#F5F5F5" :current-index="current"
			 :list="list"
				></tab-nav>
				<swiper @change="changeSwiper" :current="current" :autoplay="false" :style="{height:(wHeight/2-80/pxToRpxRate)+'px'}">
					<!-- 平台红包 -->
					<swiper-item>
						<!-- <scroll-view :scroll-y="true" class="f-col wh" style="width: 100%;background: #F5F5F5;" >
							<view v-for="item in tempData.pt" :key="item" class="coupons mla mt20 f-row p-r">
								<text class="p-a cf f20" style="border-radius: 20rpx 0 20rpx 0;padding:4rpx 12rpx;left: 0;top: 0;background: rgba(255,88,55,1);"></text>
								<view class="gyhql f-y-e pb30 p-r">
									<view class="mr30">
										<view class="t-c" style="color: #F45439;"><text class="f24">$</text><text class="f50 wei">3</text></view>
										<view class="f20" style="color:color: #626262;">满20可用</view>
									</view>
									<view class="f28" style="margin-top: 32rpx;">
										<view class="wei"></view>
										<view class="f20"  style="color:color: #626262;">有效期至2021.08.12</view>
									</view>
									<view class="p-a gdot gdot1"></view>
									<view class="p-a gdot gdot2"></view>
								</view>
								<view class="gyhqr f-c" style="border-left: 1px dashed #f5f5f5;">
									<view></view>
									<view>兑换</view>
								</view>
							</view>
						</scroll-view> -->
					</swiper-item>
					<!-- 店家代金券 -->
					<swiper-item>
						<scroll-view :scroll-y="true" class="f-col wh" style="width: 100%;background: #F5F5F5;" >
							<!-- 收藏有礼 -->
							<view v-if="tempData.collection.money" class="coupons mla mt20 f-row p-r f-x-bt p2">
								<view class="f-y-c">
									<view class="bs10 mr20" style="width: 80rpx;height: 80rpx;"><image class="wh" :src="shopData.icon" mode=""></image></view>
									<view class="f-col">
										<view>
											<text class="wei" style="color:#F45439;">${{tempData.collection.money}}</text>
											<text class="wei">代金券</text>
											<text >(满{{tempData.collection.fullMoney}}元可用)</text>
										</view>
										<view class="f24 c9">领取后{{tempData.collection.day}}有效</view>
									</view>
								</view>
								<view v-if="collectionNews" class="f24 t-c" style="width: 100rpx;height:100rpx;">
									<image class="wh" src="/static/img_home/receive-after.png" mode=""></image>
								</view>
								<view v-else class="f24 t-c wei" style="width: 100rpx;color:#F45439;" @click="collection">收藏门店并领取</view>
							</view>
							<view v-for="(item,index) in tempData.storeCoupon" :key="item.id"
							v-if="!item.islq"
							 class="coupons mla mt20 f-row p-r f-x-bt p2">
								<view class="f-y-c">
									<view class="bs10 mr20" style="width: 80rpx;height: 80rpx;"><image class="wh" :src="shopData.icon" mode=""></image></view>
									<view class="f-col">
										<view>
											<text class="wei" style="color:#F45439;">${{item.money}}</text>
											<text class="wei">代金券</text>
											<text v-if="item.isFull === '2'">(满{{item.fullMoney}}元可用)</text>
											<text v-else-if="item.isFull === '1'">(无门槛)</text>
										</view>
										<view class="f24 c6">{{item.useExplain}}</view>
									</view>
								</view>
								<view v-if="item.tempFlag" class="f24 t-c" style="width: 100rpx;height:100rpx;">
									<image class="wh" src="/static/img_home/receive-after.png" mode=""></image>
								</view>
								<view v-else class="f24 t-c wei" style="width: 100rpx;color:#F45439;" @click="receive(item.id,index)">立即领取</view>
							</view>
						</scroll-view>
					</swiper-item>
				</swiper>
		</view>
	</u-popup>
</template>

<script>
	import TabNav from '@/components/TabNav.vue'
	export default {
		components: {
			TabNav
		},
		props:{
			value:{
				type: Boolean,
				default:false
			},
			data:{
				type: Object,
				default:()=>{}
			},
			shopData:{
				type: Object,
				default:()=>{}
			}
		},
		data(){
			return {
				list:[],
				tempData:{
					collection:{isReceive:false}
				},
				couponsShow:false,
				current:0,
				news:false,
			}
		},
		computed:{
			collectionNews: {
				 get() {
				   return this.news
				 },
				 set(newValue) {
				   this.news = newValue
				 }
			}
		},
		watch:{
			data(val){
				if(this.list.length>0)return
				this.tempData = val
				try{
					if(val.pt.length>0){
						this.list.push({name:'红包'})
					}
				}catch(e){
					this.current =  1
					console.log('暂无平台红包')
					//TODO handle the exception
				}
				if(val.storeCoupon.length>0){
					this.list.push({name:'本店代金券'})
				}
			},
			value(val){
				this.couponsShow = val
			},
			couponsShow(val){
				this.$emit('input', val)
			}
		},
		methods:{
			changeSwiper(event){
				this.current=event.detail.current
			},
			changeTabNav(index){
				this.current = index
			},
			async collection(){
				await this.$parent.collectionStore()
				// this.tempData.collection.isReceive = true
				this.collectionNews = true
			},
			async receive(couponId,index){
				let {data} =  await this.util.request({
					url: this.api.lqyhq,
					method: 'GET',
					data: {
						couponId: couponId
					}
				})
				this.$set(this.tempData.storeCoupon,index,Object.assign({},this.tempData.storeCoupon[index],{tempFlag:true}))
				this.$parent.getCoupons()
			}
		}
	}
</script>

<style scoped lang="scss">
	.coupons{
		width: 700rpx;
		height: 170rpx;
		background: #FFFFFF;
		border-radius: 30rpx;
		border: 1px solid #FFFFFF;
		.gyhql{
			width: 520rpx;
			margin-left: 50rpx;
		}
		.gyhqr{
			width: 180rpx;
		}
		.gdot {
			width: 20rpx;
			height: 20rpx;
			border-radius: 50%;
			background: #f5f5f5;
			right: -10rpx;
		}
		.gdot1 {
			top: -12rpx;
		}
		
		.gdot2 {
			bottom: -12rpx;
		}
	}
</style>
