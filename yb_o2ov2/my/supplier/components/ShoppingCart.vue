<template>
	<view>
		<!-- 弹窗部分 -->
		<u-popup v-model="show" mode="bottom" zIndex="1" border-radius="20">
			<view class="p-r" :style="{paddingBottom:`${height+60}rpx`,maxHeight:`${wHeight/2}px`,background:'#fff'}">
				<!-- title -->
				<view style="position: sticky;top: 0;width: 100%;z-index: 1;" :style="{background:'#fff'}">
					<!--未参与拼单-->
					<template v-if="!together">
						<view v-if="alomostMoney>0" class="t-c f24"
							style="height: 60rpx;line-height: 60rpx;background:#F3EDD5;">
							<text>还差</text>
							<text style="color:#ff5454">{{alomostMoney}}元</text>
							<text>就能起送</text>
							<text style="color:#ff5454">[去凑单]</text>
						</view>
						<view v-else class="t-c f24" style="height: 60rpx;line-height: 60rpx;background:#F3EDD5;">
							<discount :reduceArr="reduceArr" :discount="discount"></discount>
						</view>
						<view class="p2 f-x-bt f24 c9" style="border-bottom: 1px solid #fafafa;">
							<view>
								<text class="c6">{{$t('good.product_details')}}</text>
								（<text class="c3">{{$t('good.packing_charge')}}</text>
								<text style="color: #FE624B;">${{cartInfo.boxMoney || 0}}</text>
								）
							</view>
							<view class="f-y-c" @click="clear">
								<text class="iconfont icondelete f26 c6" style="margin-right: 5rpx;"></text>
								<text class="c6">{{$t('good.empty_cart')}}</text>
							</view>
						</view>
					</template>
					<!--拼单-->
					<template v-else>
						<view class="p2 f-x-bt f24 c9" style="border-bottom: 1px solid #fafafa;">
							<view>
								<text class="c6">已选商品</text>
								(<text class="c3">我点</text><text style="color: #FE624B;">${{selfMoney || 0}}</text>)
								（<text class="c3">打包费</text>
								<text style="color: #FE624B;">${{cartInfo.boxMoney || 0}}</text>
								）
							</view>
							<view class="f-y-c">
								<u-button type="info" size="mini" :plain="true" @click="handleCancle()">退出拼单</u-button>
							</view>
						</view>
					</template>
				</view>
				<!-- content -->
				<scroll-view scroll-y class="f-col p02" :style="{maxHeight:`${scrollHeight}px`}">
					<!-- 拼单对象 -->
					<view v-if="together>0" class="f-x-bt mt20">
						<view class="flex">
							<view class="mr20 bs60" style="width: 45rpx;height: 45rpx;">
								<image class="wh" :src="user.portrait" mode=""></image>
							</view>
							<view class="mr20">
								<text class="c6 f24">{{user.userName}}(我)</text>
							</view>
						</view>

						<view class="f-y-c">
							<u-button type="info" size="mini" :plain="true" @click="clear">
								<text class="iconfont icondelete f26 c6" style="margin-right: 5rpx;"></text>
								<text class="c6">清空</text>
							</u-button>
						</view>
					</view>

					<!-- 商品 -->
					<view v-for="item in selfList" :key="item.id" class="flex p20"
						style="border-bottom: 1px solid #fafafa;">

						<view style="width: 600rpx;" class="flex">
							<view class="mr20 bs10" style="width: 120rpx;height: 120rpx;">
								<image class="wh" :src="item.icon" mode=""></image>
							</view>
							<view>
								<view class="f28 wei">{{item.name}}</view>
								<view class="f20 c6" style="line-height: 20rpx;">
									<text v-if="item.groupName">{{item.groupName}}</text>
									<text v-if="item.groupName&&(item.attribute||item.materialName)">+</text>
									<text v-if="item.attribute">{{item.attribute}}</text>
									<text v-if="(item.groupName||item.attribute)&&item.materialName">+</text>
									<text v-if="item.materialName">{{item.materialName}}</text>
								</view>
								<view v-if="item.discountNum>0" :style="{color:tColor}" class="f-row mt10">
									<view class="f20 bs5 b-d p-3-10" :style="{borderColor:tColor}">
										{{item.discountType==1?'特价':item.discountType==2?'折扣':item.discountType==3?'立减':item.discountType==4?'第二件打折':'买一送一'}}
									</view>
								</view>
								<view class="f28 mt15 wei" style="color: #FE624B;"><text
										class="f20">$</text>{{item.totalMoney || 0}}
									<text v-if="item.discountNum>0"
										class="t-d-l ml10 c9 f26">{{sl+blxs(item.num*(item.money || 0))}}</text>
								</view>
							</view>
						</view>
						<view class="f-x-bt f-g-1">
							<view class="f-e-bt" style="width: 120rpx;">
								<view class="f-y-e pb10" style="height: 60rpx;" @click="removeCart(item)">
									<view class="bs10 f-c" style="height: 36rpx;width: 36rpx;text-align: center;"
										:style="{border: `1px solid ${shopGoodsInfo.categorySet.delColor}`}">
										<!-- 减号 -->
										<text class="iconfont iconminus f20 bs10 wei" style="padding: 4rpx 0;"
											:style="{color:shopGoodsInfo.categorySet.addColor2}"></text>
									</view>
								</view>
								<view style="padding-bottom: 5rpx;">{{item.num}}</view>
								<view class="f-y-e pb10" style="height: 60rpx;" @click="addCart(item)">
									<view class="bs10 f-c" style="height: 36rpx;width: 36rpx;text-align: center;"
										:style="{background: shopGoodsInfo.categorySet.delColor}">
										<!-- 加号 -->
										<text class="iconfont iconplus f20 bs10 wei" style="padding: 4rpx 0;"
											:style="{color: shopGoodsInfo.categorySet.addColor}"></text>
									</view>
								</view>
							</view>
						</view>
					</view>
					<template v-if="together">
						<!-- 拼单别人 -->
						<view v-for="tuser in getTogetherUser" :key="tuser.id">
							<view class="f-x-bt mt20">
								<view class="flex">
									<view class="mr20 bs60" style="width: 45rpx;height: 45rpx;">
										<image class="wh" :src="tuser.portrait" mode=""></image>
									</view>
									<view class="mr20">
										<text class="c6 f24">{{tuser.userName}}</text>
									</view>
									<view class="f24">
										<u-button type="error" shape="circle" size="mini" :plain="true">
											<view v-if="tuser.status==2">
												<u-icon name="checkbox-mark" color="#f56c6c" size="22"></u-icon>
												<text class="c6 ml10">已点完</text>
											</view>
											<view v-else>
												<u-icon name="hourglass-half-fill" color="#f56c6c" size="22"></u-icon>
												<text class="c6 ml10">正在点</text>
											</view>

										</u-button>
									</view>
								</view>
								<view class="f-y-c" v-if="tuser.id==commander.id">
									<u-tag text="团长" mode="plain" type="info" />
								</view>
							</view>
							<!--商品-->
							<view v-for="item in getCarList(tuser.id)" :key="item.id" class="flex p20"
								style="border-bottom: 1px solid #fafafa;">
								<view style="width: 600rpx;" class="flex">
									<view class="mr20 bs10" style="width: 120rpx;height: 120rpx;">
										<image class="wh" :src="item.icon" mode=""></image>
									</view>
									<view>
										<view class="f28 wei">{{item.name}}</view>
										<view class="f20 c6" style="line-height: 20rpx;">
											<text v-if="item.groupName">{{item.groupName}}</text>
											<text v-if="item.groupName&&(item.attribute||item.materialName)">+</text>
											<text v-if="item.attribute">{{item.attribute}}</text>
											<text v-if="(item.groupName||item.attribute)&&item.materialName">+</text>
											<text v-if="item.materialName">{{item.materialName}}</text>
										</view>
										<view v-if="item.discountNum>0" :style="{color:tColor}" class="f-row mt10">
											<view class="f20 bs5 b-d p-3-10" :style="{borderColor:tColor}">
												{{item.discountType==1?'特价':item.discountType==2?'折扣':item.discountType==3?'立减':item.discountType==4?'第二件打折':'买一送一'}}
											</view>
										</view>
										<view class="f28 mt15 wei" style="color: #FE624B;"><text
												class="f20">$</text>{{item.totalMoney || 0}}
											<text v-if="item.discountNum>0"
												class="t-d-l ml10 c9 f26">{{sl+blxs(item.num*(item.money || 0))}}</text>
										</view>
									</view>
								</view>
								<view class="f-x-bt f-g-1">
									<view style="padding-top: 50rpx;">{{item.num}}</view>
								</view>
							</view>
						</view>

					</template>
				</scroll-view>
			</view>
		</u-popup>
		<!-- 悬浮窗部分 -->
		<view class="cartBoxf24 p-r" :style="{height:height+'rpx',background:'#f5f5f5'}">
			<!-- 满减悬浮 -->
			<view v-if="!show && reduceArr" class="p-a p-a-xc bs20"
				style="width: 700rpx;height: 120rpx;background: rgba(249,247,226,.9);top:-60rpx;">
				<discount :reduceArr="reduceArr" :discount="discount"></discount>
			</view>

			<view class="f-s-ac mla c9 p-r bg0"
				style="margin: 0 auto;width: 700rpx;height: 110rpx;border-radius: 100rpx;" @click="show=true">
				<view class="p=r" style="margin-top: -40rpx;margin-left: 30rpx;width: 100rpx;height: 100rpx;"
					id="aniEnd">
					<image class="wh" v-if="cartInfo.data"
						:src="cartInfo.data.length===0?shopGoodsInfo.categorySet.noGoodIcon:shopGoodsInfo.categorySet.goodIcon"
						mode="aspectFit"></image>
				</view>
				<view class="countPoint" v-if="cartInfo.data && cartInfo.data.length !== 0">{{goodsTotalNum}}</view>
				<block v-if="cartInfo.data && cartInfo.data.length===0">
					<view class="ml20 f-g-1 f-y-c">
						<text class="cf f30 mt10">$</text>
						<text class="cf f36 wei">0</text>
						<text class="m01">|</text>
						<!-- <text
							class="f24">{{$t('good.estimated_delivery_fee')}}${{(shopGoodsInfo.shopData && shopGoodsInfo.shopData.distribution && shopGoodsInfo.shopData.distribution.money) || 0}}</text> -->
					</view>
					<view class="mr20">
						<!-- <text>${{(shopGoodsInfo.shopData && shopGoodsInfo.shopData.distribution && shopGoodsInfo.shopData.distribution.startMoney) || 0}}{{$t('good.start_sending')}}</text> -->
					</view>
				</block>
				<block v-else>
					<view class="ml20 f-g-1 f-col" style="line-height: 30rpx;">
						<view>
							<!-- 满减后 价格 -->
							<text class="cf f30 mt10">$</text>
							<text class="cf f36 wei">{{cartInfo.price || 0}}</text>
							<!-- 划线价 （原价） -->
							<text class="f28 c9 t-d-l"
								v-if="cartInfo.price!==cartInfo.oldPrice">${{cartInfo.oldPrice || 0}}</text>
						</view>
						<!-- <view>
							<text
								class="cf f24">{{$t('good.estimated_delivery_fee')}}${{(shopGoodsInfo.shopData && shopGoodsInfo.shopData.distribution && shopGoodsInfo.shopData.distribution.money) || 0}}</text>
						</view> -->
					</view>
					<view v-if="alomostMoney>0" class="c9 mr20">
						<!-- <text>{{$t('good.differ_from')}}{{alomostMoney}}{{$t('good.start_sending')}}</text> -->
					</view>
					<!-- 单点不送 -->
					<view v-else-if="validate&&validate.show" class="mr20">
						<view @click.stop="handleGoAdd(validate.onClick)">{{validate.text||''}}</view>
					</view>
					<view v-else class="f-c h100 c0 wei f30"
						style="border-radius: 0 100rpx 100rpx 0;width: 180rpx;margin-right: -2rpx;"
						:style="{background:tColor,color:fontColor}" @click.stop="goToPay">
						<text>{{ selfUser.type==1?'选好了':$t('good.go_settle') }}</text>
					</view>
				</block>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapMutations
	} from "vuex";
	import utils from '@/common/utils.js'
	import Discount from './Discount.vue'
	import {
		utilMixins
	} from "@/common/util-mixins.js"
	export default {
		components: {
			Discount
		},
		props: {
			storeId: {
				type: [String, Number],
				default: ''
			},
			cartInfo: {
				type: Object,
				default: () => {
					return {
						shopData: {
							distribution: { money: 0, startMoney: 0 }
						},
						categorySet: {},
						moreSet: {},
					}
				}
			},
			background: {
				height: {
					type: String,
					default: "#f5f5f5"
				}
			},
			height: {
				type: [Number, String],
				default: 140
			},
			together: {
				type: Number,
				default: 0
			},
			togetherUser: {
				type: Array,
				default: []
			},
			selfMoney: {
				type: Number,
				default: 0
			},
		},
		data() {
			return {
				show: false,
				isPdFlag: false
			}
		},
		mixins: [utilMixins],
		computed: {
			...mapState(['shopGoodsInfo', 'canOrder', 'user']),
			selfList() { //自己的商品
				return Array.isArray(this.cartInfo.data) ? this.cartInfo.data.filter((item) => item.userId == this.user
					.id) : []
			},
			otherList() {
				return Array.isArray(this.cartInfo.data) ? this.cartInfo.data.filter((item) => item.userId != this.user
					.id) : []
			},
			getTogetherUser() {
				return Array.isArray(this.togetherUser) ? this.togetherUser.filter((item) => item.id != this.user.id) : []
			},
			selfUser() {
				return Array.isArray(this.togetherUser) && this.togetherUser.length ? this.togetherUser.filter((item) =>
					item.id == this.user.id)[0] : {
					type: 0
				}
			},
			commander() { //团长
				return Array.isArray(this.togetherUser) ? this.togetherUser.filter((item) => item.type == 0)[0] : {
					id: 0
				}
			},
			isCommander() {
				try {
					return this.commander.id == this.selfUser.id
				} catch {}
				return false

			},
			alomostMoney() {
				try {
					return (this.shopGoodsInfo.shopData.distribution.startMoney - this.cartInfo.price).toFixed(2)
				} catch (e) {
					//TODO handle the exception
					return 0
				}
			},
			scrollHeight() {
				return this.wHeight / 2 - (this.height + 60) / this.pxToRpxRate - 57
			},
			reduceArr() {
				if (this.shopGoodsInfo.discount.reduce instanceof Array) return false
				return this.shopGoodsInfo.discount.reduce
			},
			goodsTotalNum() {
				var count = 0
				if (!this.cartInfo) return
				if (this.cartInfo.data) {
					for (let i of this.cartInfo.data) {
						count += +i.num
					}
				}
				return count

			},
			discount() {
				if (!this.reduceArr) return
				if (this.reduceArr.data.type === '1') {
					// 循环满减
					return {
						reduceMoney: this.cartInfo.reduce,
						money: utils.countMoney(this.reduceArr.data.fullMoney - (this.cartInfo.oldPrice % this.reduceArr
							.data.fullMoney)),
						againReduce: this.reduceArr.data.money
					}
				}
				if (this.reduceArr.data.type === '2') {
					//判断递增满减阶级
					let moneyArr = this.reduceArr.data.moneyArr
					let index = moneyArr.findIndex(i => +i.fullMoney > +this.cartInfo.oldPrice)
					let info = moneyArr[index]
					if (index === -1) { // 最大阶级
						return {
							reduceMoney: this.cartInfo.reduce,
							reduceFullMoney: true
						}
					}
					return {
						reduceMoney: this.cartInfo.reduce, //已减
						money: utils.countMoney(info.fullMoney - this.cartInfo.oldPrice), //再满
						againReduce: info.money - this.cartInfo.reduce // 可再减
					}
				}
			},
			validate() {
				if (!this.cartInfo.data) return false
				// 单点不送
				if (this.canOrder.singleIds.length > 0) {
					let single = true
					this.cartInfo.data.forEach(item => {
						if (!this.canOrder.singleIds.some(i => item.goodsId === i)) {
							//some结果为false说明找到了【单点不送】以外的商品
							single = false
						}
					})
					if (single) {
						return {
							show: single,
							text: '单点不送'
						}
					}

				}
				// 必点
				if (this.canOrder.mustIds.length > 0) {
					let must = true
					this.cartInfo.data.forEach(item => {
						if (this.canOrder.mustIds.some(i => item.goodsId === i)) {
							//some结果为false说明找到了【单点不送】以外的商品
							must = false
						}
					})
					if (must) {
						return {
							show: must,
							text: '未点必选品',
							onClick: 'mustCategory'
						}
					}
				}

				return false
			},
		},
		mounted() {
			let query = uni.createSelectorQuery().in(this)
			// 确认动画结束位置
			query.select('#aniEnd').boundingClientRect(res => {
				if (!res) return
				this.$emit('getAniEndDot', res.left + (res.width / 2), res.top + res.height)
			}).exec()
		},
		methods: {
			...mapMutations(['setCartInfo']),
			getCarList(id) {
				return this.otherList.filter((item) => item.userId == id)
			},
			handleGoAdd(fnName) {
				if (fnName === 'mustCategory') {
					if (this.$parent.$parent) {
						this.$parent.selectCategory(this.$parent.$parent.mustCategoryIndex)
					}
				}
			},
			otherGoTopay() {
				this.jjmbxx = true
				this.goToPay()
			},
			async goToPay() {
				// 有拼单-确认
				const that = this
				if (that.together) {
					if (this.isCommander && this.selfUser.status == 1) {
						uni.showModal({
							title: '提示',
							content: '你是团长，提交后别人将不能修改商品和加入拼单',
							confirmText: '确认',
							confirmColor: '#FFCD17',
							success: function(res) {
								if (res.confirm) {
									that.modifyTogetherStatus()
									that.goToPayFunc()
								} else if (res.cancel) {
									console.log('用户点击取消');
								}
							}
						})
					} else {
						this.jjmbxx = true
					}
					that.modifyTogetherStatus()
					that.goToPayFunc()
				} else {
					that.goToPayFunc()
				}
			},
			async goToPayFunc() {
				// 无拼单
				if (this.isLogin) {
					if (!this.jjmbxx) {
						try {
							await this.requestSM('orderNotice')
						} catch (e) {
							this.jjmbxx = true
							this.loading = false
							return
						}
					}
					uni.setStorageSync('storeAddress', {
						lat: this.shopGoodsInfo.shopData.lat,
						lng: this.shopGoodsInfo.shopData.lng,
						name: this.shopGoodsInfo.shopData.name,
						address: this.shopGoodsInfo.shopData.address,
						icon: this.shopGoodsInfo.shopData.icon
					})
					let discount = {
						reduceMoney: this.cartInfo.reduce //活动满减

					}
					this.go('navigateTo',
						`/yb_o2ov2/my/supplier/submit-order?storeId=${this.storeId}&discount=${JSON.stringify(discount)}&togetherId=${this.together}`
					)
				} else {
						const pages = getCurrentPages();
						const currentPage = pages[pages.length - 1];
						const route = currentPage.route;
						const fullPath = route + '?' + Object.entries(currentPage.options || {}).map(([key, val]) => `${key}=${val}`).join('&');
						uni.setStorageSync('login_redirect', fullPath);
						uni.navigateTo({
							url: `/yb_o2ov2/my/login`
						})
				}
			},
			async clear() {
				await this.util.modal('確認清除購物車嗎', '清除購物車').then(res => {
					this.util.request({
						url: this.api.delsuppliercar,
						method: 'POST',
						data: {
							item: '1',
							storeId: this.storeId,
						}
					}).then(res => {
						if (res && res.code == 1) {
							this.util.message('刪除成功', 1, 1000)
							this.$emit('refresh')
						}
					})
				})
				this.show = false
			},
			addCart(good) {
				console.log('ssss')
				this.$emit('addCart', undefined, good.goodsId, undefined, undefined, good.id)
			},
			removeCart(good) {
				console.log('ssss1111')
				this.$emit('removeCart', good.goodsId, undefined, undefined, good.id)
			},
			handleCancle() {
				console.log(this.user)
				this.$parent.handleCancle()
				this.show = false
			},
			async modifyTogetherStatus() {
				await this.util.request({
					url: this.api.modifyTogetherStatus,
					method: 'POST',
					data: {
						storeId: this.storeId,
						together: this.together,
						status: 2
					}
				})
			},
			showDetails() {
				this.show = true
			}
		}
	}
</script>

<style scoped lang="scss">
	.cartBox {
		position: relative;
		width: 100%;
		top: 0;
		z-index: 2;
	}

	.countPoint {
		position: absolute;
		width: 40rpx;
		height: 40rpx;
		top: 0;
		left: 110rpx;
		text-align: center;
		color: #ffffff;
		border-radius: 50%;
		background: red;
	}
</style>