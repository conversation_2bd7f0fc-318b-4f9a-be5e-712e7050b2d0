<template>
	<view class="store-item" @click="go('navigateTo', `/yb_o2ov2/my/supplier/store?storeId=${item.id}`)">
		<image class="store-img" :src="item.icon"></image>
		<view class="info">
			<view class="name">{{item.name}}</view>
			<view class="good-list">
				<view class="good-item" v-for="good in item.recommendGoods" :key="good.id">
					<image class="good-img" :src="good.icon"></image>
					<view class="good-name">{{good.name}}</view>
					<view class="good-price">${{good.price}}</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			item: {
				type: Object,
				default: () => {}
			}
		},
		methods: {

		}
	}
</script>

<style lang="scss" scoped>
	.store-item {
		display: flex;
		width: 100%;
		justify-content: flex-start;
		align-items: flex-start;
		background-color: #fff;
		border-radius: 30rpx;
		margin-bottom: 20rpx;
		padding: 20rpx;
		box-sizing: border-box;

		.store-img {
			width: 120rpx;
			height: 120rpx;
		}

		.info {
			flex: 1;
			margin-left: 20rpx;

			.name {
				font-size: 30rpx;
			}

			.good-list {
				display: flex;
				justify-content: flex-start;
				align-items: center;
				margin-top: 20rpx;

				.good-item {
					display: flex;
					justify-content: center;
					align-items: center;
					flex-direction: column;
					margin-left: 20rpx;
					flex-shrink: 0;

					&:first-child {
						margin-left: 0;
					}

					.good-img {
						width: 100rpx;
						height: 100rpx;
					}
					
					.good-name{
						width: 140rpx;
						margin-top: 20rpx;
						font-size: 26rpx;
						overflow: hidden;
						white-space: nowrap;
						text-overflow: ellipsis;
					}

					.good-price {
						font-size: 26rpx;
						color: red;
					}
				}
			}
		}
	}
</style>