<template>
	<view style="padding-bottom: 220rpx;">
		<!-- 返回 -->
		<view class="page-navigation-bar">
			<view class="go-icon">
				<text class="iconfont iconback f40 "
					@click="handleBack"></text>
			</view>
			<text>{{$t('good.confirm_order')}}</text>
		</view>

		<view class="p02">
			<block v-if="showSubmit">
				<!-- 头部渐变背景 -->
				<view class="p-a w100" style="height: 300rpx;left: 0;z-index: -1;"
					:style="{background:` linear-gradient(180deg, ${tColor} 100rpx, #f5f5f5)`}"></view>
				<view class="bf mla mt30 pb30" style="border-radius:20rpx;">
					<!-- deliveryMode -->
					<view class="p-r bf flex type"
						v-if="shopGoodsInfo.shopData.takeOutSet.distributionSupport.length>1">
						<block>
							<view class="t-c wei t-o-e w100 leftItem active">
								{{$t('good.delivery') }}
							</view>
						</block>
					</view>
					<!-- 外卖地址 -->
					<block v-if="form.deliveryMode===2">
						<view class="p02 mt30 pt10">
							<block v-if="receivingAddress.details">
								<view class="f-s-ac" @click="showAddress=true">
									<text class="f20 mr10 f-s-0"
										style="padding: 0rpx 8rpx;color: #5cb3e6;background: #ecf7fd;border-radius: 5rpx;">{{receivingAddress.label}}</text>
									<text class="wei f36 f-g-1 t-o-e">{{ receivingAddress.address }}{{receivingAddress.details}}</text>
									<text class="iconfont iconinto f24"></text>
								</view>
								<view class="c9 f28 mt10">
									<text class="mr20">{{receivingAddress.userName}} {{receivingAddress.sex=='1'?$t('address.mr'):$t('address.ms')}}</text>
									<text>{{receivingAddress.userTel}}</text>
								</view>
							</block>
							<block v-else-if='hideAddress.length!==0 && !receivingAddress.details'>
								<view class="f-s-ac" @click="showAddress=true">
									<text class="wei f38 f-g-1 t-o-e"
										:style="{color:tColor}">{{$t('address.select_placeholder')}}</text>
									<text class="iconfont iconinto f24"></text>
								</view>
							</block>
							<block v-else>
								<view class="f-s-ac" @click="go('navigateTo', '/yb_o2ov2/my/address/edit')">
									<text class="wei f38 f-g-1 t-o-e"
										:style="{color:tColor}">{{$t('address.add_address')}}</text>
									<text class="iconfont iconinto f24"></text>
								</view>
							</block>
							<!-- <view class="f28 mt30">
								<radio-group @change="radioChange">
									<label class="uni-list-cell uni-list-cell-pd" v-for="(item, index) in deliveryArray"
										:key="item.value">
										<span>
											<radio activeBorderColor="#d1d1d1" activeBackgroundColor="#fff"
												iconColor="#FFCC00" :value="item.value" :checked="index === 0" />
										</span>
										<text class="mr30 ml10">{{item.name}}</text>
									</label>
								</radio-group>
								<view class="mt10" v-if="form.appointment==1" @click="showTimeM(1)">
									<text class="wei" style="height: 46rpx;line-height: 46rpx;">{{statrTime}}
										<text v-show="statrTime">-</text>
										{{timeText}}</text>
									<text v-if="zitiOpen" class="iconfont iconinto f20 c9"></text>
								</view>

								<text class="wei f-g-1">{{deliveryText}}</text>
								<text class="mr10 f-s-0" :style="{color:fontColor}">{{timeText}}</text>
								<text class="iconfont iconinto f24" v-if="yuyueOpen" :style="{color:fontColor}"></text>
							</view> -->
						</view>
					</block>
					<!-- 自取地址 -->
					<block v-if="form.deliveryMode===1">
						<view class="mt30 flex pl20 o-h " style="display: flex;">
							<view class="f-col mr10 f-s-0"
								style="flex-basis: 400rpx;font-size: 22rpx;margin-right:8rpx">
								<view class="f32 wei p10"><text>{{storeAddressinfo.address}}</text></view>
								<view class="flex mt30">
									<view class="f-c-s f-y-c " style="border-right: 1px solid #f5f6f9;width:288rpx;"
										@click="showTimeM(2)">
										<text class="c9">{{$t('good.self_pickup_or_dining')}}</text>
										<view class="dis-in">
											<text class="wei" style="height: 46rpx;line-height: 46rpx;">{{statrTime}}
												<text v-show="statrTime">-</text>
												{{timeText}}</text>
											<text v-if="zitiOpen" class="iconfont iconinto f20 c9"></text>
										</view>
									</view>
									<view class="f-c-s" style="padding-left:14rpx;">
										<text class="c9">{{$t('good.reserved_phone_number')}}</text>
										<view class="f-c c3" style="width: 194rpx;">
											<input v-model="form.userTel" type="number" class="wei" />
											<text class="iconfont iconedit f28"></text>
										</view>
									</view>
								</view>
							</view>
							<view class="p-r bs10 t-c" style="width: 300rpx;height: 160rpx;" @click="openNavigation">
								<text class="bf f20 wei p-r distance-text" style="top: 10rpx;padding: 4rpx 12rpx"
									v-if="shopGoodsInfo.shopData.distance">距您{{shopGoodsInfo.shopData.distance}}</text>
								<image class="wh" :src="`${onImgurl}img/zqdt.png`" mode=""></image>
								<view class="p-a p-a-c" style="width: 50rpx;height: 50rpx;">
									<image class="wh" :src="storeAddressinfo.icon" mode=""></image>
								</view>
							</view>

						</view>
						<view v-if="ztTypeArr.length">
							<view class="f-x-bt p23">
								<view @click="clickztMode(v)" v-for="(v,i) in ztTypeArr" :key="String(i)"
									class="pstypec bs10 f-c"
									:style="{color:v.value==form.isOut?tColor:'',borderColor:v.value==form.isOut?tColor:''}">
									<text class="iconfont c3 mr20" :class="[v.icon]"
										:style="{color:v.value==form.isOut?tColor:''}"></text>
									<text class="f32">{{v.name}}</text>
									<view class="yddsj posi-a"
										:style="{borderBottom:v.value==form.isOut?'41rpx solid '+tColor:''}"></view>
									<text v-if="v.value==form.isOut"
										class="gou posi-a iconfont iconselect cf f20"></text>
								</view>
							</view>
						</view>
						<view class="mt20 pl20">
							<view class="f-y-c">
								<text :class="agreeTo?'iconselect':'iconnoselect'" class="iconfont f28 mr10"
									:style="{color:tColor}" @click="agreeTo = !agreeTo"></text>
								<text>{{$t('good.agree_and_accept')}}</text>
								<text @click="checkAgreement" style="color:#2D95FF;">《{{$t('good.agreement')}}》</text>
							</view>
						</view>
					</block>
				</view>
			</block>
			<block v-if="!showSubmit">
				<view class="bf mla mt30 pb30" style="width: 700rpx;border-radius:20rpx;">
					<view class="mt20" style="text-align: center;font-size: 16px;font-weight: 700;margin-bottom: 20px;">
						已完成选购，等待发起人提交订单
					</view>
					<u-steps :list="numList" :current="steps" mode="number"></u-steps>
				</view>
			</block>

			<!-- 商品list -->
			<view class="p2 bs20 f24 bf mt10">
				<view class="f-x-bt f28">
					<text>{{shopGoodsInfo.shopData.name}}</text>
					<text v-if="form.deliveryMode===1" class="f20 bs10 wei"
						style="padding: 0 4rpx;border: 1px solid #18B566;color:#18B566 ;">支持自取</text>
					<text v-else class="f20 bs10 wei c0" :style="{background:tColor,color:fontColor}"
						style="padding: 0 4rpx;">{{shopGoodsInfo.shopData.deliveryMode}}</text>
				</view>
				<view class="p20 " style="border-top: 1px solid #fafafa;border-bottom: 1px solid #fafafa;">
					<view v-if="togetherId>0">
						<view class="f-x-bt mt20 outer3">
							<view class="flex">
								<view class="mr20 bs60" style="width: 45rpx;height: 45rpx;">
									<image class="wh" :src="user.portrait" mode=""></image>
								</view>
								<view class="mr20">
									<text class="c6 f24">{{user.userName}}(我)</text>
								</view>
							</view>

							<view class="f-y-c" v-if="steps==1">
								<u-button type="info" size="mini" :plain="true" :custom-style="customStyle"
									@click="handleCancle">
									<text class="c6">退出拼单</text>
								</u-button>
								<u-button type="warning" size="mini" :plain="true" @click="editGoods">
									<text class="c6">修改商品</text>
								</u-button>
							</view>
						</view>
						<view v-for="(item, index) in selfList" :key="String(item.id || index)" class="f-x-bt f20 mt20">
							<view class="mr10 bs10 f-s-0" style="width: 90rpx;height: 90rpx;">
								<image class="wh" :src="item.icon" mode="aspectFill"></image>
							</view>
							<view class="f-g-1" style="line-height: 30rpx;">
								<view class="f28 flex">
									<view v-if="item.discountType>0 && item.discountType!=4"
										class="f-g-0 yhic f20 cf bf5f f-c hdspan" style="background:  #ff474a">
										{{item.discountType==1?'特':item.discountType==2?'折':item.discountType==5?'赠':'减'}}
									</view>
									{{item.name}}
								</view>
								<view v-if="item.discountType==4" class="f-row cf5f mt10">
									<view class="f20 bs5 b-d p-3-10" :style="{borderColor:'#FF5F2F'}">
										{{item.discountType==4?'第二件打折':'买一送一'}}
									</view>
								</view>
								<view class="c9" style="margin-top: 5rpx;min-height: 24rpx;">
									<text v-if="item.groupName">{{item.groupName}}</text>
									<text v-if="item.groupName&&(item.attribute||item.materialName)">,</text>
									<text v-if="item.attribute">{{item.attribute}}</text>
									<text v-if="item.attribute&&item.materialName">,</text>
									<text v-if="item.materialName">{{item.materialName}}</text>
								</view>
								<view class="c9"><text>x {{item.num}}</text></view>
							</view>
							<view class="f32 f-s-0">
								<text v-if="item.totalOriginalMoney && item.totalOriginalMoney!=item.totalMoney"
									class="c9 f24 t-d-l">${{item.totalOriginalMoney}}</text>
								<text class="f24">$</text>{{item.totalMoney}}
							</view>
						</view>

						<view v-for="(tuser, uindex) in getTogetherUser" :key="String(tuser.id || uindex)">
							<view class="f-x-bt mt20 outer3">
								<view class="flex">
									<view class="mr20 bs60" style="width: 45rpx;height: 45rpx;">
										<image class="wh" :src="tuser.portrait" mode=""></image>
									</view>
									<view class="mr20">
										<text class="c6 f24">{{tuser.userName}}</text>
									</view>
								</view>
							</view>
							<view v-for="(item, index) in getCarList(tuser.id)" :key="String(item.id || index)" class="f-x-bt f20 mt20">
								<view class="mr10 bs10 f-s-0" style="width: 90rpx;height: 90rpx;">
									<image class="wh" :src="item.icon" mode="aspectFill"></image>
								</view>
								<view class="f-g-1" style="line-height: 30rpx;">
									<view class="f28 flex">
										<view v-if="item.discountType>0 && item.discountType!=4"
											class="f-g-0 yhic f20 cf bf5f f-c hdspan" style="background:  #ff474a">
											{{item.discountType==1?'特':item.discountType==2?'折':item.discountType==5?'赠':'减'}}
										</view>
										{{item.name}}
									</view>
									<view v-if="item.discountType==4" class="f-row cf5f mt10">
										<view class="f20 bs5 b-d p-3-10" :style="{borderColor:'#FF5F2F'}">
											{{item.discountType==4?'第二件打折':'买一送一'}}
										</view>
									</view>
									<view class="c9" style="margin-top: 5rpx;min-height: 24rpx;">
										<text v-if="item.groupName">{{item.groupName}}</text>
										<text v-if="item.groupName&&(item.attribute||item.materialName)">,</text>
										<text v-if="item.attribute">{{item.attribute}}</text>
										<text v-if="item.attribute&&item.materialName">,</text>
										<text v-if="item.materialName">{{item.materialName}}</text>
									</view>
									<view class="c9"><text>x {{item.num}}</text></view>
								</view>
								<view class="f32 f-s-0">
									<text v-if="item.totalOriginalMoney && item.totalOriginalMoney!=item.totalMoney"
										class="c9 f24 t-d-l">${{item.totalOriginalMoney}}</text>
									<text class="f24">$</text>{{item.totalMoney}}
								</view>
							</view>

						</view>
					</view>

					<view v-else>
						<view v-for="(item, index) in cartList.data" :key="String(item.id || index)" class="f-x-bt f20 mt20">
							<view class="mr10 bs10 f-s-0" style="width: 90rpx;height: 90rpx;">
								<image class="wh" :src="item.icon" mode="aspectFill"></image>
							</view>
							<view class="f-g-1" style="line-height: 30rpx;">
								<view class="f28 flex">
									<view v-if="item.discountType>0 && item.discountType!=4"
										class="f-g-0 yhic f20 cf bf5f f-c hdspan" style="background:  #ff474a">
										{{item.discountType==1?'特':item.discountType==2?'折':item.discountType==5?'赠':'减'}}
									</view>
									{{item.name}}
								</view>
								<view v-if="item.discountType==4" class="f-row cf5f mt10">
									<view class="f20 bs5 b-d p-3-10" :style="{borderColor:'#FF5F2F'}">
										{{item.discountType==4?'第二件打折':'买一送一'}}
									</view>
								</view>
								<view class="c9" style="margin-top: 5rpx;min-height: 24rpx;">
									<text v-if="item.groupName">{{item.groupName}}</text>
									<text v-if="item.groupName&&(item.attribute||item.materialName)">,</text>
									<text v-if="item.attribute">{{item.attribute}}</text>
									<text v-if="item.attribute&&item.materialName">,</text>
									<text v-if="item.materialName">{{item.materialName}}</text>
								</view>
								<view class="c9"><text>x {{item.num}}</text></view>
							</view>
							<view class="f32 f-s-0">
								<text v-if="item.totalOriginalMoney && item.totalOriginalMoney!=item.totalMoney"
									class="c9 f24 t-d-l">${{item.totalOriginalMoney}}</text>
								<text class="f24">$</text>{{item.totalMoney}}
							</view>
						</view>
					</view>

					<!-- 打包费 -->
					<view class="f-x-bt f20 mt20" v-if="user.isVip && submitInfo.hyzk">
						<view class="mr10 f-g-1 f-y-c">
							<view class="yhic f22 cf f-c f-g-0" style="background: #EDA555;">V</view>
							<view class="f26">会员优惠</view>
						</view>
						<view class="f32 cf7">
							<view class="f-s-0">{{sl+submitInfo.hyzk}}</view>
						</view>
					</view>
					<view class="f-x-bt f20 mt20" v-if="Number(submitInfo.bzf) !== 0">
						<view class="mr10 f-g-1">
							<view class="f28">包装费</view>
							<!-- <view class="c9">活动减2.5元外送费</view> -->
						</view>
						<view>
							<!-- <text class="c9 wei t-d-l">$2.5</text> -->
							<text class="f32 f-s-0"><text class="f24">$</text>{{submitInfo.bzf}}</text>
						</view>
					</view>
					<!-- 外送费 -->
					<!-- <view class="f-x-bt f20 mt20" v-if="form.deliveryMode === 2">
						<view class="mr10 f-g-1">
							<view class="f28">{{$t('good.delivery_fee')}}</view>
							<view class="c9" v-if="receivingAddress.discount && receivingAddress.discount>0">
								活动减{{receivingAddress.discount}}元外送费</view>
						</view>
						<view>
							<text v-if="receivingAddress.discount"
								class="c9 wei t-d-l">${{receivingAddress.oldMoney}}</text>
							<text class="f32 f-s-0"><text
									class="f24">$</text>{{receivingAddress.deliveryMoney >=0 ? receivingAddress.deliveryMoney:shopGoodsInfo.shopData.distribution.money}}</text>
						</view>
					</view> -->
					<!-- 使用红包 -->
					<view class="mt20" v-if="showSubmit">
						<!-- 满减优惠 -->
						<!-- 平台新客立减 -->
						<view class="f-x-bt f28 p10" v-if="discount.platformNewReduction.money!=0">
							<view>
								<text class="bs10 f24 wei cf"
									style="background:  #ff474a;padding: 0 6rpx;margin-right: 5rpx;">新</text>
								<text>平台新客立减</text>
							</view>
							<view>
								<text class="wei" style="color: #ff474a;"><text
										class="f24">-$</text>{{discount.platformNewReduction.money}}</text>
							</view>
						</view>
						<!-- 门店新客立减 -->
						<view class="f-x-bt f28 p10" v-if="discount.newReduction!=0">
							<view>
								<text class="bs10 f24 wei cf"
									style="background:  #ff474a;padding: 0 6rpx;margin-right: 5rpx;">新</text>
								<text>门店新客立减</text>
							</view>
							<view>
								<text class="wei" style="color: #ff474a;"><text
										class="f24">-$</text>{{discount.newReduction}}</text>
							</view>
						</view>
						<view class="f-x-bt f28 p10" v-if="discount.reduceMoney!=0">
							<view>
								<text class="bs10 f24 wei cf"
									style="background:  #ff474a;padding: 0 6rpx;margin-right: 5rpx;">减</text>
								<text>满减优惠</text>
							</view>
							<view>
								<text class="wei" style="color: #ff474a;"><text
										class="f24">-$</text>{{discount.reduceMoney}}</text>
							</view>
						</view>
						<view class="f-x-bt f28 p10" v-if="submitInfo.mzInfo.title">
							<view>
								<text class="bs10 f24 wei cf"
									style="background:  #ff474a;padding: 0 6rpx;margin-right: 5rpx;">赠</text>
								<text>{{submitInfo.mzInfo.name}}</text>
							</view>
							<view>
								<text class="wei" style="color: #ff474a;">{{submitInfo.mzInfo.title}}</text>
							</view>
						</view>
						<!-- <view class="f-x-bt f28 p10">
							<view style="display: flex;align-items: center;">
								<image style="width: 36rpx;height: 36rpx;margin-right: 4rpx;" src="/static/u-icon.png">
								</image>
								<text>{{$t('good.discount')}}</text>
								<view class="uCoinNum">
									<input type="number" :min="0" :max="uCoinMax" v-model="uCoinNum" @input="handleUCoinInput" maxlength="10"/>
								</view>
							</view>
						</view> -->
						<!-- 发票 -->
						<!-- <view class="f-x-bt f28 p10">
							<view style="display: flex;align-items: center;" @click="toggleInvoice">
								<view class="custom-radio" :class="{'checked': isUseInvoice}">
									<text v-if="isUseInvoice" class="iconfont iconselect"></text>
								</view>
								<text class="mr30 ml10">{{$t('good.select_invoice')}}</text>
							</view>
						</view> -->
						<!-- 使用红包  平台新客立减如何设置为互斥undoShare2-平台红包不可用-->
						<!-- <view class="f-x-bt f28 p10" @click="selectCoupon(1)"
							v-if="discount.platformNewReduction.undoShare!='2'">
							<view>
								<text class="bs10 f24 wei cf"
									style="background:  #ff474a;padding: 0 6rpx;margin-right: 5rpx;">红</text>
								<text>{{system.custom.systemRedbag || '平台红包'}}</text>
							</view>
							<view>
								<block v-if="discount.platConponMoney===0 && discount.platConponNum>0">
									<text class="cf bs10 f24"
										style="background: #ff474a;padding: 2rpx 8rpx;">{{ discount.platConponNum }}张可用</text>
									<text class="iconfont iconinto f22 c9"></text>
								</block>
								<block v-else-if="discount.platConponMoney>0">
									<text class="wei" style="color: #ff474a;"><text
											class="f24">-$</text>{{discount.platConponMoney}}</text>
									<text class="iconfont iconinto f22 c9"></text>
								</block>
								<block v-else>
									<text class="wei f24 cd">暂无可用</text>
								</block>
							</view>
						</view> -->
						<!-- <view class="f-x-bt f28 p10" @click="selectCoupon(3)"
							v-if="user.isVip && form.deliveryMode===2">
							<view>
								<text class="bs10 f24 wei cf"
									style="background:  #ff474a;padding: 0 6rpx;margin-right: 5rpx;">红</text>
								<text>外送红包</text>
							</view>
							<view>
								<block v-if="discount.platConponMoneyPs===0 && discount.platConponNumPs>0">
									<text class="cf bs10 f24"
										style="background: #ff474a;padding: 2rpx 8rpx;">{{ discount.platConponNumPs }}张可用</text>
									<text class="iconfont iconinto f22 c9"></text>
								</block>
								<block v-else-if="discount.platConponMoneyPs>0">
									<text class="wei" style="color: #ff474a;"><text
											class="f24">-$</text>{{discount.platConponMoneyPs}}</text>
									<text class="iconfont iconinto f22 c9"></text>
								</block>
								<block v-else>
									<text class="wei f24 cd">暂无可用</text>
								</block>
							</view>
						</view> -->
						<!-- <view class="f-x-bt f28 p10" @click="selectCoupon(2)">
							<view>
								<text class="bs10 f24 wei cf"
									style="background:  #ff474a;padding: 0 6rpx;margin-right: 5rpx;">券</text>
								<text>商店代金券</text>
							</view>
							<view>
								<block v-if="discount.storeConponMoney===0 && discount.storeConponNum>0">
									<text class="cf bs10 f24"
										style="background: #ff474a;padding: 2rpx 8rpx;">{{ discount.storeConponNum }}张可用</text>
									<text class="iconfont iconinto f22 c9"></text>
								</block>
								<block v-else-if="discount.storeConponMoney>0">
									<text class="wei" style="color: #ff474a;"><text
											class="f24">-$</text>{{discount.storeConponMoney}}</text>
									<text class="iconfont iconinto f22 c9"></text>
								</block>
								<block v-else>
									<text class="wei f24 cd">暂无可用</text>
								</block>
							</view>
						</view> -->
					</view>
				</view>
				<!-- 结算 -->
				<view class="f28 f-x-bt pt20">
					<view class="c9 f-g-1">{{$t('good.discount_rules')}}</view>
					<view class="f-s-0">
						<text>{{$t('good.subtotal')}}</text>
						<text class="wei">$</text>
						<text class="f34 wei">{{submitInfo.nowMoney}}</text>
					</view>
				</view>
			</view>
			<view
				v-if="orderMuster.changePay&&orderMuster.changePay.goodsArr&&orderMuster.changePay.goodsArr.length&&showSubmit"
				class="mt20 p30 bf bs20">
				<view class="p03 mb10 f-y-c">
					<text class="bs10 f24 wei cf"
						style="background:  #ff474a;padding: 0 6rpx;margin-right: 5rpx;">换</text>
					<view class="">超值换购</view>
					<!-- <view v-show="hgInfo.id&&hgInfo.price>hgInfo.money" class="ml20 f22 c9">
						商品最多可为您节省{{sl+blxs(hgInfo.price-hgInfo.money)}}</view> -->
				</view>
				<view class='bf f-row o-x-s p13'>
					<view @click="xzhg(v)" class="bf f-g-0 hgic mr30 bs15 b-s-4 f-row p-r"
						v-for="(v,i) in orderMuster.changePay.goodsArr" :key="String(i)">
						<view class="hgimg">
							<mg-img :src="v.icon"></mg-img>
							<view class="p-a spzk t0 f18 cf bf5f2 l-n ">{{Number((v.money/v.price*10).toFixed(2))}}折
							</view>
						</view>
						<view class='f-1 f-y-bt p-10-15 f24'>
							<view class="t-o-e">{{v.name}}</view>
							<view class="f-y-c">
								<view class="f-1 t-o-e f22">{{sl}}<text class="f28">{{v.money}}</text><text
										class="t-d-l ml10">{{sl+v.price}}</text></view>
								<text class="iconfont icongx f38 ce mt0 ml10" :class="{'cf5f':hgInfo.id==v.id}"></text>
							</view>
						</view>
					</view>
				</view>
			</view>
			<!-- 备注 -->
			<view class="p2 bs20 f28 bf mt10" v-if="showSubmit">

				<view class="f-x-bt f28 mt20"
					@click="go('navigateTo',`/yb_o2ov2/home/<USER>">
					<view>
						<text class="f28 wei">{{$t('good.remark')}}</text>
					</view>
					<view class="t-o-e t-r" style="width: 500rpx;">
						<text class="c9">{{form.userNote||$t('good.remark_placeholder')}}</text>
						<text class="iconfont iconinto f24 c9"></text>
					</view>
				</view>

				<!-- <view class="f-x-bt f28 mt30" v-if="shopGoodsInfo.moreSet.cjsl!='2' && showSubmit">
					<view>
						<text class="f28 wei">{{system.custom.tablewareName || $t('good.quantity_of_tableware')}}</text>
					</view>
					<view @click="showTableware=true">
						<text class="c9">{{selectedTableware}}</text>
						<text class="iconfont iconinto f24 c9"></text>
					</view>
				</view> -->
			</view>
			<!-- 提交订单 -->
			<view class="payBox f20" v-if="showSubmit">
				<view class="ml40 f-c-xc">
					<view class="wei" style="line-height: 44rpx;">
						<text>$</text>
						<text class="f40">{{submitInfo.nowMoney}}</text>
					</view>
				</view>
				<view class="t-c" style="width: 200rpx;line-height: 100rpx;"
					:style="{background:tColor,color:fontColor}" @click="submitForm">
					<text class="f30 wei">{{$t('good.submit_order_text')}}</text>
				</view>
			</view>
			<!-- 弹窗 -->
			<!-- 选择收货地址 -->
			<u-popup v-model="showAddress" mode="bottom" closeable :height="popupHeight" border-radius="20">
				<view class="p-r">
					<view class="f32 wei t-c bf posi-s" style="top:0;height: 100rpx;line-height: 100rpx;">
						{{$t('address.select_placeholder')}}
					</view>
					<view class="f32 t-c p-a"
						style="bottom:0;height: 130rpx;width: 750rpx;z-index: 1;background: linear-gradient(0deg, #fff, rgba(255,255,255,0));">
						<view class="mla bs20" style="width: 700rpx;height: 70rpx;line-height: 70rpx;"
							:style="{background:tColor,color:fontColor}"
							@click="go('navigateTo', '/yb_o2ov2/my/address/edit')"> {{$t('address.add_address')}}
						</view>
					</view>
					<scroll-view scroll-y="true" :style="{height:`${popupHeight-100}rpx`}" class="address-scroll">
						<view class="f-col" style="padding-bottom: 120rpx;">
							<view v-for="item in filteredAddresses" :key="String(item.id)"
								class="address-item" :class="{'address-item-active': receivingAddress.id === item.id}"
								@click="selectAddress(item)">
								<view class="address-item-content">
									<text class="iconfont iconposition f28 pt10 address-icon"></text>
									<view class="address-info">
										<view class="address-header">
											<text class="address-label" :style="{background: item.label === '家' ? '#FFE4E1' : '#E6F3FF', color: item.label === '家' ? '#FF6B6B' : '#5CB3E6'}">{{item.label}}</text>
											<text class="address-details">{{ item.address }}{{item.details}}</text>
											<text class="iconfont iconeditfill c9 f28 edit-icon"
												@click.stop="go('navigateTo',`/yb_o2ov2/my/address/edit?id=${item.id}`)"></text>
										</view>
										<view class="address-footer">
											<text class="user-name">{{item.userName}} {{item.sex=='1'?$t('address.mr'):$t('address.ms')}}</text>
											<text class="user-phone">{{item.userTel}}</text>
										</view>
									</view>
									<text v-if="receivingAddress.id === item.id" class="iconfont iconselect f32" :style="{color: tColor}"></text>
								</view>
							</view>
							<view v-if="overAddress.length !==0" class="over-address-title">超外送范围地址</view>
							<view v-for="item in overAddress" :key="String(item.id)" class="address-item address-item-disabled">
								<view class="address-item-content">
									<text class="iconfont iconposition f28 pt10 address-icon"></text>
									<view class="address-info">
										<view class="address-header">
											<text class="address-details">{{ item.address }}{{item.details}}</text>
											<text class="iconfont iconeditfill c9 f28 edit-icon"
												@click.stop="go('navigateTo',`/yb_o2ov2/my/address/edit?id=${item.id}`)"></text>
										</view>
										<view class="address-footer">
											<text class="user-name">{{item.userName}} {{item.sex=='1'?$t('address.mr'):$t('address.ms')}}</text>
											<text class="user-phone">{{item.userTel}}</text>
										</view>
									</view>
								</view>
							</view>
						</view>
					</scroll-view>
				</view>
			</u-popup>
			<!-- 选择收货时间 -->
			<u-popup v-model="showTime" mode="bottom" closeable :height="popupHeight" border-radius="20">
				<view class="p-r">
					<view class="f32 wei t-c bf posi-s" style="top:0;height: 100rpx;line-height: 100rpx;">
						{{form.deliveryMode==2?$t('good.selecte_takeout_time'):$t('good.selecte_time')}}
					</view>
					<view class="flex" :style="{height:`${popupHeight-100}rpx`}">
						<view style="background: #f5f6f9;">
							<scroll-view scroll-y="true" style="width: 300rpx;"
								:style="{height:`${popupHeight-155}rpx`}">
								<view class="t-c " style="background: #f5f6f9;">
									<view v-for="(day,index) in storeTimeList" :key="String(index)" class="p3"
										:class="selectDay===index?'bf':''" style="letter-spacing: 4rpx;"
										@click="selectDay=index">{{day.dayTitle}}</view>
								</view>
							</scroll-view>
						</view>
						<scroll-view scroll-y="true" style="width: 450rpx;" :style="{height:`${popupHeight-155}rpx`}">
							<view class="p02">
								<view :class="startTimeIndexActive == index ? 'active' : ''"
									v-for="(item,index) in storeTimeList[selectDay].time" :key="String(item.time)" class="p30"
									style="border-bottom: 1px solid #f5f6f9;" @click="changeStartTime(index)">
									<text>{{item.timeTitle}}</text>
									<!-- <text class="c9">（3.5元外送费）</text> -->
								</view>
							</view>
						</scroll-view>
						<scroll-view scroll-y="true" style="width: 450rpx;" :style="{height:`${popupHeight-155}rpx`}">
							<view class="p02">
								<view v-for="(item,index) in storeTimeList[selectDay].time" :key="String(item.time)" class="p30"
									style="border-bottom: 1px solid #f5f6f9;" @click="changeTime(index)">
									<text>{{item.timeTitle}}</text>
									<!-- <text class="c9">（3.5元外送费）</text> -->
								</view>
							</view>
						</scroll-view>
					</view>
				</view>
			</u-popup>
			<!-- 选择餐具 -->
			<u-popup v-model="showTableware" mode="bottom" :height="popupHeight" border-radius="20">
				<view class="t-c p1 posi-s wei f30 bf" style="top: 0;height: 100rpx;line-height: 100rpx;">选择餐具数量</view>
				<view class="p-r h100" :style="{height:`${popupHeight-200}rpx`}">
					<scroll-view scroll-y="true" class="wh">
						<view class="t-c tableware"
							:style="form.tablewareNum === -1?`color:${tColor};font-weight:bold`:''"
							@click="selectTableware(0)">无需餐具</view>
						<view v-for="(item,index) in 10" :key="String(index)" class="t-c tableware"
							:style="form.tablewareNum === index+1?`color:${tColor};font-weight:bold`:''"
							@click="selectTableware(index+1)">{{index+1}}份</view>
						<view class="t-c tableware"
							:style="form.tablewareNum === 10?`color:${tColor};font-weight:bold`:''"
							@click="selectTableware(11)">10份以上</view>
					</scroll-view><strong></strong>
				</view>
				<view class="t-c p1 posi-s wei f30 bf"
					style="bottom: 0;height: 100rpx;border-top:1rpx solid #f5f6f9;background: #fafafa;"
					@click="showTableware=false">取消</view>
			</u-popup>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapActions
	} from "vuex";
	import utils from '@/common/utils.js'
	import mgImg from '@/components/common/mg-img.vue'
	import {
		utilMixins
	} from "@/common/util-mixins.js"
	export default {
		components: {
			mgImg,
		},
		data() {
			return {
				// 开始天数
				startDay: '',
				startTimeIndexActive: '',
				deliveryText: '立即送出',
				diningType: [], //商店、打包
				distributionSupport: [], //外卖、自取
				deliveryArray: [{
					name: this.$t('good.send_it_out_immediately'),
					value: 2
				}], //立即送出、预约
				storeAddressinfo: {}, //自取店家位置
				showTableware: false,
				tablewarevalue: [0],
				selectDay: 0,
				showTime: false,
				showAddress: false,
				agreeTo: true,
				isUseInvoice: false, // Add invoice selection state
				invoiceData: {},
				storeTimeOrderType: 1, //1外卖 2自取
				form: {
					deliveryMode: 2, //2外卖 1自取
					couponId: {
						store: '',
						platform: '',
						deliveryCouponId: ''
					},
					isOut: '',
					// 备注
					userNote: '',
					// 餐具
					tablewareNum: 0,
					// 店铺id
					storeId: 0,
					// 手机号 到店自取时必填
					userTel: null,
					//1： 预约 2:立即送出
					appointment: 2,
					// 地址id
					userAddId: null,
					useType: '1',
				},
				storeId: '',
				cartList: {}, //购物车
				myAddressList: [], //地址
				receivingAddress: {}, // 默认收货地址
				storeTimeList: [], // 外送时间
				statrTime: '',
				startDateTime: '',
				timeText: '', //送达文字描述
				discount: {
					newReduction: 0, //门店新客立减
					platformNewReduction: {
						money: 0,
						undoShare: '',
					}, //平台新客立减
					reduceMoney: 0, //活动满减
					storeConponNum: 0, //可用商户代金券数量
					platConponNum: 0, //可用平台代金券数量
					platConponNumPs: 0, //可用平台代金券数量
					storeConponMoney: 0, //选择sj代金券减免金额
					platConponMoney: 0, //选择平台代金券减免金额
					platConponMoneyPs: 0, //选择平台代金券减免金额
					give: {},
				},
				ztTypeArr: [],
				orderMuster: {},
				hgInfo: {},
				togetherId: 0,
				customStyle: {
					marginRight: '10px'
				},
				numList: [{
					name: '完成选购'
				}, {
					name: '提交订单'
				}, {
					name: '完成拼单'
				}, ],
				timer: null,
				steps: 1,
				togetherUser: [],
				uCoinNum: 0,
			}
		},
		onLoad(option) {
			this.discount = Object.assign(this.discount, JSON.parse(option.discount))
			this.storeId = option.storeId
			if (option.togetherId) {
				this.togetherId = option.togetherId
				if (!this.timer && this.togetherId != 0) {
					this.timer = setInterval(() => {
						setTimeout(this.getCartList, 0)
					}, 6000); //6秒刷新一次
				}
			}
			this.form.userTel = this.user.userTel || ''
			this.storeAddressinfo = uni.getStorageSync('storeAddress')
			
			// if (this.shopGoodsInfo.shopData.takeOutSet.distributionSupport.length === 1) {
			// 	this.form.deliveryMode = Number(this.shopGoodsInfo.shopData.takeOutSet.distributionSupport[0]) === 1 ? 2 :
			// 		1
			// 	if (this.orderSet.first == '1') {
			// 		this.form.useType = 1
			// 	} else {
			// 		this.form.useType = 2
			// 	}
			// }
			// if (this.shopGoodsInfo.shopData.takeOutSet.distributionSupport.length > 1) {
			// 	if (this.orderSet.first == '1') {
			// 		this.form.deliveryMode = 1
			// 		this.form.useType = 2
			// 	} else {
			// 		this.form.deliveryMode = 2
			// 		this.form.useType = 1
			// 	}
			// }
			
			//设置自取类型
			// if (this.shopGoodsInfo.moreSet.diningType.includes('1')) {
			// 	this.ztTypeArr.push({
			// 		value: '1',
			// 		name: this.$t('good.in_store_dining'),
			// 		icon: 'icondianneitubiao f36',
			// 	})
			// }
			// if (this.shopGoodsInfo.moreSet.diningType.includes('2')) {
			// 	this.ztTypeArr.push({
			// 		value: '2',
			// 		name: this.$t('good.pack_and_take_away'),
			// 		icon: 'icondabao f42',
			// 	})
			// }
			// this.ztTypeArr[0] && (this.form.isOut = this.ztTypeArr[0].value)

			// this.getDelivery()
			this.init()
			this.getInvoiceData()
		},
		onShow() {
			// this.init()
			this.getMyAddressList() //获取 收货地址列表
			// 修改备注
			let note = uni.getStorageSync('_temp_note')
			if (note) {
				this.form.userNote = note
				uni.removeStorageSync('_temp_note')
			}
		},
		mixins: [utilMixins],
		computed: {
			...mapState({
				shopGoodsInfo: state => state.shopGoodsInfo,
				currency: state => state.config.currency,
				user: state => state.user,
				orderSet: state => state.config.orderSet,
			}),
			turnTColor() {
				return `background:${utils.jjldColor(this.tColor,0)+11}`
			},
			appointment() {
				return this.shopGoodsInfo.shopData.businessState.state === 3 ? 1 : ''
			},
			overAddress() {
				return this.myAddressList.filter(item => item.isClick === 2)
			},
			hideAddress() {
				return this.myAddressList.filter(item => item)
			},
			popupHeight() {
				return String(this.wHeight / 2 * this.pxToRpxRate)
			},
			selectedTableware() {
				if (typeof this.form.tablewareNum === "number") {

					return this.form.tablewareNum === 0 ? '无需餐具' : this.form.tablewareNum === 11 ? '10份以上' : this.form
						.tablewareNum + '份'
				} else {
					return this.$t('good.selecte_placeholder')
				}
			},
			yuyueOpen() {
				return this.shopGoodsInfo.shopData.advanceSet.open && this.shopGoodsInfo.shopData.advanceSet.open.indexOf(
					'1') > -1
			},
			zitiOpen() {
				return this.shopGoodsInfo.shopData.advanceSet.open && this.shopGoodsInfo.shopData.advanceSet.open.indexOf(
					'2') > -1
			},
			submitInfo() {
				let money = (+this.cartList.price) //商品原价已经经过满减活动后
					-
					this.discount.platformNewReduction.money //平台新客立减 TODO需要判断是否是新客
					-
					this.discount.newReduction //新客立减 TODO需要判断是否是新客
					-
					this.discount.storeConponMoney // 商店代金券减免金额
					-
					this.discount.platConponMoney // 平台红包减免金额
				let psf = (this.form.deliveryMode === 1 ? 0 : +(this.receivingAddress.deliveryMoney ?? this.shopGoodsInfo
						.shopData.distribution.money ?? 0)), //外送费 判断外卖自取、判断有无默认地址（没有默认地址按当前定位算）
					bzf = this.cartList.boxMoney ? +this.cartList.boxMoney : 0,
					total = 0,
					mzInfo = {}
				money <= 0 && (money = 0.01)
				total = +(money + psf).toFixed(2)
				if (this.form.deliveryMode == 1 && this.form.isOut == 1) {
					bzf = 0
					total = +(money + psf - this.cartList.boxMoney).toFixed(2)
				}
				let mzitem = this.shopGoodsInfo.discount.give.moneyArr && this.shopGoodsInfo.discount.give.moneyArr.find(
					v =>
					total >= v.fullMoney)
				mzitem && (mzInfo = {
					name: '满赠优惠',
					title: `${mzitem.give}`
				})
				total <= 0 && (total = 0.01)
				//优惠价格=购物车原价+应付外送费的钱-实付的钱
				// console.log(11111,this.cartList.oldPrice,this.receivingAddress,this.receivingAddress.oldMoney,total)
				let yhzj = (+this.cartList.oldPrice) + (this.form.deliveryMode == 2 && this.receivingAddress ? Number(this
					.receivingAddress.discount > 0 ? this.receivingAddress.oldMoney : this.receivingAddress
					.deliveryMoney) : 0) - total
				yhzj += this.blxs(this.cartList.vipDiscount ? this.cartList.vipDiscount : 0)
				if (this.hgInfo.id) {
					total = +this.hgInfo.money + total
				}
				return {
					nowMoney: total.toFixed(2),
					hdyh: yhzj.toFixed(2) || 0,
					reduceMoney: 0,
					hyzk: this.blxs(this.cartList.vipDiscount ? this.cartList.vipDiscount : 0),
					bzf,
					mzInfo
				}
			},
			selfList() { //自己的商品
				return this.cartList.data !== undefined ? this.cartList.data.filter((item) => item.userId == this.user
					.id) : []
			},
			otherList() {
				return this.cartList.data !== undefined ? this.cartList.data.filter((item) => item.userId != this.user
					.id) : []
			},
			getTogetherUser() {
				return this.cartList.togetherUser !== undefined ? this.cartList.togetherUser.filter((item) => item.id !=
					this.user.id) : []
			},
			selfUser() {
				return this.cartList.togetherUser !== undefined ? this.cartList.togetherUser.filter((item) => item.id ==
					this.user.id)[0] : {
					type: 0
				}
			},
			commander() { //团长
				return this.cartList.togetherUser !== undefined ? this.cartList.togetherUser.filter((item) => item.type ==
					0)[0] : {
					id: 0
				}
			},
			isCommander() {
				try {
					return this.commander.id == this.selfUser.id
				} catch {}
				return false

			},
			showSubmit() {
				if (this.togetherId > 0 && this.isCommander)
					return true
				if (this.togetherId == 0)
					return true
				return false
			},
			filteredAddresses() {
				return this.myAddressList?.filter(addr => addr && addr.isClick !== 2) || []
			},
			uCoinMax() {
				return Math.min(this.submitInfo.nowMoney * 0.9, Number(this.user.uCoin))
			},
		},
		watch: {
			showSubmit: function(val) {
				if (val) {
					this.init()
				}
			}
		},
		methods: {
			...mapActions(["getConfig"]),
			handleBack() {
				const pages = getCurrentPages();
				if (pages.length > 1) {
					this.go('back');
				} else {
					this.go('reLaunch', `/yb_o2ov2/my/supplier/store?storeId=${this.storeId}`);
				}
			},
			async getInvoiceData() {
				let {
					data
				} = await this.util.request({
					url: this.api.invoice,
					method: 'GET',
				})
				this.invoiceData = data
				this.isUseInvoice = data.status
			},
			toggleInvoice() {
				// 如果当前是选中状态，直接取消选中
				if(this.isUseInvoice) {
					this.isUseInvoice = false;
					return;
				}
				
				// 如果要选中，检查发票信息
				if(!this.invoiceData.email || !this.invoiceData.companynumberheader || !this.invoiceData.unifiednumbering) {
					uni.showModal({
						title: this.$t('common.tips'),
						content: this.$t('good.invoice_info_incomplete'),
						success: (res) => {
							if (res.confirm) {
								this.go('navigateTo', '/yb_o2ov2/my/issueAnInvoice/invoice');
							}
						}
					});
					return;
				}
				
				this.isUseInvoice = true;
			},
			selectAddress(item) {
				this.form.userAddId = item.id
				this.form.userTel = item.userTel
				this.receivingAddress = item
				this.showAddress = false
				this.getStoreTime()
				// 获取外送地址信息
				this.getReceivingAddress(item.lat, item.lng);
			},
			radioChange(event) {
				if (event.detail.value == 1) {
					this.showTimeM(1)
				}
				this.form.appointment = event.detail.value
			},
			async getDelivery() {
				const {
					data
				} = await this.util.request({
					url: this.api.orderGetDelivery,
					method: 'GET',
					data: {
						storeId: this.storeId
					}
				})
				this.diningType = data.takeOutSet.data.diningType
				this.distributionSupport = data.takeOutSet.data.distributionSupport
				if (data.advanceSet) {
					if (data.advanceSet.data.open.includes('1')) {
						this.deliveryArray.push({
							name: this.$t('good.appointment_for_delivery'),
							value: 1
						})
					}
				}
			},
			cancelTimer() {
				if (this.timer) {
					clearInterval(this.timer)
					this.timer = null
				}
			},
			async editGoods() {
				await this.util.request({
					url: this.api.modifyTogetherStatus,
					method: 'POST',
					data: {
						storeId: this.storeId,
						together: this.togetherId,
						status: 1
					}
				}).then((res) => {

					if (res.msg == 2) {
						this.util.modal('拼单订单已锁定，要修改联系发起人', '提示')
						return
					}

					uni.navigateBack();
				})
			},
			async cancelTogether() {
				let {
					code
				} = await this.util.request({
					url: this.api.cancelTogether,
					method: 'GET',
					data: {
						storeId: this.storeId,
						togetherId: this.togetherId
					}
				})
				setTimeout(() => {
					//uni.navigateBack();
					uni.hideLoading()
					this.go('navigateTo',
						`/yb_o2ov2/my/supplier/store?storeId=${this.storeId}&cancel=1`
					)
				}, 1000)

			},
			handleCancle() {
				this.util.modal('确定取消不会保留本次拼单内容', '提示').then(res => {
					uni.showLoading({
						title: '載入中'
					});
					this.cancelTimer()
					this.cancelTogether()
				})
			},
			getCarList(id) {
				return this.otherList.filter((item) => item.userId == id)
			},
			clickztMode(v) {
				this.form.isOut = v.value
			},
			selectCoupon(type) {
				// type 2商店,1平台
				if (type == 2 && this.discount.storeConponNum <= 0 || type == 1 && this.discount.platConponNum <= 0) return
				if (type == 1) {
					this.go('navigateTo',
						`/yb_o2ov2/home/<USER>/platform-coupon?storeId=${this.storeId}&couponId=${this.form.couponId.platform}&useType=${this.form.useType}`
					)
				} else if (type == 3) {
					this.go('navigateTo',
						`/yb_o2ov2/home/<USER>/platform-coupon?storeId=${this.storeId}&couponId=${this.form.couponId.deliveryCouponId}&useType=${this.form.useType}&type=3`
					)
				} else {
					this.go('navigateTo',
						`/yb_o2ov2/home/<USER>/store-coupon?storeId=${this.storeId}&couponId=${this.form.couponId.store}&useType=${this.form.useType}`
					)
				}
			},
			checkAgreement() {
				// uni.setStorageSync('noticeDetail',{body:this.currency.selfAgreement})
				// this.go('navigateTo',`/yb_o2ov2/home/<USER>
				this.go('navigateTo', `/yb_o2ov2/my/other/gywm?t=${this.system.custom.selfName || '到店自取'}服务协议&p=3`)
			},
			openNavigation() {
				this.util.ckWz(this.storeAddressinfo)
			},
			changeDeliveryMode(mode) {
				this.storeTimeOrderType = this.form.deliveryMode
				this.form.deliveryMode = mode
				this.form.useType = mode == 2 ? 1 : 2
				this.form.appointment = 2
				this.getStoreTime()
				this.getIsNew()
				this.getCoupon(2)
				this.getCoupon(1)
			},
			chooseAddress(item) {
				this.form.userAddId = item.id
				this.form.userTel = item.userTel
				this.getReceivingAddress(item.lat, item.lng)
				this.showAddress = false
				this.getStoreTime()
			},
			async init() {
				this.getCartList() //购物车商品
				//console.log(this.showSubmit)
				if (this.showSubmit) {

					if (this.shopGoodsInfo.shopData.takeOutSet.distributionSupport.includes('1')) {
						await this.getReceivingAddress() //获取默认 收货地址
					}
					// this.getMyAddressList() //获取 收货地址列表
					this.getStoreTime() // 送达/自取时间
					this.getIsNew() //是否参与新客立减
					this.getCoupon(2)
					this.getCoupon(1)
					this.getCoupon(3)
				}
			},
			async getIsNew() {
				let {
					data
				} = await this.util.request({
					url: this.api.supplierorderMuster,
					method: 'GET',
					data: {
						storeId: this.storeId,
					}
				})
				this.discount.newReduction = data.newReduction
				this.discount.platformNewReduction = data.platformNewReduction
				this.orderMuster = data
			},
			xzhg(v) {
				this.hgInfo = v.id == this.hgInfo.id ? {} : v
			},
			async getCoupon(type) { //可用优惠券
				return
				let {
					data
				} = await this.util.request({
					url: this.api.kyyhq,
					method: 'POST',
					data: {
						storeId: this.storeId,
						type: type, //1平台 2商户代金券
						useType: this.form.useType, //1外卖 2自取
					}
				})
				if (type == 2) {
					this.discount.storeConponNum = data.normal.length
				}
				if (type == 1) {
					this.discount.platConponNum = data.normal.length
				}
				if (type == 3) {
					this.discount.platConponNumPs = data.normal.length
				}
			},
			selectTableware(index) {
				this.form.tablewareNum = index
				this.showTableware = false
			},
			// 开始时间
			changeStartTime(time) {
				this.startTimeIndexActive = time
				this.startDay = this.selectDay
				this.statrTime = this.storeTimeList[this.startDay].day + ' ' + this.storeTimeList[this.startDay].time[time]
					.timeTitle
				this.startDateTime = this.storeTimeList[this.startDay].time[time].time
			},
			changeTime(time) {
				let day = this.selectDay
				let item = this.storeTimeList[day].time[time]
				if (time - this.startTimeIndexActive < 0) {
					uni.showToast({
						title: '结束时间低于开始时间了哟',
						duration: 2000,
						icon: "none"
					});
					return
				}
				if (((time - this.startTimeIndexActive) > 2)) {
					uni.showToast({
						title: '时间间隔最多半小时哦',
						duration: 2000,
						icon: "none"
					});
					return
				}
				// 如果结束时间大于开始时间则中止
				if ((item.timeTitle - this.statrTime) > '01:00') return
				this.form.serviceAt = `${this.statrTime}-${item.timeTitle}`
				this.form.serviceTime = item.time
				this.isnow = item.isnow // 0 预约  1立即送出
				this.deliveryText = item.isnow === 0 ? '预约送达' : '立即送出'
				if (this.form.deliveryMode === 2) { //外卖
					//今天立即送出
					if (day === 0 && time === 0) {
						this.timeText = '大约' + item.timeTitle + '送达'
						return this.showTime = false
					}
					//今天之内送出
					// this.deliveryText = '预约送达'
					this.timeText = item.timeTitle
					if (day !== 0) return this.showTime = false
					//不是今天送出
					this.timeText = item.timeTitle
					this.showTime = false
				} else if (this.form.deliveryMode === 1) { //自取
					//今天自取
					this.timeText = item.timeTitle
					if (day === 0) {
						return this.showTime = false
					} else {
						return this.showTime = false
					}
					//不在今天内自取
					this.timeText = item.timeTitle
				}
			},
			showTimeM(type) {
				if (type == 1 && this.shopGoodsInfo.shopData.advanceSet.open && this.shopGoodsInfo.shopData.advanceSet.open
					.indexOf('1') > -1) {
					this.showTime = true
				} else if (type == 2 && this.shopGoodsInfo.shopData.advanceSet.open && this.shopGoodsInfo.shopData
					.advanceSet.open.indexOf('2') > -1) {
					this.showTime = true
				} else {
					this.showTime = false
				}
			},
			submitForm: utils.throttle(async function() {
				if (this.form.deliveryMode === 2) {
					if (!this.receivingAddress.address) {
						this.util.message('請新增收貨地址', 3);
						return;
					}

				}
				this.form.storeId = this.storeId
				const parmas = {
					// 备注
					userNote: this.form.userNote,
					// 店铺id
					storeId: this.storeId,
					// 地址id
					userAddId: this.receivingAddress.id,
					useType: '1',
					// ucoin: this.uCoinNum,
					invoiceid: this.isUseInvoice  ? this.invoiceData.id : '',
				}
				let payData = await this.util.request({
					url: this.api.saveorder,
					contentType: 'application/json',
					mask: '下单中',
					method: 'POST',
					data: parmas
				})
				if (payData.code == 1) {
					this.getPayConfig()
					let data = {}
					data.orderId = payData.data
					data.orderType = 1,
						//付款前需要保存付款信息传给付款页面
						this.setPayInfo(data)
					this.go('redirectTo', '/yb_o2ov2/my/pay/index')
				} else {
					return this.util.message(payData.msg || payData.data, 3, 2000)
				}
			}, 1000),
			getPayConfig() {
				this.getConfig({
					name: 'payConfig',
					api: this.api.config,
					data: {
						ident: 'payConfig'
					}
				})
			},
			async getTogetherStatus() {},
			async getCartList() { //购物车商品
				// console.log('this.params.storeId',this.storeId)
				let {
					data
				} = await this.util.request({
					url: this.api.getsuppliercar,
					method: 'GET',
					data: {
						storeId: this.storeId,
						together: this.togetherId,
						item: '1' //1外卖 2店内 3快餐
					}
				})
				this.cartList = data
				//this.cartList.reduce
				if (this.togetherUser.length != data.togetherUser.length) {
					this.getCoupon(1)
					this.getCoupon(2)
					this.getCoupon(3)
				}
				this.togetherUser = data.togetherUser
				if (data.togetherStatus == 3) {
					this.steps = 2
					this.cancelTimer()
				}
			},
			async getReceivingAddress(lat, lng) { //获取默认 收货地址
				let {
					data
				} = await this.util.request({
					// url: this.api.receivingAddress,
					url: this.api.supplierAddress,
					method: 'POST',
					data: {
						storeId: this.storeId,
						addressId: this.form.userAddId,
						lat: lat || this.latLng.latitude,
						lng: lng || this.latLng.longitude,
						deliveryCouponId: this.form.couponId.deliveryCouponId ? this.form.couponId
							.deliveryCouponId : ''
					}
				})
				this.receivingAddress = data
			},
			async getMyAddressList() { //获取 收货地址列表
				let {
					data
				} = await this.util.request({
					url: this.api.getsupplierMyAddress,
					method: 'GET',
					data: {
						storeId: this.storeId
					}
				})
				this.myAddressList = data

			},
			async getStoreTime() {
				let {
					data
				} = await this.util.request({
					url: this.api.supplierTime,
					method: 'GET',
					data: {
						userAddId: this.form.deliveryMode === 2 ? this.receivingAddress.id : '',
						storeId: this.storeId,
						orderType: this.storeTimeOrderType
					}
				})
				if (data[0]) {
					this.storeTimeList = data
					this.changeStartTime(0)
					this.changeTime(0)
				} else {
					// uni.showModal({
					// 	title: '提示',
					// 	content: '此商店营业时间或预约单设置不合理，无有效的供用户选择的时间，无法下单',
					// 	showCancel: false,
					// 	success: res => this.go('back')
					// })
				}
			},
			handleUCoinInput(e) {
				let value = Number(e.detail.value);
				let maxAmount = Math.min(Number(this.user.uCoin), this.submitInfo.nowMoney * 0.9);
				value = Math.floor(value);
				if (value > maxAmount) {
					this.uCoinNum = maxAmount;
					uni.showToast({
						title: this.$t('good.max_u_coin_usage', { amount: maxAmount }),
						icon: 'none'
					});
				} else if (value < 0) {
					this.uCoinNum = 0;
				} else {
					this.uCoinNum = value;
				}
			},
		}
	}
</script>

<style scoped lang="scss">
	/deep/.uni-radio-input {
		width: 30rpx;
		height: 30rpx;
		border-radius: 0;
	}

	.distance-text {
		padding: 8rpx 16rpx;
		border-radius: 30rpx;
		box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.3);
		margin-bottom: 12rpx;
	}

	.type {
		height: 50rpx;
		border-radius: 20rpx;

		.leftItem {
			position: absolute;
			left: 0;
			height: 100%;
			height: 70rpx;
			line-height: 70rpx;
			font-size: 26rpx;
			background: rgba(151, 109, 39, 0.067);
			border-radius: 20rpx 0 20rpx 0;
		}

		.rightItem {
			position: absolute;
			right: 0;
			height: 100%;
			height: 70rpx;
			font-size: 26rpx;
			line-height: 70rpx;
			background: rgba(151, 109, 39, 0.067);
			border-radius: 0 20rpx 0 20rpx;
		}

		.active {
			bottom: 0;
			font-size: 30rpx;
			font-weight: bold;
			border-radius: 20rpx;
			background: #FFF;
		}

		.singleActive {
			position: absolute;
			right: 0;
			height: 100%;
			height: 70rpx;
			font-size: 26rpx;
			line-height: 70rpx;
			background: #FDF8E2;
			border-radius: 0 20rpx 0 20rpx;
		}
	}

	.payBox {
		position: fixed;
		bottom: 80rpx;
		width: 700rpx;
		height: 100rpx;
		display: flex;
		justify-content: space-between;
		color: #FFF;
		background: #333;
		border-radius: 50rpx;
		overflow: hidden;
	}

	.tableware {
		height: 80rpx;
		line-height: 80rpx;
		border-bottom: 1rpx solid #f5f6f9;
	}

	.pstypec {
		width: 300rpx;
		height: 75rpx;
		border: 1rpx solid #333;
		position: relative;
	}

	.yddsj {
		right: 0;
		bottom: 0;
		width: 0;
		height: 0;
		border-bottom: 41rpx solid #fff;
		border-left: 46rpx solid transparent;
	}

	.gou {
		right: 4rpx;
		bottom: 3rpx;
	}

	.hgic {
		width: 382rpx;
		height: 140rpx;

		.hgimg {
			width: 140rpx;
			height: 140rpx;
		}

		.spzk {
			border-bottom-right-radius: 15rpx;
			padding: 5rpx 14rpx;
		}
	}

	.active {
		background-color: #f5f6f9;
	}

	.outer3 {
		background-color: #f7f7f7;
		border-radius: 10rpx;
		height: 50rpx;
	}

	.custom-radio {
		width: 30rpx;
		height: 30rpx;
		border: 1px solid #d1d1d1;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		
		&.checked {
			background-color: #fff;
			border-color: #FFCC00;
			
			.iconselect {
				color: #FFCC00;
				font-size: 30rpx;
			}
		}
	}

	.address-scroll {
		background: #f8f8f8;
	}

	.address-item {
		margin: 20rpx;
		background: #fff;
		border-radius: 12rpx;
		transition: all 0.3s;
		
		&-active {
			background: #f0f9ff;
			border: 2rpx solid #5cb3e6;
		}
		
		&-disabled {
			opacity: 0.6;
		}
		
		&-content {
			padding: 24rpx;
			display: flex;
			align-items: flex-start;
		}
	}

	.address-info {
		flex: 1;
		margin-left: 16rpx;
	}

	.address-header {
		display: flex;
		align-items: center;
		margin-bottom: 12rpx;
	}

	.address-label {
		padding: 4rpx 12rpx;
		border-radius: 6rpx;
		font-size: 24rpx;
		margin-right: 12rpx;
	}

	.address-details {
		flex: 1;
		font-size: 28rpx;
		font-weight: 500;
		color: #333;
	}

	.edit-icon {
		padding: 10rpx;
	}

	.address-footer {
		display: flex;
		align-items: center;
		font-size: 24rpx;
		color: #999;
	}

	.user-name {
		margin-right: 20rpx;
	}

	.over-address-title {
		padding: 20rpx 40rpx;
		color: #999;
		font-size: 26rpx;
	}

	.address-icon {
		color: #5cb3e6;
		font-size: 32rpx;
	}

	.uCoinNum {
		border: 1rpx solid #333;
		border-radius: 4rpx;
		padding: 0 10rpx;
		margin-left: 24rpx;
		width: 200rpx;
		height: 48rpx;
		display: flex;
		align-items: center;
	}

	.uCoinNum input {
		width: 100%;
		height: 100%;
		border: none;
		outline: none;
		font-size: 28rpx;
		background: transparent;
	}
</style>