<template>
	<view class="p-r">
		<view class="order-info-navigation-bar">
			<!-- 返回 -->
			<view @click="go('back')">
				<text class="iconfont iconback f40"></text>
			</view>
			<view @click="showTrack=false" class="f40 wei mt10 f-y-c" style="line-height: 44rpx;">
				<block v-if="orderInfo.state === '1'">
					<view v-if="orderSet.autoClose==='1'" class="flex">
						<view>等待付款,剩余</view>
						<view class="dis-in" style="width: 110rpx;" :style="{color:tColor}" v-if="stime">{{stime}}
						</view>
					</view>
					<view v-else>等待付款</view>
				</block>
				<block v-else>{{stateText}}</block>
			</view>
		</view>
		<scroll-view :scroll-y="true" style="height: 93vh;overflow-y: auto;-webkit-overflow-scrolling: touch;">
			<view class="card">
				<!-- 图形 -->
				<view class="w100">
					<view class="mla"
						style="padding: 2rpx;width: 50rpx;border-top: 1px solid #ddd;border-bottom: 1px solid #ddd;">
					</view>
				</view>

				<!-- 外卖 -->
				<block v-if="orderInfo.deliveryMode !== '10'">
					<!-- 尚未付款 -->
					<view v-if="orderInfo.state === '1' && orderSet.autoClose==='1'">
						<view class="f26 c6 mt10 pb20 b-b">{{$t('order.tips')}}：{{$t('order.exceed')}}{{orderSet.closeTime}}{{$t('order.tips_text')}}</view>
					</view>
					<!-- 已接单 -->
					<view v-if="orderInfo.state === '3'" class="f30 c0 wei">
						<view>{{$t('order.expected')}}<text :style="{color:tColor}">{{orderInfo.serviceAt}}</text>{{$t('order.deliver')}}</view>
						<view class="f26 c6 mt10 pb20 b-b">
							{{$t('order.cause')}}{{orderInfo.deliveryName}}{{orderInfo.deliveryMode === '1'?'':`${$t('order.delivery')}`}}</view>
					</view>
					<!-- 外送中 -->
					<view v-if="orderInfo.state === '4'" class="f30 c0 wei">
						<view>{{$t('order.expected')}}<text :style="{color:tColor}">{{orderInfo.serviceAt}}</text>{{$t('order.deliver')}}</view>
						<view class="f26 c6 mt10 pb20 b-b">{{$t('order.cause')}}{{orderInfo.deliveryName}}</view>
					</view>
					<!-- 订单完成 -->
					<view v-if="['5','6'].includes(orderInfo.state)" class="f30 c0 wei">
						<view>{{$t('order.complete_tip_text')}}{{orderInfo.storeName}}{{$t('order.complete_tip_text2')}}</view>
					</view>
					<!-- 申请退款 -->
					<view v-if="orderInfo.state === '9'" class="f30 c0 wei">
						<view>{{$t('order.refund_tip_text')}}</view>
					</view>
					<!-- <view v-else-if="['5','6'].includes(orderInfo.state)" class="f30 c0 wei">感谢您对平台的信任，期待下次光临</view> -->
					<!-- <view class="f26 c6 mt10 pb20 b-b">溫馨：请厉行节约，拒绝浪费。</view> -->
				</block>
				<!-- 自取 -->
				<block v-else>
					<!-- 尚未付款 -->
					<view v-if="orderInfo.state === '1'">
						<view class="f26 c6 mt10 pb20 b-b">{{$t('order.tips')}}：{{$t('order.exceed')}}{{orderSet.closeTime}}{{$t('order.tips_text')}}</view>
					</view>
					<!-- 外送中 -->
					<view v-if="orderInfo.state === '4'" class="f30 c0">
						<view class="wei">{{$t('order.pick_up_code')}}：<text style="color: #ef7800;">{{orderInfo.selfCode}}</text></view>
						<view class="f28 c3">{{$t('order.self_pickup_time')}}：{{orderInfo.serviceAt}}</view>
						<view class="f24 c9">{{$t('order.business_address')}}：{{orderInfo.address}}</view>
					</view>
					<!-- 订单完成 -->
					<view v-if="orderInfo.state === '5'" class="f30 c0 wei">
						<view>{{$t('order.complete_tip_text')}}{{orderInfo.storeName}}{{$t('order.complete_tip_text2')}}</view>
					</view>

					<!-- 申请退款 -->
					<view v-if="orderInfo.state === '9'" class="f30 c0 wei">
						<view>{{$t('order.cancelled_order_tip')}}</view>
						<view class="f28 c3">{{$t('order.refund_amount')}}：${{orderInfo.money}}</view>
					</view>
				</block>


				<view class="f-raw mt20" style="border-top: 1px solid #f5f6f9;">
					<view v-for="item in helpList" :key="item.text">
						<view
							v-if="(item.states.includes(orderInfo.state)&&(orderInfo.deliveryMode === '10'?item.show.includes(2):item.show.includes(1)))"
							class="f-c-c mt20 mr10" style="width: 120rpx;"
							@click="operation({data:item,type:item.btnType})">
							<block v-if="item.btnType=='applyRefund'">
								<block v-if="currency && currency.receivingRefund=='2' && ['3','4'].includes(orderInfo.state)">
								</block>
								<block v-else>
									<text class="iconfont f44" :class="[item.icon]" :style="item.style"></text>
									<view class="f24 wei mt10" :style="item.style">{{item.text}}</view>
								</block>
							</block>
							<block v-else-if="item.btnType=='evaluate'">
								<block v-if="currency.orderEvaluate == '1'">
									<text class="iconfont f44" :class="[item.icon]" :style="item.style"></text>
									<view class="f24 wei mt10" :style="item.style"
										v-if="currency.commentIntegralOpen!=1">{{$t('order.immediately_evaluate')}}</view>
									<view class="f24 wei mt10" :style="item.style" v-else>{{item.text}}</view>
								</block>
							</block>
							<block v-else-if="item.btnType=='message'">
								<block v-if="supPow.plug_ids.includes('chat')">
									<text class="iconfont f44" :class="[item.icon]" :style="item.style"></text>
									<view class="f24 wei mt10" :style="item.style">{{item.text}}</view>
								</block>
							</block>
							<block v-else>
								<text class="iconfont f44" :class="[item.icon]" :style="item.style"></text>
								<view class="f24 wei mt10" :style="item.style">{{item.text}}</view>
							</block>
						</view>
					</view>
				</view>
			</view>
			<!-- 自取 -->
			<!-- 		<view v-if="orderInfo.deliveryMode === '10'&& orderInfo.state === '4'" class="card">
				<view class="title-order">
					<view class="">订单核销</view>
				</view>
				<view class="f-c-c p30">
					<view class="p23 bf5 bs20">
						<text class="mr20">取貨號碼</text>
						<text style="letter-spacing:20rpx">{{orderInfo.selfCode}}</text>
					</view>
					<view style="width: 380rpx;height: 380rpx;">
						<image class="wh" :src="QRcode" mode=""></image>
					</view>
					<view class="c6 f26 t-c">{{orderSet.tips}}</view>
				</view>
			</view> -->
			<!-- 商品 -->
			<view class="card">
				<view class="f-y-c title-order">
					<view class="t-o-e" style="max-width: 500rpx;" @click="go('navigateTo', `/yb_o2ov2/my/supplier/store?storeId=${orderInfo.storeId}&type=1`)">{{orderInfo.storeName}}</view>
				</view>
				<view class="p20 b-b">
					<view v-if="togetherId>0">
						<view v-for="(user,index) in orderInfo.togetherUser" :key="index">
							<view class="f-x-bt mt20 outer3">
								<view class="flex">
									<view class="mr20 bs60" style="width: 45rpx;height: 45rpx;">
										<image class="wh" :src="user.portrait" mode=""></image>
									</view>
									<view class="mr20 ">
										<text class="c6 f24">{{user.userName}} {{user.id==uId?'(我)':''}}</text>
										(<text class="c3 f24">小计</text><text
											style="color: #FE624B;">${{sumTotal(user.id)}}</text>)
									</view>
								</view>
							</view>
							<view v-for="(item) in getCarList(user.id)" :key="item.id" class="f-y-c mt20 p-r">
								<view class="mr20 bs10" style="width: 100rpx;height: 100rpx;">
									<image class="wh" :src="item.icon" mode="aspectFill"></image>
								</view>
								<view class="f-c-s f-g-1">
									<view style="min-height: 56rpx;" class="flex">
										<view v-if="item.isActivity>0&&item.isActivity!=4"
											class="f-g-0 yhic f18 cf bf5f f-c hdspan mr10" style="background:  #ff474a">
											{{item.isActivity==1?'特':item.isActivity==2?'折':item.isActivity==5?'赠':'减'}}
										</view>
										<text class="f28">{{item.name}}</text>
									</view>
									<view v-if="item.isChange==1" class="p-a hgc f18 cf bf5f2 l-n">换购</view>
									<view v-if="item.isActivity==4" class="f-row cf5f">
										<view class="f20 bs5 b-d p-3-10" :style="{borderColor:'#FF5F2F'}">
											{{item.isActivity==4?'第二件打折':'买一送一'}}
										</view>
									</view>
									<view class="f24 c9" style="min-height: 24rpx;">
										<text v-if="item.data">{{item.data}}</text>
										<text v-if="item.data&&(item.attribute||item.material)">,</text>
										<text v-if="item.attribute">{{item.attribute}}</text>
										<text v-if="item.attribute&&item.material">,</text>
										<text v-if="item.material">{{item.material}}</text>
									</view>
									<view class="f24 c9">x{{item.num}}</view>
								</view>
								<view style="flex-shrink: 0;">
									<text class="t-d-l c6 f26"
										v-if="item.totalOriginalMoney && item.totalOriginalMoney!=item.money">${{item.totalOriginalMoney}}</text>
									<text class="f32"><text class="f24">$</text>{{item.money}}</text>
								</view>
							</view>
						</view>
					</view>
					<view v-else>
						<view v-for="(item) in orderInfo.goodsArr" :key="item.id" class="f-y-c mt20 p-r">
							<view class="mr20 bs10" style="width: 100rpx;height: 100rpx;">
								<image class="wh" :src="item.icon" mode="aspectFill"></image>
							</view>
							<view class="f-c-s f-g-1">
								<view style="min-height: 56rpx;" class="flex">
									<!-- <text class="bs10 f24 wei cf"
										style="background:  #ff474a;padding: 0 6rpx;margin-right: 5rpx;">新</text> -->
									<view v-if="item.isActivity>0&&item.isActivity!=4"
										class="f-g-0 yhic f18 cf bf5f f-c hdspan mr10" style="background:  #ff474a">
										{{item.isActivity==1?'特':item.isActivity==2?'折':item.isActivity==5?'赠':'减'}}
									</view>
									<text class="f28">{{item.name}}</text>
								</view>
								<view v-if="item.isChange==1" class="p-a hgc f18 cf bf5f2 l-n">换购</view>
								<view v-if="item.isActivity==4" class="f-row cf5f">
									<view class="f20 bs5 b-d p-3-10" :style="{borderColor:'#FF5F2F'}">
										{{item.isActivity==4?'第二件打折':'买一送一'}}
									</view>
								</view>

								<!-- 							<view v-if="v.isChange==1" class="p-a hgc f18 cf bf5f l-n">换购</view>
								<view class="f-bt">
									<view class="f-row">
										<view v-if="v.isActivity>0&&v.isActivity!=4" class="f-g-0 yhic f18 cf bf5f f-c hdspan">
											{{v.isActivity==1?'特':v.isActivity==2?'折':v.isActivity==5?'赠':'减'}}
										</view>
										<view class="l-h1">{{v.name}}</view>
									</view>
									<view class="f-g-0 f32">{{sl+itemTotal(v)}}</view>
								</view>
								<view v-if="v.isActivity==4" class="f-row cf5f">
									<view class="f20 bs5 b-d p-3-10" :style="{borderColor:'#FF5F2F'}">
										{{v.isActivity==4?'第二件打折':'买一送一'}}
									</view>
								</view> -->

								<!-- <view class="f24 c6">1人份+默认</view> -->
								<view class="f24 c9" style="min-height: 24rpx;">
									<text v-if="item.data">{{item.data}}</text>
									<text v-if="item.data&&(item.attribute||item.material)">,</text>
									<text v-if="item.attribute">{{item.attribute}}</text>
									<text v-if="item.attribute&&item.material">,</text>
									<text v-if="item.material">{{item.material}}</text>
								</view>
								<view class="f24 c9">x{{item.num}}</view>
							</view>
							<view style="flex-shrink: 0;">
								<text class="t-d-l c6 f26"
									v-if="item.totalOriginalMoney && item.totalOriginalMoney!=item.money">${{item.totalOriginalMoney}}</text>
								<text class="f32"><text class="f24">$</text>{{item.money}}</text>
							</view>
						</view>
					</view>
				</view>
				<view class="pb20 b-b">
					<view class="f-x-bt mt20" v-if="orderInfo.vipDiscount && orderInfo.vipDiscount>0">
						<view class="mr10 f-g-1 f-y-c">
							<view class="yhic f22 cf f-c f-g-0" style="background: #EDA555;">V</view>
							<view class="f26">会员优惠</view>
						</view>
						<view class="f32 cf7">
							<view class="f-s-0"><text class="f24">$</text>{{orderInfo.vipDiscount}}</view>
						</view>
					</view>
					<!-- <view class="f-x-bt f20 mt20" v-if="submitInfo.hyzk">
						<view class="mr10 f-g-1 f-y-c">
							<view class="yhic f22 cf f-c f-g-0" style="background: #EDA555;">V</view>
							<view class="f26">会员优惠</view>
						</view>
						<view class="f32 cf7">
							<view class="f-s-0">{{sl+submitInfo.hyzk}}</view>
						</view>
					</view> -->
					<!-- 包装费 -->
					<view class="f-x-bt mt20">
						<view>{{$t('good.packing_charge')}}</view>
						<view class="f32"><text class="f24">$</text>{{orderInfo.boxMoney}}</view>
					</view>
					<!-- 外送费 -->
					<!-- <view class="f-x-bt mt20" v-if="orderInfo.deliveryMode !== '10'">
						<view>{{$t('good.delivery_fee')}}</view>
						<view class="">
							<text class="t-d-l c9 nowei"
								v-if="orderInfo.oldDeliveryMoney && orderInfo.oldDeliveryMoney!=orderInfo.deliveryMoney">${{orderInfo.oldDeliveryMoney}}</text>
							<text class="f32"><text class="f24">$</text>{{orderInfo.deliveryMoney}}</text>
						</view>
					</view> -->
				</view>
				<view class="pb20 b-b">
					<!-- 平台新客立减 -->
					<view class="f-x-bt mt20" v-if="Number(orderInfo.platformNewMoney)!==0">
						<view class="f-y-c" style="align-items: baseline;">
							<text class="bs10 f24 wei cf"
								style="background:  #ff474a;padding: 0 6rpx;margin-right: 5rpx;">新</text>
							<text>平台新客立减</text>
						</view>
						<view class="" style="color: #FA3534;">-${{orderInfo.platformNewMoney}}</view>
					</view>
					<!-- 门店新客立减 -->
					<view class="f-x-bt mt20" v-if="Number(orderInfo.newMoney)!==0">
						<view class="f-y-c" style="align-items: baseline;">
							<text class="bs10 f24 wei cf"
								style="background:  #ff474a;padding: 0 6rpx;margin-right: 5rpx;">新</text>
							<text>门店新客立减</text>
						</view>
						<view class="" style="color: #FA3534;">-${{orderInfo.newMoney}}</view>
					</view>
					<!-- 满额立减 -->
					<view class="f-x-bt mt20" v-if="Number(orderInfo.preferentialMoney)!==0">
						<view class="f-y-c" style="align-items: baseline;">
							<text class="bs10 f24 wei cf"
								style="background:  #ff474a;padding: 0 6rpx;margin-right: 5rpx;">满</text>
							<text>满额立减</text>
						</view>
						<view class="" style="color: #FA3534;">-${{orderInfo.preferentialMoney}}</view>
					</view>
					<view class="f-x-bt mt20" v-if="orderInfo.giveName">
						<view class="f-y-c" style="align-items: baseline;">
							<text class="bs10 f24 wei cf"
								style="background:  #ff474a;padding: 0 6rpx;margin-right: 5rpx;">赠</text>
							<text>满赠</text>
						</view>
						<view class="" style="color: #FA3534;">{{orderInfo.giveName}}</view>
					</view>
					<!-- 商店代金券 -->
					<view class="f-x-bt mt20" v-if="Number(orderInfo.couponPreferential)!==0">
						<view class="f-y-c" style="align-items: baseline;">
							<text class="bs10 f24 wei cf"
								style="background:  #ff474a;padding: 0 6rpx;margin-right: 5rpx;">券</text>
							<text>商店代金券</text>
						</view>
						<view class="" style="color: #FA3534;">-${{orderInfo.couponPreferential}}</view>
					</view>
					<!-- 平台红包 -->
					<view class="f-x-bt mt20" v-if="Number(orderInfo.platformCouponPreferential)!==0">
						<view class="f-y-c" style="align-items: baseline;">
							<text class="bs10 f24 wei cf"
								style="background:  #ff474a;padding: 0 6rpx;margin-right: 5rpx;">红</text>
							<!-- <text>{{system.custom.systemRedbag || '平台红包'}}</text> -->
						</view>
						<view class="" style="color: #FA3534;">-${{orderInfo.platformCouponPreferential}}</view>
					</view>
					<view class="f-x-bt mt20" v-if="Number(orderInfo.deliveryCouponMoney)!==0">
						<view class="f-y-c" style="align-items: baseline;">
							<text class="bs10 f24 wei cf"
								style="background:  #ff474a;padding: 0 6rpx;margin-right: 5rpx;">红</text>
							<text>外送红包</text>
						</view>
						<view class="" style="color: #FA3534;">-${{orderInfo.deliveryCouponMoney}}</view>
					</view>
					<!-- 平台红包 -->
					<!-- 	<view class="f-x-bt mt20">
						<view class="f-y-c" style="align-items: baseline;">
							<text class="bs10 f24 wei cf"
								style="background:  #ff474a;padding: 0 6rpx;margin-right: 5rpx;">红</text>
							<text>平台红包</text>
						</view>
						<view class="" style="color: #FA3534;">-$3</view>
					</view> -->
				</view>
				<!-- 合计 -->
				<view class="t-r p20 f28">
					<!-- 		<text>已优惠</text>
					<text class="mr10" style="color: #FA3534;">$11</text> -->
					<text class="mr10">{{$t('order.amount_to')}}</text>
					<text class="wei f36">${{orderInfo.money}}</text>
				</view>
				<!-- <view class="flex t-c p2">
					<view class="w50 f-c" style="border-right: 1px solid #ddd;" @click="openNavigation">
						<text class="iconfont iconcontact f30 mr10"></text>
						<text class="wei">导航商店</text>
					</view>
					<view class="w50 f-c" @click="call(orderInfo.storeTel)">
						<text class="iconfont iconcallmerchant f30 mr10"></text>
						<text class="wei">致电商店</text>
					</view>
				</view> -->
			</view>
			<!-- 外送信息 -->
			<view v-if="orderInfo.deliveryMode !== '10'" class="card">
				<view class="title-order">
					<view class="">{{$t('order.shipping_info')}}</view>
				</view>
				<view class="f-x-bt mt20">
					<view class="c6 f-s-0">{{$t('order.expected_time')}}</view>
					<view class="">{{$t('order.immediate_delivery')}}</view>
				</view>
				<view class="f-x-bt mt20">
					<view class="c6 f-s-0">{{$t('order.shipping_address')}}</view>
					<view class="">
						<view>{{orderInfo.receivedAddress}}</view>
						<view>{{orderInfo.receivedName}}（先生）{{orderInfo.receivedTel}}</view>
					</view>
				</view>
				<view class="f-x-bt mt20">
					<view class="c6 f-s-0">{{$t('order.distribution_service')}}</view>
					<view class="">{{orderInfo.deliveryName}}</view>
				</view>
				<block v-if="['4','5'].includes(orderInfo.state)">
					<view class="f-x-bt mt20">
						<view class="c6 f-s-0">外送骑手</view>
						<!-- 商店外送 -->
						<view v-if="orderInfo.deliveryMode ==='1'">由商店自行外送</view>
						<!-- 第三方外送 -->
						<view v-else class="">{{orderInfo.deliveryInfo.riderName}}</view>
					</view>
					<!-- <view class="flex t-c mt20">
						<view class="w50 f-c" style="border-right: 1px solid #ddd;"
							@click="call(orderInfo.deliveryMode==='1'?orderInfo.storeTel:orderInfo.deliveryInfo.riderTel)">
							<text class="iconfont iconcontact f30 mr10"></text>
							<text class="wei">联系骑手</text>
						</view>
						<view class="w50 f-c"
							@click="call(orderInfo.deliveryMode==='1'?orderInfo.storeTel:orderInfo.deliveryInfo.riderTel)">
							<text class="iconfont iconcallmerchant f30 mr10"></text>
							<text class="wei">致电骑手</text>
						</view>
					</view> -->
				</block>
			</view>
			<!-- 自取信息 -->
			<view v-else class="card">
				<view class="title-order">
					<view class="">{{$t('order.self_selected_info')}}</view>
				</view>
				<view class="f-x-bt mt20">
					<view class="c6 f-s-0">{{$t('order.self_pickup_time')}}</view>
					<view class="">{{orderTime(orderInfo.serviceAt) }}</view>
				</view>
				<view class="f-x-bt mt20" v-if="orderInfo.isOut">
					<view class="c6 f-s-0">{{$t('order.self_selected_type')}}</view>
					<view class="">
						{{orderInfo.isOut=='1'? `${$t('good.in_store_dining')}`:`${$t('good.pack_and_take_away')}`}}
					</view>
				</view>
				<view class="f-x-bt mt20">
					<view class="c6 f-s-0">{{$t('order.business_address')}}</view>
					<view class="t-r">{{orderInfo.address}}</view>
				</view>
				<view class="f-x-bt mt20">
					<view class="c6 f-s-0">{{$t('good.reserved_phone_number')}}</view>
					<view class="">{{orderInfo.receivedTel}}</view>
				</view>
			</view>
			<!-- 订单信息 -->
			<view class="card">
				<view class="title-order">
					<view class="">{{$t('order.order_info')}}</view>
				</view>
				<view class="f-x-bt mt20">
					<view class="c6 f-s-0">{{$t('order.order_number')}}</view>
					<view class="">{{orderInfo.outTradeNo}}</view>
				</view>
				<view class="f-x-bt mt20">
					<view class="c6 f-s-0">{{$t('order.order_time')}}</view>
					<view class="">{{orderTime(orderInfo.createdAt)}}</view>
				</view>
				<view class="f-x-bt mt20">
					<view class="c6 f-s-0">{{$t('order.payment_method')}}</view>
					<view v-if="orderInfo.payMode !== null" class="">{{getPayMode(orderInfo.payMode)}}</view>
					<view v-else>{{$t('order.unpaid')}}</view>
				</view>
				<view class="f-x-bt mt20" v-if="orderInfo.payMode === '5'">
					<view class="c6 f-s-0">{{$t('order.atm_bank')}}</view>
					<view>{{ orderInfo.atm_setting.atm_bank }}</view>
				</view>
				<view class="f-x-bt mt20" v-if="orderInfo.payMode === '5'">
					<view class="c6 f-s-0">{{$t('order.atm_account')}}</view>
					<view>{{ orderInfo.atm_setting.atm_code }}</view>
				</view>
				<view class="f-x-bt mt20" v-if="orderInfo.payMode === '5'">
					<view class="c6 f-s-0">{{$t('order.atm_name')}}</view>
					<view>{{ orderInfo.atm_setting.atm_name }}</view>
				</view>
			</view>
			<!-- 到底了 -->
			<view class="c9 w100 t-c f24 mt20" style="padding-bottom: 40rpx;">{{$t('order.over_now')}}~</view>
		</scroll-view>
		<!-- 订单追踪 -->
		<u-popup v-model="showTrack" mode="bottom" closeable :height="popupHeight" border-radius="20">
			<view class="p-r">
				<view class="f32 wei t-c bf posi-s" style="top:0;height: 100rpx;line-height: 100rpx;">订单追踪</view>
				<scroll-view scroll-y="true" :style="{height:`${popupHeight-100}rpx`}">
					<view class="f-col p04" style="padding-bottom: 120rpx;">
						<u-time-line>
							<u-time-line-item v-for="(item,index) in 7" :key="index"
								:bg-color="item===6?'#fff70a':'#ddd'">
								<!-- 此处没有自定义左边的内容，会默认显示一个点 -->
								<template v-slot:content>
									<view class="f-x-bt w100 c9 f24">
										<view>订单已提交</view>
										<view>7月30日 11:28</view>
									</view>
								</template>
							</u-time-line-item>
						</u-time-line>
					</view>
				</scroll-view>
			</view>
		</u-popup>
		<Load :show="showLoad"></Load>
		<mg-popup v-model="showRed" position="middle">
			<view class="bf t-c bs15" style="width: 540rpx;">
				<view class="f-c f-1 mt20">
					<view class="qhbimg pt30">
						<mg-img :src='`${onImgurl}gfhb/qhb.png`'></mg-img>
					</view>
				</view>
				<view class="m30">
					<p class='c0 wei f30'>【送您一个拼手气红包】</p>
					<p class="c6 f26 mt10">快分享给小伙伴看看谁会撞大运</p>
				</view>
				<view class="f-row hlt f-x-bt" style="height: 100rpx;">
					<button hover-class='be' class="bs0 p0 f-1 bf f30 h100 f-c c9" @click="showRed=false">取消</button>
					<sq-btn cname="f-1 bf f30 h100 f-c hll" w='270' h='100' :sname="{color:tColor}" t='发红包' type="3"
						@refresh='fhb'></sq-btn>
				</view>
			</view>
		</mg-popup>
	</view>
</template>

<script>
	import {
		mapState,
		mapActions
	} from 'vuex'
	import utils from '@/common/utils.js'
	import mgImg from '@/components/common/mg-img.vue'
	import MgCell from '@/components/common/mg-cell.vue'
	import mgPopup from '@/components/common/popup.vue'
	import sqBtn from '@/components/common/sq-btn.vue'
	import {
		wxShare,
		getSLink,
	} from "@/common/wechat-util.js"
	export default {
		components: {
			mgImg,
			MgCell,
			mgPopup,
			sqBtn,
		},
		data() {
			return {
				showLoad: true,
				scrollHeight: '',
				showTrack: false,
				// stateArr: ['', '等待付款', '下单成功，等待商店接单', '商店已接单', '订单外送中', '订单已完成', '已评价', '订单已取消', '商店已拒单', '退款中，等待商店处理',
				// 	'退款已通过',
				// 	'退款失敗'
				// ], //外卖state对应
				// ztstateArr: ['', '等待付款', '下单成功，等待商店接单', '商店已接单，准备中', '待取货', '订单已完成', '已评价', '订单已取消', '商店已拒单', '退款中，等待商店处理',
				// 	'订单已取消',
				// 	'退款失敗'
				// ], //自取state对应
				stateArr: this.$t('order.stateArr'),
				ztstateArr: this.$t('order.ztStateArr'),
				stateText: '',
				//1尚未付款,2已付款,3已接单,4外送中/待取货,5已收货,6已评价,7已取消,8.拒单，9申请退款,10已退款，11退款拒绝
				helpList: [
					// show 是否在外卖自取订单中显示  1外卖 2自取
					{
						text: this.$t('order.pay_now'),
						icon: 'iconevaluationget',
						style: 'color:#ef7800',
						states: ['1'],
						btnType: 'payTo',
						show: [1, 2]
					},
					// { text: '联系商店', icon: 'iconcontact', const: false,states: ['2','3','4','5','6','8','11','10','9'],btnType:'message',show:[1,2] }, 
					{
						text: this.$t('order.order_again'),
						icon: 'iconagainorder',
						states: ['5'],
						btnType: 'againOrder',
						show: [1, 2]
					},
					// {
					// 	text: '致电商店',
					// 	icon: 'iconcallmerchant',
					// 	const: true,
					// 	states: ['2', '3', '4', '5', '6', '8', '7', '11', '10', '9'],
					// 	btnType: 'contactStore',
					// 	show: [1, 2]
					// },
					// {
					// 	text: '评价得金豆',
					// 	icon: 'iconevaluationget',
					// 	style: 'color:#ef7800',
					// 	states: ['5'],
					// 	btnType: 'evaluate',
					// 	show: [1, 2]
					// },
					// {
					// 	text: this.$t('order.reminder'),
					// 	icon: 'iconevaluationget',
					// 	states: ['3', '4'],
					// 	btnType: 'reminder',
					// 	show: [1]
					// },
					{
						text: this.$t('order.complete_order'),
						icon: 'iconagainorder',
						states: ['4'],
						btnType: 'confirmReceipt',
						show: [1]
					},
					{
						text: this.$t('order.cancel_order'),
						icon: 'iconcancelorder',
						states: ['1', '2', '3', '4'],
						btnType: 'applyRefund',
						show: [1, 2]
					},
					{
						text: this.$t('order.confirm_receipt'),
						icon: 'iconselect',
						states: ['4'],
						btnType: 'confirmReceipt',
						show: [2]
					},
				],
				stime: '',
				orderInfo: {},
				type: '',
				QRcode: '',
				supPow: {},
				showRed: false,
				showRed: false,
				hbId: '',
				togetherId: 0,
				options: {}
			}
		},
		computed: {
			...mapState({
				orderSet: state => state.config.orderSet,
				currency: state => state.config.currency,
			}),
			popupHeight() {
				return `${this.wHeight / 2 * this.pxToRpxRate / 1.5}`
			},
			selfList() { //自己的商品
				return this.orderInfo.goodsArr !== undefined ? this.orderInfo.goodsArr.filter((item) => item.userId == this
					.user
					.id) : []
			},
			otherList() {
				return this.orderInfo.goodsArr !== undefined ? this.orderInfo.goodsArr.filter((item) => item.userId != this
					.user
					.id) : []
			},
			getTogetherUser() {
				return this.orderInfo.togetherUser !== undefined ? this.orderInfo.togetherUser.filter((item) => item.id !=
					this.user.id) : []
			},
			selfUser() {
				return this.orderInfo.togetherUser !== undefined ? this.orderInfo.togetherUser.filter((item) => item.id ==
					this.user.id)[0] : {
					type: 0
				}
			},
		},
		async onLoad(option) {
			this.payObj = {
				orderId: option.orderId,
				orderType: option.orderType
			}
			this.options = {
				payType: option.payType
			}
			this.gsp()
			if (option.orderId) {
				this.GetPxToRpxRate()
				await this.getConfig({
					name: 'orderSet',
					api: this.api.orderSet,
				})
				await this.getLocInfo()
				await this.getConfig({
					name: 'currency',
					api: this.api.config,
					data: {
						ident: 'currency'
					}
				})
			}
		},
		onShow() {
			this.getOrderInfo()
		},
		// onUnload() {
		// 	//更新订单列表
		// 	getCurrentPages()[0].$vm.$refs.initOrder.init()
		// },
		// onReady() {
		// 	this.getElement()
		// },

		methods: {
			...mapActions(["setSystemInfo", 'getConfig']),
			sumTotal(id) {
				let goods = this.getCarList(id)
				let total = goods.reduce((pre, cur) => {
					return utils.accAdd(pre, utils.accMul(cur.money, cur.num))
				}, 0)
				//计算总优惠
				let discount = utils.accSub(this.orderInfo.originMoney, this.orderInfo.money)
				//计算所占比例
				let scale = utils.accDiv(total, this.orderInfo.originMoney)
				//计算自己的优惠
				let selfDiscount = utils.accMul(scale, discount)

				return utils.nsswr(utils.accSub(total, selfDiscount))

			},
			getCarList(id) {
				return this.orderInfo.goodsArr.filter((item) => item.userId == id)
			},
			openNavigation() {
				this.util.ckWz({
					lat: this.orderInfo.stortLat,
					lng: this.orderInfo.storeLng,
					name: this.orderInfo.storeName,
					address: this.orderInfo.address,
					icon: this.orderInfo.storeIcon
				})
			},
			async gsp() {
				let {
					data
				} = await this.util.request({
					'url': this.api.gsp,
					mask: '載入中',
					data: {
						// id: this.payObj.orderId
					},
				})
				this.supPow = data
				console.log(123, data)
			},
			async getQRcode() {
				let {
					data
				} = await this.util.request({
					'url': this.api.zqqcm,
					mask: '載入中',
					data: {
						id: this.payObj.orderId
					},
				})
				this.QRcode = data
			},
			getPayMode(payMode) {
				// 1微信付款2付款宝3.现金付款,5余额付款10货到付款
				switch (payMode) {
					case '1':
						return this.$t('pay.line_pay')
					case '2':
						return this.$t('pay.street_pay')
					case '3':
						return this.$t('pay.pay_Cash')
					case '4':
						return this.$t('pay.pay_text')
					case '5':
						return this.$t('pay.pay_ATM')
					// case '5':
					// 	return '余额付款'
					// case '10':
					// 	return '货到付款'
				}
			},
			getState() {
				let item = this.orderInfo
				if (item.deliveryMode != 10) {
					this.stateText = this.stateArr[+item.state]
				} else {
					this.stateText = this.ztstateArr[+item.state]
				}
			},
			orderTime(time) {
				let result = utils.timeToDate(time)
				return result
			},
			setTime(time) {
				let now = utils.dateToTime(),
					time2 = +time + this.orderSet.closeTime * 60
				if (time2 > now) {
					let first = utils.countDownTime(time2 - now)
					this.stime = `${first[2]}:${first[3]}`
					this.setIntervalTime = setInterval(() => {
						time2 -= 1
						if (time2 == now) {
							clearInterval(this.setIntervalTime)
							this.go('back')
						}
						let temArr = utils.countDownTime(time2 - now)
						this.stime = `${temArr[2]}:${temArr[3]}`
					}, 1000)
				} else {
					this.stime = ''
				}
			},
			setCloseTime(closeTime) {
				let now = utils.dateToTime()
			},
			async getOrderInfo() {
				let {
					data
				} = await this.util.request({
					'url': this.api.supplierOrderInfo,
					mask: 1,
					data: {
						orderId: this.payObj.orderId
					},
				})
				this.orderInfo = data
				if (data) {
					this.getisPop()
				}
				this.showLoad = false
				this.setTime(data.createdAt)
				this.getState()
				this.togetherId = data.togetherId
				if ((data.state == 3 || data.state == 4) && data.deliveryMode == 10) {
					this.getQRcode()
				}
			},
			// async integralAdd() {
			// 	let {data} = await this.util.request({
			// 		'url': this.api.zfyl,
			// 		data: this.payObj
			// 	})
			// 	this.ylInfo = data
			// },
			getElement() {
				let query = uni.createSelectorQuery().in(this);
				query.select('.nav').boundingClientRect((res) => {
					this.scrollHeight = this.wHeight - res.bottom
				}).exec()

			},
			call(tel) {
				uni.makePhoneCall({
					phoneNumber: tel //仅为示例
				});
			},
			// 轮询订单状态
			async pollOrderStatus() {
				clearInterval(this.pollInterval)
				this.pollInterval = setTimeout(async () => {
					let data = await this.util.request({
						'url': this.api.linePayQuery,
						method:'POST',
						data: {
							orderId: this.orderInfo.id,
							storeType:'supplier'		
						},
					})
					console.log(data);
					
					if (data.code === 1) {
						clearInterval(this.pollInterval)
						this.getOrderInfo();
						console.log('订单支付成功', data)
						// 可以在这里添加其他处理逻辑
					}
				}, 1000)
			},
			async operation(e) {
				let operationt = e.type
				let obj = {}
				if(e.type === 'payTo' && this.options.payType === 'linepay'){
					const data = await this.util.request({
						url: this.api.linePayConfirm,
						method:'POST',
						data:{
							orderId:this.orderInfo.id,
							storeType:'supplier'
						}
					})
					if(data.code === 1){
						// 轮询订单状态
						this.pollOrderStatus()
					}
					return
				}
				switch (operationt) {
					// case 'cancelOrder':
					// 	if (e.tip) {
					// 		return this.qxdd(1)
					// 		this.yyradio = ''
					// 		this.yyArr = this.qxyyArr
					// 		this.showCancel = true
					// 		return;
					// 	} else {
					// 		return this.qxdd()
					// 	}
					case 'applyRefund':
						if (this.orderInfo.state == 2 || this.orderInfo.state == 1) { //已付款未接单  尚未付款
							return this.qxdd(1)
						}
						obj = {
							title: this.$t('good.process_tip'),
							url:this.orderInfo.state == 2 ?'supplierCancel':'supplierrefund',
							params: {
								orderId: this.orderInfo.id,
								note: this.qxyy || '',
							}
						}
						break;
					case 'reminder':
						obj = {
							title: this.$t('good.follow_tip'),
							url: 'supplierWmddcd',
							params: {
								// userId: this.user.userId,
								orderId: this.orderInfo.id
							}
						}
						break;
					case 'confirmReceipt':
						obj = {
							title: this.$t('good.received_tip'),
							url: 'supplierWmddsh',
							params: {
								orderId: this.orderInfo.id
							}
						}
						break;
					case 'contactStore':
						return this.util.makeTel(this.orderInfo.storeTel)
					case 'againOrder':
						this.go('navigateTo', `/yb_o2ov2/my/supplier/store?storeId=${this.orderInfo.storeId}`)
						break;
					// case 'evaluate':
					// 	return this.go('navigateTo',
					// 		`/yb_o2ov2/order/edit-comment?orderId=${this.orderInfo.id}&storeName=${this.orderInfo.storeName}`
					// 	)
					case 'payTo':
						let data = {}
						data.orderId = this.orderInfo.id
						data.orderType = 1,
							//付款前需要保存付款信息传给付款页面
							this.setPayInfo(data)
						this.go('navigateTo', '/yb_o2ov2/my/pay/index')
						return;
					// case 'message':
					// 	this.go('navigateTo', `/yb_o2ov2/my/message/chat?storeId=${this.orderInfo.storeId}`)
					// 	return

				}
				try {
					await this.util.modal(obj.title)
				} catch (e) {
					return
				}
				let data = await this.util.request({
					'url': this.api[obj.url],
					method: 'POST',
					mask: 1,
					data: obj.params,
				})
				if (data) {
					this.getOrderInfo();
					this.util.message('操作成功', 1)
				}
			},
			async qxdd(e) {
				if (e) {
					try {
						await this.util.modal(this.$t('good.process_tip'))
					} catch (e) {
						return
					}
				}
				let data = await this.util.request({
					'url': this.api.supplierCancel,
					method: 'POST',
					mask: '取消订单中',
					data: {
						orderId: this.orderInfo.id,
						note: e ? this.qxyy || '' : '',
					},
				})
				if (data) {
					this.getOrderInfo();
					this.util.message('操作成功', 1)
				}
			},
			async getisPop() {
				let {
					data
				} = await this.util.request({
					'url': this.api.ispop,
					data: {
						orderId: this.orderInfo.id,
					}
				})
				this.hbId = data ? data.id : ''
				this.config = data
				// #ifdef  H5
				let link = getSLink(`yb_o2ov2/shop/gfhb/gfhb?id=${this.hbId}&userId=${this.uId}`)
				wxShare({
					title: this.config.shareTitle,
					desc: '送你一个拼手气红包!',
					link,
					imgUrl: this.getImgS(this.config.shareImg),
				})
				// #endif
			},
			showhb() {
				this.showRed = true
			},
			fhb() {
				this.showRed = false
			},
			async GetPxToRpxRate() {
				if (!this.pxToRpxRate) {
					await this.setSystemInfo()
				}
			},
		},
		onShareAppMessage() {
			let p = `yb_o2ov2/shop/gfhb/gfhb?id=${this.hbId}&userId=${this.uId}`
			return this.util.mpShare({
				t: this.config.shareTitle,
				i: this.getImgS(this.config.shareImg),
				p,
			})
		},
	}
</script>

<style lang="scss" scoped>
	.order-info-navigation-bar {
		display: flex;
		height: 80rpx;
		justify-content: flex-start;
		align-items: center;
	}

	.card {
		width: 700rpx;
		margin: 0 auto;
		margin-top: 20rpx;
		border-radius: 20rpx;
		background: #FFFFFF;
		padding: 10rpx 20rpx;
		font-size: 28rpx;
		color: #000000;
	}

	.b-b {
		border-bottom: 1px solid #fafbfd;
	}

	.title-order {
		padding: 10rpx 10rpx 10rpx 0;
		border-bottom: 1px solid #fafbfd;

		view {
			font-size: 32rpx;
			font-weight: bold;
		}

		text {}
	}

	.hgc {
		top: 10rpx;
		left: 10rpx;
		padding: 3rpx 12rpx;
		border-radius: 0 0 15rpx 0;
	}

	.hbtb {
		position: fixed;
		top: 300rpx;
		right: 20rpx;
		z-index: 999;
	}

	.qhbimg {
		width: 357rpx;
		height: 266rpx;
	}

	.hbimg {
		width: 50rpx;
		height: 60rpx;
	}

	.hgc {
		top: 30rpx;
		left: 30rpx;
		padding: 3rpx 12rpx;
		border-radius: 0 0 15rpx 0;
	}

	.hdspan {
		margin-top: 8rpx;
	}

	.outer3 {
		background-color: #f7f7f7;
		border-radius: 10rpx;
		height: 50rpx;
	}
</style>