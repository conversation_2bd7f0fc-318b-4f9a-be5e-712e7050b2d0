<template>
	<view>
		<!-- 头部 start -->
		<view class="p-a t0 w100 " :style="{top:0,height:`${statusNavBarHeight}px`,paddingTop:`${menuButtonTop}px`,zIndex:3,background:'#fff'}">
			<!-- 返回 -->
			<view class="p-a" :style="{top:`${menuButtonTop}px`,left:`20rpx`,zIndex:4}" @click="go('back')">
				<text class="iconfont iconback f40"></text>
			</view>
			<view class="t-c f32 wei">
				<view>直播列表</view>
			</view>
		</view>
		<!-- 头部 end -->
		<scroll-view scroll-y="true" class="bf5 h100v pb30"
			refresher-enabled refresher-default-style="none"
			:refresher-threshold="100/pxToRpxRate" :refresher-triggered="refreshLoading" @refresherrefresh="refresh"
			@scrolltolower="nextPage"
			:style="{paddingTop:`${statusNavBarHeight}px`}">
			<!-- 下拉 -->
			<view class="p-a p-a-xc" style="top: -100rpx;">
				<loading-icon></loading-icon>
			</view>
			<view class="f-x-bt f-w p2">
				<view @click="goDl(v)" v-for="(v,i) in dataList" :key='v.id' class="p-r b-s-3 bs15 mb20" style="width: 345rpx;">
					<view class="bs15 p-r" style="height: 345rpx;">
						<image class="wh" :src="v.cover_img"></image>
						<view class="p-a p-a-c f-c zban">
							<text class="iconfont icontplay cf f56 ml10"></text>
						</view>
					</view>
					<view class="p23 bf">
						<view class="t-o-e l-n">{{v.name}}</view>
						<view class="f-y-c mt10">
							<view class="bsf mr10" style="width: 46rpx;height: 46rpx;">
								<image class="wh" :src="v.cover_img"></image>
							</view>
							<view class="f24 c9 f-1 t-o-e">{{v.anchor_name}}</view>
						</view>
					</view>
					<view class="p-a l0 t0 zbzt f-y-c cf f24">
						<view class="yd bsf bf mr10"></view>
						<view>{{ls(v)}}</view>
					</view>
				</view>
			</view>
			<u-loadmore v-if="list.length!==0" @loadmore="nextPage" :status="status" />
		</scroll-view>
	</view>
</template>

<script>
	import LoadingIcon from '@/components/LoadingIcon.vue'
	export default {
		components: {
			LoadingIcon
		},
		data(){
			return{
				params: {
					page: 1,
					size: 10,
				},
				dataList:[],
				refreshLoading:false,
				status:'loading',
			}
		},
		created(){
			this.init()
		},
		methods: {
			async init() {
				this.params.page = 1
				this.fetchData()
			},
			async refresh() {
				this.refreshLoading = true
				this.params.page = 1
				await this.fetchData()
				setTimeout(()=>{
					this.refreshLoading = false
				},500)
			},
			async fetchData(type) {
				this.status = 'loading'
				let {
					data
				} = await this.util.request({
					'url': this.api.zblb,
					method: 'POST',
					data: this.params,
				})
				if(type !=='nextPage' || this.params.page === 1){
					this.dataList = data
				}else{
					if(data.length === 0){
						this.status = 'nomore'
						return
					}
					this.dataList = this.dataList.concat(data)
				}
				this.status = 'loadmore'
			},
			nextPage() {
				if (this.status !== 'loadmore') return 
				this.params.page++
				this.fetchData('nextPage')
			},
			goDl(v) {
				uni.navigateTo({
					url: `plugin-private://wx2b03c6e691cd7370/pages/live-player-plugin?room_id=${v.roomid}`
				})
			},
			ls(v) {
				let t = ''
				switch (v.live_status) {
					case '101':
						t = '直播中'
						break;
					case '102':
						t = '未开始'
						break;
					case '103':
						t = '已结束'
						break;
					case '104':
						t = '禁播'
						break;
					case '105':
						t = '暂停中'
						break;
					case '106':
						t = '异常'
						break;
					case '107':
						t = '已过期'
						break;
				}
				return t
			},
		},
		onShareAppMessage() {
			return {
				title: '邀你一起看直播！',
			}
		},
		onShareTimeline(e) {
			return {
				title: '邀你一起看直播！',
				imageUrl: this.getImgS(this.system.shareIcon),
			}
		}
	}
</script>

<style scoped lang="scss">
	.zban {
		width: 100rpx;
		height: 100rpx;
		border: 1px solid #fff;
		background: rgba(0, 0, 0, 0.5);
		border-radius: 50%;
	}
	.zbzt {
		width: 140rpx;
		padding: 15rpx 0 15rpx 20rpx;
		background: rgba(109, 109, 109, 0.9);
		border-radius: 0 60rpx 60rpx 0;

		.yd {
			width: 15rpx;
			height: 15rpx;
		}
	}
</style>
