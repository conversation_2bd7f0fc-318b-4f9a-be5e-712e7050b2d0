<template>
	<view class="page">
		<view class="page-navigation-bar">
			<view class="go-icon">
				<text class="iconfont iconback f40 " @click="go('back','/yb_o2ov2/my/index')"></text>
			</view>
			<text>{{$t('my.issue_an_invoice')}}</text>
		</view>
		<view class="content">
			<view class="box" @click="hanleClick(1)">
				<view class="label">{{$t('issueAnInvoice.type')}}</view>
				<view class="value">
					<text>{{$t('issueAnInvoice.invoice_type')}}</text>
					<image src="/static/home/<USER>" class="arrow-icon" ></image>
				</view>
			</view>
			<view class="box" @click="hanleClick(2)">
				<view class="label">{{$t('issueAnInvoice.invoice_phone')}}</view>
				<view class="value">
					<text>{{$t('issueAnInvoice.phone')}}</text>
					<image src="/static/home/<USER>" class="arrow-icon" ></image>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {}
		},
		methods: {
			hanleClick(value) {
				let url = ''
				if (value == 1) {
					url = '/yb_o2ov2/my/issueAnInvoice/invoice'
				} else {
					url = '/yb_o2ov2/my/issueAnInvoice/carrier'
				}
				this.go('navigateTo', url)
			}
		}
	}
</script>

<style lang="scss" scoped>
	.page {
		width: 100vw;
		height: 100vh;

		.content {
			padding: 0 30rpx;
			box-sizing: border-box;

			.box {
				width: 100%;
				margin-top: 20rpx;

				.label {
					font-weight: 600;
					font-size: 30rpx;
					color: #333333;
				}

				.value {
					display: flex;
					width: 100%;
					height: 100rpx;
					justify-content: space-between;
					align-items: center;
					margin-top: 20rpx;
					background-color: #fff;
					border-radius: 20rpx;
					padding: 20rpx;
					box-sizing: border-box;

					font-weight: 400;
					font-size: 28rpx;
					color: #333333;

					.arrow-icon {
						width: 40rpx;
						height: 40rpx;
					}
				}
			}
		}
	}
</style>