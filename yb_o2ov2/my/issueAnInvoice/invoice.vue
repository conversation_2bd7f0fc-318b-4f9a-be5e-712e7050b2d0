<template>
	<view class="page">
		<view class="page-navigation-bar">
			<view class="go-icon">
				<text class="iconfont iconback f40 " @click="handleBack"></text>
			</view>
			<text>{{$t('issueAnInvoice.invoice_type')}}</text>
		</view>
		<view class="content">
			<view class="item">
				<view class="label">{{$t('issueAnInvoice.company')}}</view>
				<view class="value">
					<input class="ipt" v-model="formData.companynumberheader"
						:placeholder="$t('issueAnInvoice.company_placeholder')" />
				</view>
			</view>
			<view class="item">
				<view class="label">{{$t('issueAnInvoice.number')}}</view>
				<view class="value">
					<input class="ipt" v-model="formData.unifiednumbering"
						:placeholder="$t('issueAnInvoice.number_placeholder')" />
				</view>
			</view>
			<view class="item">
				<view class="label">E-mail</view>
				<view class="value">
					<input class="ipt" v-model="formData.email" :placeholder="$t('issueAnInvoice.email_placeholder')" />
				</view>
			</view>
			<view class="item">
				<view class="label">{{$t('issueAnInvoice.set')}}</view>
				<view class="value">
					<switch :checked="formData.status" color="#FFCC33" style="transform:scale(0.7)"
						@change="switchChange" />
				</view>
			</view>
			<view class="but">
				<button class="btn" :style="{background:'#FFCC00',color:'#fff'}" type="default"
					@click="saveAddress">{{$t('common.save')}}</button>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				formData: {
					companynumberheader: '',
					unifiednumbering: '',
					email: '',
					status: true
				}
			}
		},
		onLoad() {
			this.getInfo()
		},
		methods: {
			handleBack() {
				const pages = getCurrentPages();
				if (pages.length > 1) {
					this.go('back');
				} else {
					// 如果没有上一页，跳转到首页
					this.go('reLaunch', '/yb_o2ov2/index/index');
				}
			},
			async getInfo() {
				let {
					data
				} = await this.util.request({
					url: this.api.invoice,
					method: 'GET',
				})
				this.formData = data
			},
			switchChange(e) {
				this.formData.status = e.detail.value
			},
			async saveAddress() {
				let {
					data
				} = await this.util.request({
					'url': this.api.invoice,
					method: 'POST',
					data: this.formData
				}).then(res => {
					console.log(res);
					
					if (res.code === 1) {
						this.util.message(res.msg, 1, 1000)
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.page {
		width: 100vw;
		height: 100vh;

		.content {
			width: 100%;
			margin-top: 40rpx;
			padding: 0 30rpx;
			box-sizing: border-box;

			.item {
				display: flex;
				width: 100%;
				height: 100rpx;
				justify-content: space-between;
				align-items: center;
				margin-top: 20rpx;
				background-color: #fff;
				border-radius: 20rpx;
				padding: 20rpx;
				box-sizing: border-box;

				.label {
					width: 30%;
					font-weight: 400;
					font-size: 28rpx;
					color: #333333;
				}

				.value {
					flex: 1;
					display: flex;
					justify-content: flex-end;
					align-items: center;
				}

				.ipt {
					height: 100%;
					font-weight: 400;
					font-size: 28rpx;
					color: #333333;
					text-align: end;
				}
			}

			.but {
				margin-top: 80rpx;
			}
		}
	}
</style>