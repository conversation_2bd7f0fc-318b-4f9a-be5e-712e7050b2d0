<template>
	<view class="p2" style="padding-top: 0;">
		<!-- <view>已贡献{{count}}条评价</view> -->
		<view v-for="item in list" :key="item.id" class="p2 bf bs20 mt20 ">
			<view class="flex">
				<view class="bsf f-s-0 mr20" style="width: 80rpx;height: 80rpx;">
					<image class="wh" :src="item.store.icon" mode=""></image>
				</view>
				<view class="f-g-1" @click="go('navigateTo',`/yb_o2ov2/home/<USER>">
					<view class="f-y-c">
						<view class="t-o-e wei">{{item.store.name}}</view>
						<view class="mr20"><text class="iconfont iconinto f18"></text></view>
						<view class="f24 c9" style="margin-left: auto;">{{item.createdAt.split(' ')[0]}}</view>
					</view>
					<view class="f20 c9 mt10">
						<text>商店:</text>
						<u-rate size="24" :current="item.star" :active-color="tColor" inactive-color="#f0f0f0"></u-rate>
						<text class="ml10">{{item.star}}星</text>
						<text class="ml10">{{getStar(item.star)}}</text>
					</view>
					<view class="f28 p20" style="border-bottom: 1rpx solid #f5f6f9;">{{item.body}}</view>
					<scroll-view scroll-x="true" style="width: 100%;" class="f-x-bt ws-n">
						<view v-for="(img,index) in item.media" :key="img" class="dis-in mr20 bs10" style="width: 190rpx;height: 130rpx;"
						@click="checkImg(index)">
							<image class="wh" :src="img" mode="aspectFill" ></image>
						</view>
					</scroll-view>
				</view>
			</view>
			<!-- //删除 -->
			<view class="p-r p2 f24" style="height: 70rpx;">
				<view class="p-a f-y-c" style="right: 0;">
					<text class="iconfont icondelete f28 mr10"></text>
					<text @click="delComment(item.id)">删除</text>
				</view>
			</view>
		</view>
		<mescroll-empty v-if="list.length===0" :option="{icon:'/static/empty/7.png',tip:'~ 暫無評價 ~'}"></mescroll-empty>
		<!-- <u-loadmore @loadmore="nextPage" :status="status" /> -->
	</view>
</template>

<script>
	export default {
		data() {
			return {
				status:'loading',
				params:{
					page:1
				},
				count:0,
				list:[]
			}
		},
		onLoad() {
			this.fetchData()
		},
		onReachBottom() {
			this.nextPage()
		},
		methods:{
			getStar(val){
				switch(val){
					case '1' :
					return '非常差'
					break;
					case '2' :
					return '差'
					break;
					case '3' :
					return '一般'
					break;
					case '4' :
					return '满意'
					break;
					case '5' :
					return '非常满意'
				}
			},
			
			async fetchData(type){
				this.status = 'loading'
				let {data} = await this.util.request({
					url: this.api.myEvaluateList,
					method: 'GET',
					data: this.params
				})
				this.status = 'loadmore'
				this.count = data.count
				if(type==='refresh'){
					this.list = data.data
					return
				}
				this.list = this.list.concat(data.data)
				if (data.data.length === 0) {
					this.status = 'nomore'
					return
				}
			},
			nextPage() {
				if (this.status === 'loading') { return }
				this.params.page++
				this.fetchData('nextPage')
			},
			async delComment(id){
				await this.util.modal('确认删除该评论吗','删除评论')
				await this.util.request({
					url: this.api.delEvaluate,
					method: 'POST',
					data:{id:id}
				})
				this.params.page = 1
				this.fetchData('refresh')
			},
			checkImg(index){
				var temUrls = this.list[index].media
				uni.previewImage({
					urls:temUrls,
					indicator:true,
					complete:e=>{
						console.log(e)
					}
				})
			},
		}
	}
</script>

<style scoped>

</style>
