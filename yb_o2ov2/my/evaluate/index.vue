<template>
	<scroll-view class="page" scroll-y @scrolltolower="onPullUpBottom">
		<view class="page-navigation-bar">
			<view class="go-icon">
				<text class="iconfont iconback f40 " @click="go('back','/yb_o2ov2/my/index')"></text>
			</view>
			<text>{{$t('my.order_evaluation')}}</text>
		</view>
		<!-- <tab-nav gutter="80" :height="tabNavHeight" :activeColor="'#333'" fontSize="30"
				inactiveTextColor="#999" :current-index="aIdx" :list="tntabs" @change="typeTabChange"
				:isScroll="false" bgColor="transparent" :bold="true" :activeItemStyle="activeTypeTabStyle" :showBar="false"></tab-nav> -->
		<view class="list-box">
			<view class="item" v-for="(item, index) in list" :key="`${item.id}-${index}`">
				<image class="close-img" src="/static/close.png" @click="close(item)"></image>
				<view class="name">{{ Array.isArray(item.good_name) ? item.good_name.join('·') : item.good_name }}</view>
				<view class="info">
					<image class="good-img" :src="item.icon" mode=""></image>
					<view class="right">{{item.createdAt}} {{$t('my.order_placed')}}</view>
				</view>
				<view class="bottom">
					<view class="but" 
						:class="{'detail-btn': item.state !== '5', 'evaluate-btn': item.state === '5'}"
						@click="go('navigateTo',`/yb_o2ov2/my/evaluate/detail?id=${item.id}&type=${pageData.type}`)">
						{{item.state === '5'?$t('evaluate.evaluate_but'):$t('good.detail_text')}}
					</view>
				</view>
			</view>
		</view>
		<mescroll-empty v-if="list.length===0"
			:option="{icon:'/static/empty/9.png',tip:`~ ${$t('common.no_order')} ~`}"></mescroll-empty>
		<u-loadmore v-if="list.length!==0" @loadmore="nextPage" :status="status" />
		<footc></footc>
	</scroll-view>
</template>

<script>
	import footc from '@/components/common/footc.vue'
	import TabNav from '@/components/TabNav.vue'
	export default {
		components: {
			footc,
			TabNav
		},
		data() {
			return {
				list: [],
				status: 'loadmore',
				tabNavHeight: 90,
				aIdx: 0,
				pageData: {
					page: 1,
					limit: 15,
					type: 'store'
				},
				activeTypeTabStyle: {
					backgroundColor: '#fff',
					boxShadow: '0 4rpx 10rpx rgba(0, 0, 0, 0.1)',
					fontWeight: 'bolder',
					color: '#333' // Explicitly set color for selected item
				},
				tntabs: [{
						name: '店铺',
					},
					{
						name: '供应商',
					},
				],
				flag: true
			}
		},
		onLoad() {
			// this.getList()
		},
		onShow() {
			this.pageData.page = 1
			this.list = []
			this.flag = true
			this.getList()
		},
		methods: {
			onPullUpBottom() {
				this.getList()
			},
			typeTabChange(index) {
				switch(index) {
					case 0:
						this.pageData.type = 'store'
						this.taIdx = 0
						break;
					case 1:
						this.pageData.type = 'supplier'
						this.taIdx = 1
						break;
				}
				
				this.aIdx = index
				this.$nextTick(() => {
					this.scrollTop = 0;
				});
				this.pageData.page = 1;
				this.list = [];
				this.getList();
			},
			async getList() {
				if (!this.flag) return
				this.flag = false
				this.status = "loading"
				const {
					data
				} = await this.util.request({
					url: this.api.orderEvaluateList,
					method: 'GET',
					data: this.pageData
				})
				
				if (data && data.list && data.list.length > 0) {
					this.list = [...this.list, ...data.list]
					this.pageData.page++
					this.flag = true
					this.status = 'loadmore'
				} else {
					this.status = 'nomore'
					this.flag = true
				}
			},
			async close(item) {
				if (!item.id) return
				uni.showModal({
					title: this.$t('common.showModal_title'),
					content: this.$t('my.confirm_delete'),
					confirmText: this.$t('common.showModal_confirmText'),
					cancelText: this.$t('common.showModal_cancelText'),
					success: async () => {
						await this.util.request({
							url: this.pageData.type === 'supplier' ? this.api.supplierOrderEvaluateDel : this.api.orderEvaluateDel,
							method: 'POST',
							data: {
								id: item.id
							}
						})
						this.pageData.page = 1
						this.list = []
						this.flag = true
						this.getList()
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.page {
		width: 100vw;
		height: 100vh;
		background-color: #f8f8f8;

		.page-navigation-bar {
			background-color: #fff;
			padding: 24rpx 30rpx;
			box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
			display: flex;
			align-items: center;
			position: sticky;
			top: 0;
			z-index: 100;
			backdrop-filter: blur(10px);
			-webkit-backdrop-filter: blur(10px);

			.go-icon {
				margin-right: 24rpx;
				.iconback {
					color: #333;
					font-weight: 500;
					transition: transform 0.3s ease;
					
					&:active {
						transform: scale(0.9);
					}
				}
			}

			text {
				font-size: 34rpx;
				font-weight: 600;
				color: #333;
				letter-spacing: 0.5px;
			}
		}

		.list-box {
			width: 100%;
			padding: 24rpx;
			box-sizing: border-box;

			.item {
				position: relative;
				width: 100%;
				border-radius: 20rpx;
				margin-bottom: 24rpx;
				padding: 32rpx;
				box-sizing: border-box;
				background-color: #fff;
				box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
				transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

				&:active {
					transform: scale(0.98);
					box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.02);
				}

				.close-img {
					position: absolute;
					width: 44rpx;
					height: 44rpx;
					top: 24rpx;
					right: 24rpx;
					opacity: 0.6;
					transition: all 0.3s ease;
					padding: 8rpx;
					border-radius: 50%;

					&:active {
						opacity: 0.8;
						background-color: rgba(0, 0, 0, 0.05);
					}
				}

				.name {
					font-size: 32rpx;
					color: #333;
					font-weight: 500;
					padding-right: 80rpx;
					line-height: 1.5;
					margin-bottom: 8rpx;
				}

				.info {
					display: flex;
					justify-content: flex-start;
					align-items: center;
					margin-top: 24rpx;
					padding: 24rpx 0;
					border-top: 2rpx solid #f5f5f5;
					border-bottom: 2rpx solid #f5f5f5;

					.good-img {
						width: 128rpx;
						height: 128rpx;
						border-radius: 16rpx;
						object-fit: cover;
						box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
					}

					.right {
						margin-left: 28rpx;
						font-size: 28rpx;
						color: #666;
						font-weight: 400;
						line-height: 1.4;
					}
				}

				.bottom {
					display: flex;
					justify-content: flex-end;
					margin-top: 28rpx;

					.but {
						display: flex;
						min-width: 180rpx;
						height: 76rpx;
						padding: 0 36rpx;
						justify-content: center;
						align-items: center;
						border-radius: 38rpx;
						font-size: 30rpx;
						font-weight: 500;
						transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

						&.detail-btn {
							background-color: #f5f5f5;
							color: #666;
							box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

							&:active {
								transform: scale(0.96);
								box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
								background-color: #f0f0f0;
							}
						}

						&.evaluate-btn {
							background: linear-gradient(135deg, #FFCC00, #FFB800);
							color: #fff;
							box-shadow: 0 4rpx 16rpx rgba(255, 204, 0, 0.25);

							&:active {
								transform: scale(0.96);
								box-shadow: 0 2rpx 8rpx rgba(255, 204, 0, 0.15);
								background: linear-gradient(135deg, #FFB800, #FFCC00);
							}
						}
					}
				}
			}
		}
	}
</style>