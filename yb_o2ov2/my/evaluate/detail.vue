<template>
	<view class="page">
		<view class="page-navigation-bar">
			<view class="go-icon">
				<text class="iconfont iconback f40 " @click="handleBack"></text>
			</view>
			<text>{{$t('my.order_evaluation')}}</text>
		</view>
		<view class="content">
			<view class="good">
				<image :src="formData.store.icon" class="good-img"></image>
				<view class="good-name">{{ goodName }}</view>
			</view>
			<view class="score-box">
				<view class="score-title">{{$t('evaluate.score')}}</view>
				<view class="score">
					<view class="score-list">
						<u-rate v-model="formData.star" @change="handleScore" size="80" activeColor="#FFCC00" :disabled="hasEvaluation" />
					</view>
					<view class="right">{{scoreRsult}}</view>
				</view>
			</view>
			<view class="score-text">
				<editor v-if="!hasEvaluation" class="ql-container" :placeholder="$t('evaluate.score_text')" @blur="editorBlur" :maxlength="200"></editor>
				<view v-else class="ql-container readonly">{{ formData.body }}</view>
			</view>
			<view class="but" v-if="!hasEvaluation">
				<button class="btn" :style="{background:'#FFCC00',color:'#fff'}" type="default"
					@click="submit">{{$t('common.submit')}}</button>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				formData: {
					orderId: 0,
					body: '',
					star: 0,
					store: {},
					order_goods: [],
					order_evaluate: []
				}
			}
		},
		computed: {
			goodName() {
				if (!this.formData.order_goods || !Array.isArray(this.formData.order_goods)) {
					return ''
				}
				return this.formData.order_goods.map(item => item.name).join('·')
			},
			hasEvaluation() {
				return this.formData.order_evaluate && this.formData.order_evaluate.list && this.formData.order_evaluate.list.length > 0
			},
			scoreRsult() {
				let result = ''
				switch (this.formData.star.toString()) {
					case '0': 
						result = ''
						break;
					case '1':
						result = this.$t('evaluate.very_poor')
						break;
					case '2':
						result = this.$t('evaluate.differ_from')
						break;
					case '3':
						result = this.$t('evaluate.general')
						break;
					case '4':
						result = this.$t('evaluate.good')
						break;
					case '5':
						result = this.$t('evaluate.very_good')
						break;
				}
				return result
			}
		},
		onLoad(options) {
			if(options && options.id){
				this.getInfo()
			}
		},
		methods: {
			handleBack() {
				const pages = getCurrentPages()
				if (pages.length > 1) {
					this.go('back')
				} else {
					this.go('navigateTo', '/yb_o2ov2/my/evaluate/index')
				}
			},
			async getInfo() {
				const {
					data
				} = await this.util.request({
					url: this.$route.query.type === 'supplier'? this.api.supplierOrderEvaluate : this.api.orderEvaluate,
					method: 'GET',
					data: {
						orderId: this.$route.query.id
					}
				})
				if (data) {
					this.formData = {
						...this.formData,
						...data,
						store: data.store || {},
						body: data.order_evaluate && data.order_evaluate.list[0] ? data.order_evaluate.list[0].body : '',
						star: data.order_evaluate && data.order_evaluate.list[0] ? String(data.order_evaluate.list[0].star) : '0',
						order_evaluate: data.order_evaluate || []
					}
				}
			},
			handleScore(value) {
				this.formData.star = value
			},
			editorBlur(e){
				if (this.hasEvaluation) return
				if(e && e.detail){
					this.formData.body = e.detail.text
				}
			},
			async submit() {
				if(!this.formData.body||!this.formData.star){
					return this.util.message(this.$t('my.evaluate_validation'), 3);
				}
				this.util.request({
					url: this.$route.query.type === 'supplier'? this.api.supplierOrderEvaluate : this.api.orderEvaluate,
					method: 'POST',
					data: {
						orderId: this.$route.query.id,
						body: this.formData.body,
						star: this.formData.star
					}
				}).then(res=>{
					this.util.message(res.msg, 3)
					if(res.code === 1) {
						setTimeout(()=>{
							this.getInfo()
						}, 1000)
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.page {
		width: 100vw;
		height: 100vh;
		background-color: #f8f8f8;

		.page-navigation-bar {
			// background-color: #fff;
			padding: 20rpx 30rpx;
			box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

			.go-icon {
				.iconback {
					color: #333;
					font-weight: 500;
				}
			}
		}

		.content {
			padding: 30rpx;
			box-sizing: border-box;
			margin-top: 20rpx;

			.good {
				display: flex;
				justify-content: flex-start;
				align-items: center;
				background-color: #fff;
				padding: 30rpx;
				border-radius: 16rpx;
				box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.03);

				.good-img {
					width: 120rpx;
					height: 120rpx;
					border-radius: 12rpx;
					object-fit: cover;
				}

				.good-name {
					margin-left: 24rpx;
					font-size: 28rpx;
					color: #333;
					font-weight: 500;
					line-height: 1.4;
				}
			}

			.score-box {
				width: 100%;
				margin-top: 30rpx;
				background-color: #fff;
				padding: 30rpx;
				border-radius: 16rpx;
				box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.03);

				.score-title {
					font-size: 32rpx;
					font-weight: 600;
					color: #333;
					margin-bottom: 20rpx;
				}

				.score {
					display: flex;
					width: 100%;
					height: 160rpx;
					justify-content: space-between;
					align-items: center;

					.score-list {
						display: flex;
						justify-content: flex-start;
						align-items: center;
					}

					.right {
						font-size: 28rpx;
						color: #666;
						font-weight: 500;
					}
				}
			}

			.score-text {
				background-color: #fff;
				border-radius: 16rpx;
				margin-top: 30rpx;
				box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.03);

				.ql-container {
					padding: 30rpx;
					min-height: 240rpx;
					font-size: 28rpx;
					line-height: 1.6;
					color: #333;

					&::placeholder {
						color: #999;
					}
				}

				.readonly {
					color: #333;
					font-size: 28rpx;
					line-height: 1.6;
				}
			}

			.but {
				margin-top: 60rpx;
				padding: 0 40rpx;

				.btn {
					width: 100%;
					height: 88rpx;
					line-height: 88rpx;
					border-radius: 44rpx;
					font-size: 32rpx;
					font-weight: 500;
					box-shadow: 0 4rpx 16rpx rgba(255, 204, 0, 0.3);
					transition: all 0.3s ease;

					&:active {
						transform: scale(0.98);
						box-shadow: 0 2rpx 8rpx rgba(255, 204, 0, 0.2);
					}
				}
			}
		}
	}
</style>