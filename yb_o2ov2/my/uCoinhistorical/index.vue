<template>
	<view class="page">
		<view class="page-navigation-bar">
			<view class="go-icon">
				<text class="iconfont iconback f40 " @click="go('back','/yb_o2ov2/my/index')"></text>
			</view>
			<text>{{$t('my.u_records_of_consumption')}}</text>
		</view>
		<view class="filter-box">
			<picker mode="date" fields="month" :value="currentDate" @change="onDateChange" :start="startYear" :end="endYear">
				<view class="filter-condition">
					<text>{{formatDate}}</text>
					<text class="iconfont iconback icon"></text>
				</view>
			</picker>
		</view>
		<scroll-view scroll-y="true" class="wh" @scrolltolower="loadMore" refresher-enabled @refresherrefresh="onRefresh" :refresher-triggered="isRefreshing">
			<template v-if="list.length>0">
				<view v-for="(item) in list" :key="item.id" class="f-x-bt p23" style="border-bottom: 1px solid #fafafa;">
					<view class="f-col">
						<view class="f30 wei">{{ item.name }}</view>
						<view class="c8 mt10 f24">
							<text>{{item.createtime}}</text>
						</view>
					</view>
					<view class="f-y-c">
						<text :class="getType(item) ? 'red' : 'green'">{{getType(item) ? '+' : ''}}{{item.number}}币</text>
					</view>
				</view>
			</template>
			<mescroll-empty v-if="list.length===0" :option="{icon:'/static/empty/6.png',tip:`~ ${$t('common.no_record')} ~`}"></mescroll-empty>
		</scroll-view>
	</view>
</template>

<script>
	export default{
		data(){
			return{
				list: [],
				page: 1,
				size: 15,
				startTime: '',
				endTime: '',
				currentDate: '',
				isRefreshing: false,
				hasMore: true,
				startYear: '',
				endYear: ''
			}
		},
		computed: {
			formatDate() {
				if (!this.currentDate) return ''
				const [year, month] = this.currentDate.split('-')
				return `${year}年${month}月`
			}
		},
		created() {
			// 设置默认日期为当前月份
			const now = new Date()
			this.currentDate = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`
			// 设置年份范围：当前年份前2年到后2年
			this.startYear = `${now.getFullYear() - 2}-01`
			this.endYear = `${now.getFullYear() + 2}-12`
			this.setDateRange()
			this.fetchData()
		},
		methods: {
			getType(item) {
				return item.after_number - item.before_number > 0 
			},
			setDateRange() {
				const [year, month] = this.currentDate.split('-')
				const startDate = new Date(year, month - 1, 1)
				const endDate = new Date(year, month, 0)
				this.startTime = startDate.toISOString().split('T')[0]
				this.endTime = endDate.toISOString().split('T')[0]
			},
			async fetchData(isLoadMore = false) {
				if (!isLoadMore) {
					this.page = 1
					// this.list = []
				}
				
				try {
					const data = await this.util.request({
						url: this.api.ucoinlog,
						method: 'GET',
						data: {
							page: this.page,
							size: this.size,
							startTime: this.startTime,
							endTime: this.endTime
						}
					})
					
					if (data.code === 2) {
						return this.util.message(data.msg, 3)
					}
					
					if (data.code !== 1) {
						this.util.message(data.msg || '获取数据失败', 3)
						return
					}
					
					if (!Array.isArray(data.data)) {
						this.util.message('数据格式错误', 3)
						return
					}
					
					if (isLoadMore) {
						this.list = [...this.list, ...data.data]
					} else {
						this.list = data.data
					}
					
					this.hasMore = data.data.length === this.size
				} catch (error) {
					console.error('获取数据失败:', error)
					this.util.message('网络异常，请稍后重试', 3)
				} finally {
					this.isRefreshing = false
				}
			},
			async loadMore() {
				if (!this.hasMore) return
				this.page++
				await this.fetchData(true)
			},
			async onRefresh() {
				this.isRefreshing = true
				await this.fetchData()
			},
			onDateChange(e) {
				this.currentDate = e.detail.value
				this.setDateRange()
				this.fetchData()
			},
		}
	}
</script>

<style lang="scss" scoped>
	.page {
		display: flex;
		width: 100vw;
		height: 100vh;
		flex-direction: column;
		
		.filter-box{
			display: flex;
			width: 100%;
			height: 100rpx;
			justify-content: center;
			align-items: center;
			background-color: #FFCC00;
			
			.icon{
				margin-left: 10rpx;
				transform: rotate(-90deg);
			}
		}
		
		.list-box{
			padding: 20rpx 30rpx 0;
			box-sizing: border-box;
			flex: 1;
			
			.list-item{
				display: flex;
				width: 100%;
				justify-content: space-between;
				align-items: center;
				
				.left{
					display: flex;
					justify-content: flex-start;
					align-items: center;
					
					.item-img{
						width: 120rpx;
						height: 120rpx;
						border-radius: 50%;
					}
					
					.info{
						margin-left: 20rpx;
					}
				}
			}
		}
	}
	.red {
		color: red !important;
	}
	.green {
		color: black !important;
	}
</style>