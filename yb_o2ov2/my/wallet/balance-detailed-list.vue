<template>
	<view class="bf">
		<!-- 头部 start -->
		<view class="posi-s w100 "
			:style="{top:0,height:`${statusNavBarHeight}px`,paddingTop:`${menuButtonTop}px`,zIndex:3,background:'#f5f5f5'}">
			<!-- 返回 -->
			<view class="p-a" :style="{top:`${menuButtonTop}px`,left:`20rpx`,zIndex:4}" @click="go('back')">
				<text class="iconfont iconback f40"></text>
			</view>
			<view class="t-c f32 wei">
				<view>{{pageTitle}}</view>
			</view>
		</view>
		<!-- 头部 end -->
		<view class="w100" v-if="showTabs">
			<!-- height 80rpx -->
			<tab-nav :activeColor="tColor" :isScroll="false" :bg-color="bgColor" :current-index="current" :list="tabs"
				@change="tabsChange"></tab-nav>
		</view>
		<scroll-view scroll-y="true" @scrolltolower="nextPage" :style="{height:`${scrollHeight}px`}">
			<view class="p02">
				<view
					v-for="item in list" :key="item"
					class="f-x-bt p20" style="border-top: 1px solid #f5f6f9;"
					@click="go('navigateTo',`/yb_o2ov2/my/wallet/balance-detail?type=${item.type}&note=${item.note}&money=${item.money}&time=${item.createdAt}&tid=${item.id}`)">
					<view>
						<view>{{item.note}}</view>
						<view class="c9 f24">{{item.createdAt}}</view>
					</view>
					<view class="f-y-c">
						<text class="wei f36">{{item.type==1?'+':'-'}}{{item.money}}</text>
						<text class="iconfont iconinto c9 f24 ml10"></text>
					</view>
				</view>
				<u-loadmore @loadmore="nextPage" :status="status" />
			</view>
		</scroll-view>
	</view>
</template>

<script>
	import TabNav from '@/components/TabNav.vue'
	export default {
		components: {
			TabNav
		},
		data() {
			return {
				params:{
					page:1,
					size:10,
					type:0 ,//  1收入 2支出
				},
				status:'loadmore',
				onlyType:'',
				list: [],
				showType: 0,
				current: 0,
				bgColor: '#fff',
				tabs: [{
					name: '全部'
				}, {
					name: '收入'
				},
				{
					name: '支出',
				}
				],
			}
		},
		computed: {
			showTabs() {
				return this.onlyType === ''
			},
			pageTitle(){
				if(this.onlyType === '1') return '充值明细'
				if(this.onlyType === '2') return '消费明细'
				return '账户明细'
			},
			scrollHeight() {
				return this.wHeight - this.statusNavBarHeight -(this.showTabs?80 / this.pxToRpxRate:0)
			}
		},
		onLoad(option) {
			this.onlyType = option.onlyType || ''
			if(this.onlyType)this.params.type = this.onlyType
			this.fetchData()
		},
		methods: {
			tabsChange(index){
				this.params.type = index
				this.refresh()
			},
			refresh(){
				this.params.page = 1
				this.fetchData()
			},
			async fetchData(){
				let {data} =  await this.util.request({
					url: this.api.balanceDetails,
					method: 'GET',
					data:this.params
				})
				let list  = data.list
				console.log(list)
				if(this.params.page === 1){
						this.list = list
					}else{
						if(list.length < this.params.size){
							this.list = this.list.concat(list)
							this.status = 'nomore'
							return
						}
						this.list = this.list.concat(list)
					}
				this.status='loadmore'
			},
			nextPage(){
				if(this.status !== 'loadmore')return
				this.params.page++
				this.fetchData()
			}
		}
	}
</script>

<style>
</style>
