<template>
	<view>
		<!-- 头部 start -->
		<view class="bf posi-s w100 " :style="{top:0,height:`${statusNavBarHeight}px`,paddingTop:`${menuButtonTop}px`,zIndex:3}">
			<!-- 返回 -->
			<view class="p-a" :style="{top:`${menuButtonTop}px`,left:`20rpx`,zIndex:4}" @click="go('back')">
				<text class="iconfont iconback f40"></text>
			</view>
			<view class="t-c f32 wei">
				<view>余额</view>
			</view>
		</view>
		<!-- 头部 end -->
		<view class="p3">
			<view class="card">
				<view class="f28 mb15">账户余额（元）</view>
				<view class="f70 wei mb20">{{user.balance}}</view>
				<view class="c9 mb60" @click="go('navigateTo','/yb_o2ov2/my/wallet/balance-detailed-list')">查看明细 <text class="iconfont iconinto f18 ml20"></text></view>
				<view class="wei f-x-bt w100">
					<!-- <view class="button">提现</view> -->
					<view class="button mla" @click="goToRecharge" :style="{background:tColor,color:fontColor}">充值</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data(){
			return {}
		},
		methods:{
			goToRecharge(){
				// this.util.message('即将上线，敬请期待',3)
				// return
				this.go('navigateTo','/yb_o2ov2/my/wallet/recharge')
			},
		}
	}
</script>

<style scoped lang="scss">
	.card{
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 40rpx 30rpx;
		padding-top: 70rpx;
		border-radius: 20rpx;
		background: #fff;
	}
	.button{
		width: 300rpx;
		height: 96rpx;
		line-height: 96rpx;
		text-align: center;
		border-radius: 20rpx;
		background: #f5f6f9;
	}
</style>
