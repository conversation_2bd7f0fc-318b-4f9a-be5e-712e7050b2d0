<template>
	<view>
		<!-- 头部 start -->
		<view class="posi-s w100 " :style="{top:0,height:`${statusNavBarHeight}px`,paddingTop:`${menuButtonTop}px`,zIndex:3,background:'#f5f5f5'}">
			<!-- 返回 -->
			<view class="p-a" :style="{top:`${menuButtonTop}px`,left:`20rpx`,zIndex:4}" @click="go('back')">
				<text class="iconfont iconback f40"></text>
			</view>
			<view class="t-c f32 wei">
				<view>{{type==1?'收入':'支出'}}詳情</view>
			</view>
		</view>
		<!-- 头部 end -->
		<view class="p2 bf h100v">
			<view class="f-x-bt" style="border-bottom: 1rpx solid #f5f6f9;padding: 60rpx 0;">
				<view class="">{{type==1?'入账':'出账'}}金额</view>
				<view class="f60 wei">{{type==1?'+':'-'}}{{money}}</view>
			</view>
			<view class="f-x-bt mt30">
				<view class="c6">交易类型</view>
				<view>{{type==1?'收入':'支出'}}</view>
			</view>
			<view class="f-x-bt mt30">
				<view class="c6">交易时间</view>
				<view>{{time}}</view>
			</view>
			<view class="f-x-bt mt30">
				<view class="c6">流水单号</view>
				<view>{{tid}}</view>
			</view>
			<view class="f-x-bt mt30">
				<view class="c6">备注</view>
				<view>{{note}}</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
	
		data() {
			return {
				type:1,
				money:'',
				time:'',
				tid:0,
				note:''
			}
		},
		onLoad(option) {
			this.type = option.type
			this.money = option.money
			this.time = option.time
			this.tid = option.tid
			this.note = option.note
		},
	
	}
</script>

<style>
</style>
