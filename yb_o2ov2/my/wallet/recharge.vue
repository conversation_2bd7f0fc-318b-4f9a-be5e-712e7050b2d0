<template>
	<view>
		<!-- 头部 start -->
		<view class="posi-s w100"
			:style="{background:changedColor,color:cFontColor,top:0,height:`${statusNavBarHeight}px`,paddingTop:`${menuButtonTop}px`,zIndex:3}">
			<!-- 返回 -->
			<view class="p-a" :style="{top:`${menuButtonTop}px`,left:`20rpx`,zIndex:4}" @click="go('back')">
				<text class="iconfont iconback f40" :style="{color:cFontColor}"></text>
			</view>
			<view class="t-c f32 wei">
				<view>充值中心</view>
			</view>
		</view>
		<!-- 头部 end -->
		<!-- 渐变背景 -->
		<view class="p-a t0 w100" style="height: 500rpx;z-index: -1;"
			:style="{background:bgColor,top:`${statusNavBarHeight}px`}"></view>
		<!-- main-content -->
		<view class="w100 p2">
			<view class="my-blance">我的账户:</view>
			<view class="f-x-bt">
				<view class="f-y-c wei" :style="{color:cFontColor}" @click="go('navigateTo','/yb_o2ov2/my/wallet/recharge-detailed-list')">
					<text class="f70">{{user.balance}}</text>
					<text class="iconfont iconinto f30" :style="{color:cFontColor}"></text>
				</view>
				<view class="bsf" style="width: 70rpx;height: 70rpx;">
					<image class="wh" :src="user.portrait"></image>
				</view>
			</view>
			<!-- 选择充值金额 -->
			<view class="card mt40">
				<view class="title">
					<text class="main">账户充值</text>
				</view>
				<view class="f-x-bt f-w">
					<view v-for="(item,index) in rules" :key="item.id" class="plan-box"
						:style="{background:planBoxBgColor,borderColor:aIdx==index?tColor:'',color:aIdx==index?tColor:''}"
						@click="change(index)">
						<view :style="{color:changedColor}">
							<text class="f48 wei">{{Number(item.money)}}</text>
							<text class="f30 wei">元</text>
						</view>
						<view class="f24 item t-c">
							<text v-if="item.moneyOpenShow||item.integralOpenShow||item.growOpenShow">赠送:</text>
							<text v-if="item.moneyOpenShow">{{item.giveMoney}}元</text>
							<text v-if="item.moneyOpenShow&&(item.integralOpenShow || item.growOpenShow)" class="m01">+</text>
							<text v-if="item.integralOpenShow">{{item.integral}}积分</text>
							<text v-if="item.growOpenShow" class="m01">+</text>
							<text v-if="item.growOpenShow">{{item.giveGrow}}成长值</text>
							
						</view>
					</view>
					<!-- 自由金额 -->
					<view class="plan-box"
					:style="{background:planBoxBgColor,borderColor:aIdx==-1?tColor:'',color:aIdx==-1?tColor:''}">
						<input :focus='focus' @click="change(-1)" v-model="money" class="t-c f48 wei" type="digit" maxlength="6"
						:style="{color:changedColor}"
						style="width: 160rpx;height: 70rpx;"
						placeholder="请输入" placeholder-class="f30 t-c c3"></input>
						<view class="f20 item">其他金额</view>
					</view>
					<!-- 样式占位 -->
					<view v-if="rules.length%3 === 1" class="plan-box"></view>
				</view>
			</view>
			<!-- 储值说明 -->
			<view class="card mt20">
				<view class="title">
					<text class="main">储值说明</text>
				</view>
				<view><rich-text :text="instructions"></rich-text></view>
			</view>
			<view class="foot-btnc cf f30 f-c bf">
				<button @click="save" :loading="loading" :disabled="loading" class="foot-btn ljcz bs15 f30" hover-class="btnhc" :style="{background:tColor}">立即储值</button>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapActions
	} from 'vuex'
	import utils from '@/common/utils.js'
	import RichText from '@/components/RichText.vue'
	export default {
		components: {
			RichText
		},
		data() {
			return {
				active: '', //选中
				rules: [],
				instructions: '', //说明
				money: '' ,//手动输入金额,
				loading: false,
				aIdx: -1,
				focus: false,
			}
		},
		computed: {
			...mapState({
				recharge: state => state.config.recharge,
			}),
			bgColor() {
				return `linear-gradient(180deg, ${this.changedColor} 220rpx, rgba(245,245,245,0))`
			},
			cFontColor(){
				return '#FFFFFF'
			},
			changedColor() {
				// return utils.jjldColor(this.tColor,30)
				return '#3874F6'
			},
			planBoxBgColor(){
				return `${this.changedColor}10`
			}
		},
		async onLoad() {
			this.init()
		},
		methods: {
			...mapActions(["getConfig"]),
			convert(money){
				const s = (money+'').split('.')
				const int = s[0]
				const decimal = s[1]
			},
			change(v) {
				this.aIdx = v
				if (v == -1) {
					this.focus = true
				} else {
					this.focus = false
					// this.xzrule = this.rule.list[v]
				}
			},
			async init() {
				this.getRule()
				await this.getConfig({
					name: 'recharge',
					method: 'POST',
					api: this.api.config,
					data: {
						ident: 'recharge'
					},
				})
				//  watch和computed监听不到config内对象的变化 手动刷新
				this.instructions = this.recharge.details
			},
			async getRule() {
				let { data } = await this.util.request({
					url: this.api.czgz,
					method: 'GET'
				})
				data.list.forEach(item => {
					if (item.moneyOpen === '1') {
						item.moneyOpenShow = true
					}
					if (item.integralOpen === '1') {
						item.integralOpenShow = true
					}
					if (item.growOpen === '1') {
						item.growOpenShow = true
					}
					if (item.couponOpen === '1') {
						item.couponOpenShow = true
					}
				})
				this.rules = data.list
				if (data.list.length) {
					this.aIdx = 0
				}
			},
			async save() {
				if (!this.isLogin) {
					this.go('navigateTo', '/yb_o2ov2/my/login')
					return
				}
				console.log(this.recharge)
				let money = this.money
				//确认金额
				if (this.aIdx === -1 && money == '') {
					this.util.message('请确定储值金额', 3)
					return
				}
				if (this.aIdx === -1 && Number(money) < +this.recharge.downMoney && Number(money) < 0) {
					this.util.message('最小储值金额' + this.recharge.downMoney, 3)
					return
				}
				if (this.aIdx !== -1) {
					money = this.rules[this.aIdx].money
				}
				if (!this.jjmbxx) {
					try {
						await this.requestSM('recharge')
					} catch (e) {
						this.jjmbxx = true
						return
					}
				}
				let res = await this.util.request({
					'url': this.api.czxd,
					method: 'POST',
					mask: '请求中',
					data: {
						money: money
					}
				})
				if (res.code === 1) {
					let data = {}
					data.orderId = res.data
					data.orderType = 2,
						data.money = money,
						//支付前需要保存支付信息传给支付页面
						this.setPayInfo(data)
					this.go('navigateTo', '/yb_o2ov2/home/<USER>/index')
				} else {
					this.util.message(res.msg, 3)
				}

			}
		},
	}
</script>

<style scoped lang="scss">
	.my-blance {
		display: inline-block;
		border-radius: 30rpx;
		padding: 8rpx 16rpx;
		color: #FFFFFF;
		background: rgba(0, 0, 0, .1);
	}

	.card {
		padding: 20rpx;
		border-radius: 20rpx;
		overflow: hidden;
		background-color: #FFFFFF;

		.title {
			padding-bottom: 20rpx;

			.main {
				font-size: 34rpx;
				font-weight: bold;
				margin-right: 10rpx;
			}

			.sub {
				font-size: 24rpx;
				color: #999
			}
		}
	}

	.plan-box {
		width: 210rpx;
		height: 160rpx;
		border: 1px solid #fff;
		border-radius: 10rpx;
		padding: 10rpx;
		margin-bottom: 20rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: flex-start;
		.item {
			color: #999
		}
	}
	.ljcz{
		width: 320rpx;
	}
</style>
