<template>
	<view class="h100" :style="{background:`linear-gradient(0deg, rgba(255,255,255,0) 50% , ${tColor})`}">
		<!-- 头部 start -->
		<view class="posi-s w100 "
			:style="{top:0,height:`${statusNavBarHeight}px`,paddingTop:`${menuButtonTop}px`,zIndex:3,background:'#fff'}">
			<!-- 返回 -->
			<view class="p-a" :style="{top:`${menuButtonTop}px`,left:`20rpx`,zIndex:4}" @click="go('back')">
				<text class="iconfont iconback f40"></text>
			</view>
			<view class="t-c f32 wei">
				<view>代付详情</view>
			</view>
		</view>
		<!-- 头部 end -->
		<view class="">
			<view class="cf p-r mt20">
				<view class="rwimg bsf">
					<image class="wh" :src="user.portrait" mode="aspectFit"></image>
				</view>
				<view class="t-c c0 p2">
					<text class="wei">{{user.userName}}</text>
					<text>发起了订单代付请求~</text>
				</view>
				<!-- <view class="p-a dftxt f32">{{payConfig && payConfig.help}}</view> -->
			</view>
			<!-- 详情card -->
			<view class="card mt20">
				<view class="mt10 f28 c9 bs10 f-c">
					<view>需付款</view>
				</view>
				<view class="paymoeny wei f40 f-c"><text class="nowei">$</text><text
						class="f56">{{Number(pmoney)}}</text></view>
				<view class="f28 djsc f-c wei">
					剩余支付时间
					<view class="ml10 f-y-c ml10 nowei">
						<view class="time-num">{{stime[2]}}</view>
						<view class="m01">:</view>
						<view class="time-num">{{stime[3]}}</view>
					</view>
				</view>
				<view class="send mt40" :style="{background:tColor,color:fontColor}">
					<button form-type="submit" open-type='share' class="foot-btn b-s-2 f30 wei c0"
						:style="{background:tColor,color:fontColor}">发送给微信好友</button>
				</view>
			</view>
			<!-- 商品card -->
			<view class="card mt10">
				<view class="f32 wei">{{info.storeName}}</view>
				<view v-for="goods in info.goodsArr" :key="goods.id" class="f-y-c mt20" style="height: 110rpx;">
					<view class="mr20 h100" style="width: 110rpx;">
						<image class="wh" :src="goods.icon"></image>
					</view>
					<view class="h100">
						<view class="f-col">
							<view class="wei">{{goods.name}}</view>
							<view class="c9">
								<text v-if="goods.groupName">{{goods.groupName}}</text>
								<text v-if="goods.groupName&&(goods.attribute||goods.materialName)">,</text>
								<text v-if="goods.attribute">{{goods.attribute}}</text>
								<text v-if="goods.attribute&&goods.materialName">,</text>
								<text v-if="goods.materialName">{{goods.materialName}}</text>
							</view>
							<view class="c9">x{{goods.num}}</view>
						</view>
					</view>
					<view class="wei h100" style="margin-left: auto;">
						<view>${{goods.money}}</view>
					</view>
				</view>

			</view>
			<view class="">
				<view class="f30 wei c0 p253">代付说明</view>
				<view class="p03 f24 mb10 c9">1、对方需要开通微信支付才能帮您付款，如未开通，请重新选择其他好友发送；</view>
				<view class="p03 f24 mb10 c9">2、当代付订单退款成功后，实付金额将原路退还代付人。</view>
			</view>

		</view>
		<mg-share wxs='1' v-model="shareshow"></mg-share>
	</view>
</template>

<script>
	import {
		mapActions,
		mapState,
		mapGetters
	} from 'vuex'
	import utils from '@/common/utils.js'
	import {
		utilMixins
	} from "@/common/util-mixins.js"
	import mgShare from '@/components/template/share.vue'
	export default {
		name: 'df',
		components: {
			mgShare,
		},
		data() {
			return {
				stime: '',
				info: null, //订单详情
				loading: false,
				shareshow: false,
			}
		},
		async onLoad(options) {
			this.getSystem()
			await this.getConfig({
				name: 'payConfig',
				api: this.api.config,
				data: { ident: 'payConfig' }
			})
			await this.getConfig({
				name: 'orderSet',
				api: this.api.orderSet,
			})
			if (options.orderId) {
				let {
					data
				} = await this.util.request({
					'url': this.api.orderRep,
					data: {
						orderId: options.orderId,
					}
				})
				this.info = data
				this.djs(data.createdAt)
			}
		},
		mixins: [utilMixins],
		computed: {
			...mapState({
				payConfig: state => state.config.payConfig,
				orderset: state => state.config.orderSet,
			}),
			pmoney() {
				return Number(this.info && this.info.money || 0).toFixed(2)
			},
		},
		methods: {
			...mapActions(['getConfig']),
			djs(time) {
				let now = this.dateToTime(),
					time2 = +time + this.orderset.closeTime * 60
				if (time2 > now) {
					this.stime = utils.countDownTime(time2 - now)
					this.dsq = setInterval(async () => {
						time2 -= 1
						if (time2 == now) {
							clearInterval(this.dsq)
							this.go({
								t: 4
							})
						}
						this.stime = utils.countDownTime(time2 - now)
					}, 1000)
				}
			},
		},
		onShareAppMessage() {
			//this.go('reLaunch', '/yb_o2ov2/index/index')
			let p = `/yb_o2ov2/home/<USER>/payment-for-friend?orderId=${this.info.id}`
			return this.util.mpShare({
				t: this.payConfig.help,
				i: this.payConfig.icon && this.getSingleImg(this.payConfig.icon),
				p
			})
		},
	}
</script>
<style scoped lang="scss">
	.rwimg {
		width: 100rpx;
		height: 100rpx;
		margin: 0 auto;
	}

	.dftxt {
		top: 80rpx;
		left: 75rpx;
		right: 75rpx;
	}

	.card {
		border-radius: 10rpx;
		margin: 0 20rpx;
		padding: 30rpx;
		background: #fff;

		.paymoeny {
			display: flex;
			align-items: baseline;
		}
	}

	.djsc {
		line-height: 70rpx;
	}

	.send {
		width: 100%;
		border-radius: 15rpx;
		overflow: hidden;
	}

	.time-num {
		background: #333;
		color: #fff;
		width: 50rpx;
		height: 42rpx;
		text-align: center;
		line-height: 42rpx;
		border-radius: 6rpx;
	}
</style>
