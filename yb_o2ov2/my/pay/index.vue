<template>
	<view class="wh">
		<!-- 头部 start -->
		<view class="bf5 posi-s w100" :style="{ top: 0, height: `${statusNavBarHeight}px`, paddingTop: `${menuButtonTop}px`, zIndex: 3 }">
			<!-- 返回 -->
			<view class="p-a" :style="{ top: `${menuButtonTop}px`, left: `20rpx`, zIndex: 4, width: '60rpx', height: '60rpx' }" @click="pageBack">
				<text class="iconfont iconback f40"></text>
			</view>
			<view class="t-c f32 wei">
				<view>{{ $t('pay.pay_order') }}</view>
			</view>
		</view>
		<!-- 头部 end -->
		<view class="p2 mt40">
			<view class="f-c-c">
				<!-- 锁单时间 -->
				<block v-if="[1].includes(payObj.orderType) && orderSet.autoClose == '1'">
					<view class="f28 c6">
						{{ $t('pay.pay_order_time') }}
						<text class="wei ml10" v-if="stime">{{ stime[2] }}:{{ stime[3] }}</text>
					</view>
				</block>
				<!-- 实付金额 -->
				<view class="wei" style="margin-top: -15rpx; margin-bottom: -15rpx">
					<text class="f34">$</text>
					<text class="f70">{{ orderInfo.money }}</text>
				</view>
				<!-- 原价 -->
				<!-- <view class="t-d-l c3 f28">$33.28</view> -->
				<!-- 订单详情 -->
				<block v-if="[1].includes(payObj.orderType)">
					<view class="f28 c6 f-y-c" @click="go('redirectTo', `/yb_o2ov2/my/supplier/order-info?orderId=${payObj.orderId}`)">
						<text class="mr10">{{ orderInfo.storeName }}</text>
						<text>{{ $t('pay.order_detail') }}</text>
						<text class="iconfont iconinto f28 c6"></text>
					</view>
				</block>
				<block v-if="payObj.orderType === 2">
					<view class="f28 c6 f-y-c">
						<text>储值支付</text>
					</view>
				</block>
				<block v-if="payObj.orderType === 5">
					<view class="f28 c6 f-y-c">
						<text>买单支付</text>
					</view>
				</block>
				<block v-if="payObj.orderType === 3 || payObj.orderType === 7 || payObj.orderType === 8">
					<view class="f28 c6 f-y-c">
						<text>下单支付</text>
					</view>
				</block>
			</view>
			<!-- line -->
			<view v-if="[1, 2, 3, 5, 7, 8, 9].includes(payObj.orderType) && payMode.includes('1')" class="mt60 bf bs20 p2 f-x-bt" @click="payType = 1">
				<view class="wei f32 f-y-c">
					<view class="mr10" style="width: 60rpx; height: 60rpx">
						<image class="wh" src="/static/4.png" mode=""></image>
					</view>
					<view>{{ $t('pay.line_pay') }}</view>
				</view>
				<view>
					<text :class="payType === 1 ? 'iconselect black' : 'iconnoselect'" class="iconfont f40" :style="{ color: tColor, background: payType === 1 ? fontColor : '' }"></text>
				</view>
			</view>
			<!-- 街口 -->
			<view v-if="[1, 3, 5, 7, 8, 9].includes(payObj.orderType) && payMode.includes('2')" class="mt20 bf bs20 p2 f-x-bt" @click="payType = 2">
				<view class="wei f32 f-y-c">
					<view class="mr10" style="width: 60rpx; height: 60rpx">
						<image class="wh" src="/static/2.png" mode=""></image>
					</view>
					<view>
						<text>{{ $t('pay.street_pay') }}</text>
						<!-- <text class="c6 f24 ml20">({{user.balance}})</text> -->
					</view>
				</view>
				<view>
					<text :class="payType === 2 ? 'iconselect black' : 'iconnoselect'" class="iconfont f40" :style="{ color: tColor, background: payType === 2 ? fontColor : '' }"></text>
				</view>
			</view>
			<!-- 现金 -->
			<view v-if="[1, 3, 5, 7, 8, 9].includes(payObj.orderType) && payMode.includes('3')" class="mt20 bf bs20 p2 f-x-bt" @click="payType = 3">
				<view class="wei f32 f-y-c">
					<view class="mr10" style="width: 60rpx; height: 60rpx">
						<image class="wh" src="/static/1.png" mode=""></image>
					</view>
					<view>
						<text>貨到付款</text>
					</view>
				</view>
				<view>
					<text :class="payType === 3 ? 'iconselect black' : 'iconnoselect'" class="iconfont f40" :style="{ color: tColor, background: payType === 3 ? fontColor : '' }"></text>
				</view>
			</view>
			<!-- 货到付款 -->
			<!-- <view v-if="[1].includes(payObj.orderType)&&payConfig.deliveryOpen === '1'" class="mt20 bf bs20 p2 f-x-bt" @click="payType = 2">
				<view class="wei f32 f-y-c">
					<view class="mr10" style="width: 60rpx;height: 60rpx;">
						<image class="wh"
							src="/static/1.png"
							mode=""></image>
					</view>
					<view>货到付款</view>
				</view>
				<view>
					<text :class="payType===2?'iconselect black':'iconnoselect'" class="iconfont f40"
						:style="{color:tColor,background:payType===2?fontColor:''}"></text>
				</view>
			</view> -->
			<!-- 好友代付 -->
			<!-- <view v-if="[1].includes(payObj.orderType)&&payConfig.payMentOpen === '1'" class="mt20 bf bs20 p2 f-x-bt" @click="payType = 3">
				<view class="wei f32 f-y-c">
					<view class="mr10" style="width: 60rpx;height: 60rpx;">
						<image class="wh"
							src="/static/3.png"
							mode=""></image>
					</view>
					<view>好友代付</view>
				</view>
				<view>
					<text :class="payType===3?'iconselect black':'iconnoselect'" class="iconfont f40"
						:style="{color:tColor,background:payType===3?fontColor:''}"></text>
				</view>
			</view> -->
		</view>
		<button class="btn" :style="{ background: tColor, color: fontColor }" type="default" @click="pay">{{ $t('pay.pay_confirm') }}</button>
	</view>
</template>

<script>
import { mapState, mapActions } from 'vuex';
import utils from '@/common/utils.js';
export default {
	data() {
		return {
			// orderTpye:1外卖订单2.充值订单3积分商城订单4券包5收银6快餐7.商店8.会员9预约
			payType: '',
			stime: '',
			payObj: {},
			orderInfo: {},
			loading: false,
			payMode: []
		};
	},
	watch: {},
	computed: {
		...mapState({
			orderSet: (state) => state.config.orderSet,
			payConfig: (state) => state.config.payConfig
		})
	},
	onLoad(option) {
		this.refreshUser(); //获取余额
		this.getPayConfig();
		this.init();
	},
	// onBackPress() {
	// 	console.log('aaaaaaa')
	// 	return true
	// },
	onUnload() {
		//更新订单列表
		getCurrentPages()[0].$vm.$refs.initOrder.init();
		// 清除定时器
		if (this.setIntervalTime) {
			clearInterval(this.setIntervalTime);
		}
	},
	methods: {
		...mapActions(['getConfig']),
		getPayConfig() {
			this.getConfig({
				name: 'payConfig',
				api: this.api.config,
				data: { ident: 'payConfig' }
			});
		},
		pageBack() {
			//余额充值
			if (this.payObj.orderType === 2) return this.go('back');
			//外卖下单
			if (this.payObj.orderType === 1) return this.go('redirectTo', `/yb_o2ov2/my/supplier/order-info?orderId=${this.payObj.orderId}`);
		},
		async init() {
			//所有跳转支付的页面 需要先保存payInfo 在此页面取
			this.payObj = uni.getStorageSync('payInfo');
			this.getPayInfo();
		},
		setTime(time) {
			let now = utils.dateToTime(),
				time2 = +time + this.orderSet.closeTime * 60;
			if (time2 > now) {
				this.stime = utils.countDownTime(time2 - now);
				this.setIntervalTime = setInterval(() => {
					time2 -= 1;
					if (time2 == now) {
						clearInterval(this.setIntervalTime);
						this.go('back');
					}
					this.stime = utils.countDownTime(time2 - now);
				}, 1000);
			}
		},
		async getPayInfo() {
			if (
				this.payObj.orderType === 2 ||
				this.payObj.orderType === 3 ||
				this.payObj.orderType === 5 ||
				this.payObj.orderType === 7 ||
				this.payObj.orderType === 8 ||
				this.payObj.orderType === 9
			) {
				//余额充值//积分商城
				this.orderInfo.money = this.payObj.money;
				this.orderInfo.orderType = this.payObj.orderType;
				return;
			}
			let { data } = await this.util.request({
				url: this.api.supplierOrderInfo,
				mask: 1,
				data: {
					orderId: this.payObj.orderId
				}
			});
			this.orderInfo = data;

			const res = await this.util.request({
				url: this.api.supplierConfig,
				// mask: 1,
				data: {
					storeId: this.orderInfo.storeId
				}
			});
			this.payMode = JSON.parse(res.data);

			this.setTime(data.createdAt);
		},
		async pay() {
			if (this.payType === '') {
				this.util.message('请选择支付方式', 3);
				return;
			}
			if (this.payObj.orderType == 1) {
				if (!this.jjmbxx) {
					try {
						await this.requestSM('payOrder');
					} catch (e) {
						this.jjmbxx = true;
						this.loading = false;
						return;
					}
				}
			}
			let payObj = this.payObj;
			payObj.payType = this.payType;
			let payres = {};
			let getPayres = async () => {
				let payres = await this.util.request({
					url: this.api.supplierpay,
					method: 'POST',
					mask: 1,
					data: payObj
				});
				return payres;
			};
			let params = `?orderId=${payObj.orderId}&orderType=${payObj.orderType}`;
			switch (this.payType) {
				case 1: // line
					payres = await getPayres();
					if (payres.code === 2) return;
					uni.requestPayment({
						provider: this.provider,
						// #ifdef MP-WEIXIN
						timeStamp: payres.data.timeStamp,
						nonceStr: payres.data.nonceStr,
						package: payres.data.package,
						signType: payres.data.signType,
						paySign: payres.data.paySign,
						success: (res) => {
							if (payObj.orderType == 1) {
								this.go('redirectTo', '/yb_o2ov2/my/order/order-info' + params);
								this.integralAdd();
							} else if (payObj.orderType == 2 || payObj.orderType == 3 || payObj.orderType == 8) {
								this.refreshUser();
								this.go('back');
							} else if (payObj.orderType == 7 && payObj.eatType == 1) {
								this.go('redirectTo', '/yb_o2ov2/shop/in/order-dl?id=' + payObj.orderId);
							} else if (payObj.orderType == 7 && payObj.eatType == 2) {
								this.go('redirectTo', '/yb_o2ov2/shop/in/indd');
							} else if (payObj.orderType == 5) {
								this.go('redirectTo', '/yb_o2ov2/index/index');
							} else if (payObj.orderType == 9) {
								this.go('redirectTo', '/yb_o2ov2/shop/reserve/yyxq?id=' + payObj.orderId);
							}
						},
						fail: (err) => {
							console.log('fail:' + JSON.stringify(err));
							if (err.errMsg == 'requestPayment:fail cancel') {
								this.util.message('取消支付', 2);
							} else {
								uni.showModal({
									title: '提示',
									content: err.errMsg + err.err_desc,
									showCancel: false
								});
								this.loading = false;
							}
						}
						// #endif
					});
					break;
				case 2: // 接口
					payres = await getPayres();
					if (payres.code === 2) return;
					console.log(123, payObj.orderType);
					if (payObj.orderType == 1) {
						this.go('redirectTo', '/yb_o2ov2/my/order/order-info' + params);
					} else if (payObj.orderType == 3) {
						this.go('redirectTo', '/yb_o2ov2/my/integral/integral-record');
					} else if (payObj.orderType == 7 && payObj.eatType == 1) {
						this.util.message(payres.msg || payres.data, 1, 2000);
						setTimeout(() => {
							this.go('redirectTo', '/yb_o2ov2/shop/in/order-dl?id=' + payObj.orderId);
						}, 500);
					} else if (payObj.orderType == 7 && payObj.eatType == 2) {
						this.util.message(payres.msg || payres.data, 1, 2000);
						setTimeout(() => {
							this.go('redirectTo', '/yb_o2ov2/shop/in/indd');
						}, 500);
					} else if (payObj.orderType == 8) {
						this.util.message(payres.msg || payres.data, 1, 2000);
						setTimeout(() => {
							this.go('redirectTo', '/yb_o2ov2/index/index');
						}, 500);
					} else if (payObj.orderType == 5) {
						this.util.message(payres.msg || payres.data, 1, 2000);
						setTimeout(() => {
							this.go('redirectTo', '/yb_o2ov2/index/index');
						}, 500);
					} else if (payObj.orderType == 9) {
						this.util.message(payres.msg || payres.data, 1, 2000);
						setTimeout(() => {
							this.go('redirectTo', '/yb_o2ov2/shop/reserve/yyxq?id=' + payObj.orderId);
						}, 500);
					}
					this.integralAdd();
					break;
				case 3: // 现金
					payres = await getPayres();
					if (payres.code === 2) return;
					this.go('redirectTo', '/yb_o2ov2/my/supplier/order-info' + params);
					break;
				case 3: // 好友代付
					this.go('navigateTo', '/yb_o2ov2/my/pay/payment-by-friend?orderId=' + payObj.orderId);
					break;
			}
		},
		async integralAdd() {
			let { data } = await this.util.request({
				url: this.api.zfyl,
				data: this.payObj
			});
		}
	}
};
</script>

<style scoped lang="scss">
.black {
	background: #000;
	border-radius: 50rpx;
	line-height: 0.5;
}

.btn {
	position: fixed;
	width: 700rpx;
	left: 50%;
	transform: translateX(-50%);
	bottom: 120rpx;
	border-radius: 20rpx;
	font-weight: bold;
}
</style>
