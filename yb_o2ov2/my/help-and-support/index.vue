<template>
	<view>
		<!-- 头部 start -->
		<view class="p-f w100 "
			:style="{top:0,height:`${statusNavBarHeight}px`,paddingTop:`${menuButtonTop}px`,zIndex:3,background:'#fff'}">
			<!-- 返回 -->
			<view class="p-a" :style="{top:`${menuButtonTop}px`,left:`20rpx`,zIndex:4}" @click="go('back')">
				<text class="iconfont iconback f40"></text>
			</view>
			<view class="t-c f32 wei">
				<view>帮助中心</view>
			</view>
		</view>
		<!-- 头部 end -->
		<view class="f-col h100v bf f30" :style="{marginTop:`${statusNavBarHeight+10}px`}">
			<view v-for="item in result" :key="item.id" class="f-x-bt p23" style="border-bottom: 1rpx solid #f5f6f9;"
				@click="go('navigateTo',`/yb_o2ov2/my/help-and-support/QandA?id=${item.id}`)">
				<view class="c3">{{item.title}}</view>
				<view><text class="iconfont iconinto f18 c9"></text></view>
			</view>
			<mescroll-empty v-if="result.length===0" :option="{icon:'/static/empty/9.png',tip:'~ 暂无帮助 ~'}"></mescroll-empty>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				result:[]
			}
		},
		onLoad() {
			this.init()
		},
		methods: {
			async init() {
				let {data} =  await this.util.request({
					url: this.api.bzzx,
					method: 'GET'
				})
				this.result = data
			},
		}
	}
</script>

<style>
</style>
