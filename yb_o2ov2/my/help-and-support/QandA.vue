<template>
	<view>
		<!-- 头部 start -->
		<view class="p-f w100 " :style="{top:0,height:`${statusNavBarHeight}px`,paddingTop:`${menuButtonTop}px`,zIndex:3,background:'#fff'}">
			<!-- 返回 -->
			<view class="p-a" :style="{top:`${menuButtonTop}px`,left:`20rpx`,zIndex:4}" @click="go('back')">
				<text class="iconfont iconback f40"></text>
			</view>
			<view class="t-c f32 wei">
				<view>帮助詳情</view>
			</view>
		</view>
		<!-- 头部 end -->
		<view class="p3 h100v bf" :style="{marginTop:`${statusNavBarHeight+10}px`}">
			<view class="wei mb50 f30">{{result.title}}</view>
			<rich-text :text="result.body"></rich-text>
		</view>
	</view>
</template>

<script>
	import RichText from '@/components/RichText.vue'
	export default {
		components: {
			RichText
		},
		data() {
			return {
				result:{}
			}
		},
		
		onLoad(option) {
			this.init(option.id)
		},
		methods: {
			async init(id) {
				let {data} =  await this.util.request({
					url: this.api.bzxq,
					method: 'GET',
					data: {
						id: id
					}
				})
				this.result = data
			},
		}
	}
</script>

<style>
</style>
