<template>
	<view class="bf mh100 pb130">
		<!-- <nav-tab cname="fixedtop" v-model="aIdx" @changeTab='changeTab' :tabs='tabs'></nav-tab> -->
		<tab-nav :activeColor="tColor" :isScroll="false" :bg-color="bgColor" :current-index="current" :list="tabs"
			@change="tabsChange"></tab-nav>
		<view class="p03">
			<mg-cell cname='p30' v-for="(v,i) in list" :key='i' isl='1' :img='v.portrait' w='72' h='72' brs='50%'>
				<template slot='bd'>
					<view class="f26">{{v.userName}}<text class="f22 c9 ml5">（UID：{{v.bindId}}）</text></view>
					<view class="f22 c9 mt10">推荐时间：{{v.createdAt.substring(0,16)}}</view>
				</template>
				<template slot='ft'>
					<view v-if="v.state=='1'" class="cgreen">已完成</view>
					<view v-if="v.state=='2'" class="cf5">未完成</view>
				</template>
			</mg-cell>
			<mescroll-empty v-if="list.length===0" :option="{icon:'/static/empty/4.png',tip:'~ 暂无记录 ~'}"></mescroll-empty>
			<u-loadmore v-else @loadmore="nextPage" :status="status" />
			<!-- <jzz bgcolor="transparent" :nodata="dataList.length==0&&isget" :mygd='mygd' /> -->
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapActions
	} from 'vuex'
	import MgCell from '@/components/common/mg-cell.vue'
	import {
		sljz,
		utilMixins,
	} from "@/common/util-mixins.js"
	import TabNav from '@/components/TabNav.vue'
	// import jzz from '@/components/common/jzz.vue'
	export default {
		name: 'wdfs',
		components: {
			TabNav,
			MgCell,
		},
		data() {
			return {
				aIdx: 0,
				tabs: [{
					name: '已完成',
				}, {
					name: '未完成',
				}],
				current: 0,
				bgColor: '#fff',
				params: {
					userId: '',
					size: 10,
					page: 1,
				},
				list:[],
				status:'loadmore',
			}
		},
		async onLoad(options) {
			this.getSystem()
			this.util.setNT('邀请列表')
			await this.getLoginInfo()
			this.params.userId = this.uId
			// this.getList()
			// this.changeTab(0)
			this.fetchData()
		},
		mixins: [sljz, utilMixins],
		computed: {
			...mapState('dndc', ['fxsInfo']),
		},
		methods: {
			refresh(){
				this.params.page = 1
				this.fetchData()
			},
			async fetchData() {
				let {
					data
				} = await this.util.request({
					'url': this.api.ownilist,
					method: 'POST',
					data: this.params,
				})
				let list  = data
				if(this.params.page === 1){
						this.list = list
					}else{
						if(list.length < this.params.size){
							this.list = this.list.concat(list)
							this.status = 'nomore'
							return
						}
						this.list = this.list.concat(list)
					}
				this.status='loadmore'
			},
			nextPage(){
				if(this.status !== 'loadmore')return
				this.params.page++
				this.fetchData()
			},
			tabsChange(index){
				this.params.state = index+1
				this.refresh()
			},
			// async getList() {
			// 	let {
			// 		data
			// 	} = await this.util.request({
			// 		'url': this.api.ownilist,
			// 		method: 'POST',
			// 		data: this.params,
			// 	})
			// 	this.dataList = this.dataList.concat(data)
			// 	this.isget = true
			// 	this.mygd = this.params.size > data.length
			// 	this.params.page++
			// },
			changeTab(e) {
				this.isget = this.mygd = false
				this.params.page = 1
				let type = ''
				switch (e) {
					case 0:
						type = '1';
						break;
					case 1:
						type = '2';
						break;
				}
				this.params.state = type
				this.dataList = []
				this.getList()
			},
		},
	}
</script>
<style scoped lang="scss">
	.pt110 {
		padding-top: 110rpx;
	}
	.cgreen{
		color: #5FD047;
	}
</style>
