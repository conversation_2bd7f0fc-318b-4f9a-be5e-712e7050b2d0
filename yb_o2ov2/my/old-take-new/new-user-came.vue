<template>
	<view class="w100v h100v" style="background: #F54947;">
		<!-- 头部 start -->
		<view class="p-a t0 w100 " :style="{top:0,height:`${statusNavBarHeight}px`,paddingTop:`${menuButtonTop}px`,background:changedColor,color:fontColor,zIndex:3}">
			<!-- 返回首页 -->
			<!-- <view class="p-a" :style="{top:`${menuButtonTop}px`,left:`20rpx`,zIndex:4}" @click="backIN">
				<text class="iconfont iconback f40 cf"></text>
			</view> -->
			<view class="t-c f32 wei cf">
				<view>新人专享</view>
			</view>
		</view>
		<!-- 渐变背景 -->
		<view class="p-a t0 w100" style="height: 400rpx;z-index: -1;"
			:style="{background:bgColor,top:`${statusNavBarHeight}px`}"></view>
		<!-- 头部 end -->
		<scroll-view scroll-y class="wh" :style="{paddingTop:`${statusNavBarHeight}px`}">
			<view class="w100 p-r f-c">
				<image class="w100" :src="bgImg" mode="widthFix"></image>
				<view class="w100 p-a coupot ma flex">
					<view class="f-g-0 coutl f-c-c fleft">
						<view class="wei colorR">
							<block>
								<text>$</text><text class="f60 ml5">{{Number(coupon.money)}}</text>
							</block>
						</view>
						<view class="t-o-e cf5">{{fullName}}</view>
					</view>
					<view class="f-g-1 f-c-xc fright ml40">
						<view class="wei f30 t-o-e c3">{{coupon.name || '新人奖励'}}</view>
						<view class="t-o-e c9">
							<text v-if="coupon.storeType==1">所有商户通用</text>
							<text v-else-if="coupon.storeType==2">指定商户可用</text>
							<text v-else-if="coupon.storeType==3 && coupon.storeTypeArr">
								<text v-for="item in coupon.storeTypeArr">仅{{item.name}}可用,</text>
							</text>
						</view>
						<view class="t-o-e c9 f24 mt10">自领取日起{{coupon.day}}天内有效</view>
						<view class="f-row t-o-e c9 f24">
							<view class="f-g-0"></view>
							<view class="t-o-e">{{sycjName}}</view>
						</view>
						<view class="f-x-bt c9">
							<view class="t-o-e">{{coupon.useExplain}}</view>
						</view>
					</view>
				</view>
				<view class="w100 p-a b0" style="height: 300rpx;" @click="confirm"></view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	import {
		mapState,
		mapActions
	} from 'vuex'
	export default {
		data(){
			return {
				bgImg:require('../src/img/newUser'),
				coupon:{},
			}
		},
		async onLoad(options) {
			console.log(options)
			this.getSystem()
			this.GetPxToRpxRate()
			this.query = options
			if (options && options.scene) {
				this.storeId = ''
				this.ldxId = decodeURIComponent(options.scene).split(',')[0]
				this.ldxType = decodeURIComponent(options.scene).split(',')[1]
				this.getLoginInfo({
					inviteId: this.query.userId || this.ldxId,
					type: this.ldxType
				}).then((res) => {
					console.log(this.user)
				})
			}
			this.getCoupon()
		},
		computed: {
			...mapState({
				pxToRpxRate:state=>state.systemInfo.pxToRpxRate,
			}),
			fullName() {
				return this.coupon.fullMoney > 0 ? `满${Number(this.coupon.fullMoney)}元可用` : '无门槛'
			},
			sycjName() {
				if (this.coupon.useType) {
					let obj = this.coupon.useType,
						arr = []
					if (obj.indexOf('1') > -1) {
						arr.push('外卖可用')
					}
					if (obj.indexOf('2') > -1) {
						arr.push('自取可用')
					}
					return arr.toString()
				}
			},
		},
		methods: {
			...mapActions(["setSystemInfo","getConfig"]),
			async GetPxToRpxRate(){
				if(!this.pxToRpxRate){
					await this.setSystemInfo()
				}
			},
			async getCoupon() {
				let {
					data
				} = await this.util.request({
					'url': this.api.getCoupon,
				})
				if (data) {
					this.coupon = data
				}
			},
			// backIN(){
			// 	this.go('reLaunch',`/yb_o2ov2/index/index`)
			// },
			confirm() {
				if(this.user.changeAt>0){
					this.util.modal('该活动仅限新用户参加~~','活动提示',{showCancel:false}).then(res=>{
						this.go('reLaunch',`/yb_o2ov2/index/index`)
					})
				}else{
					this.go('reLaunch',`/yb_o2ov2/my/login?backI=${1}`)
				}
			}
		},
		
	}
</script>

<style scoped lang="scss">
	.coupot{
		height: 220rpx;
		width: 490rpx;
		bottom: 420rpx;
		.fleft{
			width: 170rpx;
		}
		.colorR{
			color: #fa5555;
		}
	}
</style>
