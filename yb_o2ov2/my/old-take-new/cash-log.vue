<template>
	<view class="p30">
		<mg-cell @tab="go({url:'txxq?id='+v.id})" hc='1' class="" v-for="(v,i) in list" :key='i' isr='1' :btt='txType(v.type)'
		 bttc='f30' :bbt='`申请时间：${v.createdAt}`' ftc='f-s-1 t-r'>
			<template slot='ft'>
				<view class="f30 c3">-{{v.money}}</view>
				<view class="mt10 f26" :class="v.state==2?'c00':v.state==3?'crb':'cf3'">{{v.state==2?'已通过':v.state==3?'已拒绝':'待审核'}}</view>
			</template>
		</mg-cell>
		<mescroll-empty v-if="list.length===0" :option="{icon:'/static/empty/4.png',tip:'~ 暂无记录 ~'}"></mescroll-empty>
		<u-loadmore v-else @loadmore="nextPage" :status="status" />
		<!-- <jzz bgcolor="transparent" :nodata="dataList.length==0&&isget" :mygd='mygd' /> -->
	</view>
</template>

<script>
	import {
		mapState,
		mapActions
	} from 'vuex'
	import MgCell from '@/components/common/mg-cell.vue'
	export default {
		name: 'invite',
		components: {
			MgCell,
		},
		data() {
			return {
				params: {
					size: 10,
					page: 1,
					item: '',
				},
				list:[],
				status:'loadmore',
			}
		},
		async onLoad(op) {
			this.getSystem()
			this.util.setNT(op.t == 1 ? '老带新提现记录' : '分销提现记录')
			await this.getLoginInfo()
			this.params.item = op.t
			// this.params.state = op.t == 1 ? '2' : op.t == 2 ? '1' : ''
			this.fetchData()
		},
		computed: {},
		methods: {
			refresh(){
				this.params.page = 1
				this.fetchData()
			},
			async fetchData() {
				let {
					data
				} = await this.util.request({
					'url': this.api.memberWL,
					method: 'POST',
					data: this.params,
				})
				let list  = data
				if(this.params.page === 1){
						this.list = list
					}else{
						if(list.length < this.params.size){
							this.list = this.list.concat(list)
							this.status = 'nomore'
							return
						}
						this.list = this.list.concat(list)
					}
				this.status='loadmore'
			},
			nextPage(){
				if(this.status !== 'loadmore')return
				this.params.page++
				this.fetchData()
			},
			txType(v) {
				let n = ''
				switch (+v) {
					case 1:
						n = "微信";
						break;
					case 3:
						n = "银行卡";
						break;
					case 2:
						n = "付款宝";
						break;
					case 4:
						n = "余额";
						break;
				}
				return n
			},
		},
	}
</script>
<style scoped lang="scss">

</style>
