
<template>
	<view class="wh bf">
		<!-- 头部 start -->
		<view class="p-f w100 " :style="{top:0,height:`${statusNavBarHeight}px`,paddingTop:`${menuButtonTop}px`,zIndex:3,background:'#fff'}">
			<!-- 返回 -->
			<view class="p-a" :style="{top:`${menuButtonTop}px`,left:`20rpx`,zIndex:4}" @click="go('back')">
				<text class="iconfont iconback f40"></text>
			</view>
		</view>
		<!-- 头部 end -->
		<view class="p-r" :style="{paddingTop:`${statusNavBarHeight+28}px`}">
			<!-- 背景 -->
			<view class="p04 z9999 p-r">
				<view class="f50 c0 wei">手机号登录/注册</view>
				<view class="f36 c9 mb60">立即登录，享受优质服务</view>
			</view>
			<view class="p05 p-r" style="padding-top: 40rpx;">
				<!-- <view class="bsf mla" style="height: 166rpx;width: 166rpx;" v-if="!telLogin">
					<image class="wh" :src="tempUserInfo.portrait || system.icon"></image>
				</view> -->
				<!-- <view class="f38 t-c wei">{{system.name}}</view> -->
				<view class="mla loginbg p-a" style="height: 600rpx;width: 750rpx;">
					<image class="wh" src="/static/loginBg.png"></image>
				</view>
			</view>
			<view>
				<!-- <view class="p04 p-r">
					<view class="f50 c0 wei">手机号登录/注册</view>
				</view> -->
				<view class="p04">
					<view>
						<u-form :model="form" ref="uForm" :label-style="{fontWeight:'bold'}">
							<u-form-item  label-width="0"><u-input type="number" size="32" v-model="form.tel" placeholder="请填写手机号码" /></u-form-item>
							<u-form-item label-width="0">
								<view class="f-x-bt mt40">
									<u-input style="width: 290rpx;" type="number" size="32" maxlength='6' v-model="form.code" placeholder="请填写验证码" />
									<view>
										<view v-if="time<=0" class="codeBtn f-c f24 c3" @click="sendcode" :style="{color:form.tel && form.tel.length>7?fontColor:'',background:form.tel && form.tel.length>7?tColor:''}">
										{{btntxt}}
										<!-- <el-button :disabled="disabled" @click="sendcode" class="sendcode">{{btntxt}}</el-button> -->
										</view>
										<view v-else  class="codeBtn f-c f24 c3">已发送 {{btntxt}}</view>
									</view>
								</view>
							</u-form-item>
							<button class="btni cf f-c f32 wei sqdl" disabled="true"
								@click="ljdl" :style="{color:form.tel && form.tel.length>7 && form.code?fontColor:'',background:form.tel && form.tel.length>7 && form.code?tColor:''}">立即登录</button>
						</u-form>
					</view>
				</view>
			</view>
		</view>
		<!-- 底部协议 -->
		<view class="w100 t-c f24 mb30 mt20 p2">
			<view class="flex">
				<u-checkbox-group>
					<u-checkbox v-model="form.sex" label-size="24" shape="circle">未注册手机号登录后将自动生成账号且代表您已阅读并同意</u-checkbox>
				</u-checkbox-group>
				<!-- <text class="c6">未注册手机号登录后将自动生成账号且代表您已阅读并同意</text> -->
			</view>
			<view>
				<text :style="{color:tColor}" @click="go('navigateTo',`/yb_o2ov2/my/other/gywm?t=${'用户服务协议'}&p=${1}`)">《用户服务协议》、</text>
				<text :style="{color:tColor}" @click="go('navigateTo',`/yb_o2ov2/my/other/gywm?t=${'隐私权政策'}&p=${2}`)">《隐私权政策》</text>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapMutations,
		mapActions
	} from 'vuex'
	import utils from '@/common/utils.js'
	export default {
		name: 'sq-login',
		data() {
			return {
				loading: false,
				loginShow:false, // false 没有获取到用户头像信息
				telLogin:false,
				tempUserInfo:{},
				form: {
					tel:'',
					code:'',
					sex:true,
				},
				disabled:false,
				time:0,
				btntxt:"获取验证码",
			}
		},
		onLoad() {
			this.util.setNT('登录')
			//没有绑定手机号 绑定手机号
			if(this.isLogin && !this.user.userTel){
				this.loginShow = true
			}
		},
		methods: {
			sendcode(){
			 this.time=60;
			 this.timer(); 
			 },
			 //发送手机验证码倒计时
			 timer() {
			 if (this.time > 0) {
			  this.disabled=true;
			  this.time--;
			  this.btntxt=this.time+"秒";
			  setTimeout(this.timer, 1000);
			 } else{
			  this.time=0;
			  this.btntxt="发送验证码";
			  this.disabled=false;
			 }
			 },
		}
	}
</script>
<style scoped lang='scss'>
	.snbtnc {
		margin-top: 20rpx;
	}

	.btni {
		margin-top: 30rpx;
		border: 1px solid #000;
		height: 100rpx;
		border-radius: 10rpx;
	}
	.btni-login{
		margin-top: 30rpx;
		height: 100rpx;
		border-radius: 20rpx;
	}
	.loginbg{
		top: -240rpx;
		left: 0;
	}
	.sqdl{
		border-radius: 60rpx;
		border: none;
		margin-top: 120rpx;
	}
	.codeBtn{
		width: 180rpx;
		height: 60rpx;
		border-radius: 60rpx;
		background-color: #f7f7f7;
		color: rgba(0,0,0,.3);
	}
</style>
