<!-- app登录 需要另外设置 -->
<!-- 除了微信小程序外 其他登录逻辑需要验证 TODO -->

<template>
  <view class="wh bf">
    <!-- 返回 -->
    <view class="p3" @click="go('back', '/yb_o2ov2/my/index')">
      <text class="iconfont iconback f40"></text>
    </view>
    <!-- 头部 end -->
    <view class="p-r">
      <!-- 背景 -->
      <view class="p04 z9999 p-r">
        <view class="f50 c0 wei">{{ $t("login.login_header_title") }}
          {{ $t("login.system_name") }}</view>
        <view class="f36 c9 mb60">{{ $t("login.authorize") }}</view>
      </view>
      <view class="p05 p-r" style="padding-top: 60rpx">
        <view class="mla" style="height: 166rpx; width: 166rpx; z-index: 3">
          <image class="wh" src="/static/line.svg"></image>
        </view>
        <!-- <view class="f38 t-c wei">{{system.name}}</view> -->
        <view class="mla loginbg p-a" style="height: 600rpx; width: 750rpx">
          <image class="wh" src="/static/loginBg.png"></image>
        </view>
      </view>
      <view class="p05" style="margin-top: 180rpx">
        <view>
          <view class="f28 c0">{{ $t("login.info_text") }}：</view>
          <view class="c6 f24 mt10">· {{ $t("login.info") }}</view>
          <view class="snbtnc">
            <button class="btni cf f-c f32 wei sqdl" :style="{ color: '#fff', background: '#FFCC00' }"
              @click="lineLogin">
              {{ $t("login.authorized_login") }}
            </button>
            <!-- LINE登录按钮 -->
            <!-- <button class="btni cf f-c f32 wei sqdl mt20" style="background: #06C755;"
							@click="lineLogin">{{$t('login.line_login') || 'LINE登录'}}</button> -->
          </view>
        </view>
      </view>
    </view>
    <!-- 底部协议 -->
    <view class="w100 t-c f24 mb30 foot-btnc f-c">
      <view class="flex">
        <u-checkbox-group>
          <u-checkbox v-model="form.ty" label-size="24" shape="circle" active-color="#FFCC00">{{ $t("login.agree_text")
            }}</u-checkbox>
        </u-checkbox-group>
      </view>
      <!-- <text class="c6">登录代表您已同意</text> -->
      <text :style="{ color: '#FFCC00' }" @click="
        go(
          'navigateTo',
          `/yb_o2ov2/my/other/gywm?t=${$t('login.agree1')}&p=${1}`
        )
        ">《{{ $t("login.agree1") }}》</text>
      <text :style="{ color: '#FFCC00' }" @click="
        go(
          'navigateTo',
          `/yb_o2ov2/my/other/gywm?t=${$t('login.agree2')}&p=${2}`
        )
        ">《{{ $t("login.agree2") }}》</text>
    </view>

    <!-- 手机号更换弹窗 -->
    <u-popup v-model="showPhoneModal" mode="center" border-radius="20" width="600rpx">
      <view class="phone-modal">
        <view class="modal-content">
          <view class="current-phone">
            <text class="label">{{ $t('address.phone') }}</text>
            <u-input class="phone-number" v-model="userTel" :placeholder="$t('user.enter_phone')"></u-input>
          </view>
        </view>
        <view class="modal-footer">
          <button class="confirm-btn" @click="confirmChangePhone">{{ $t('common.showModal_confirmText') }}</button>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { mapState, mapMutations, mapActions } from "vuex";
import utils from "@/common/utils.js";
export default {
  name: "sq-login",
  data() {
    return {
      loading: false,
      loginShow: false, // false 没有获取到用户头像信息
      tempUserInfo: {},
      form: {
        tel: "",
        code: "",
        ty: true,
      },
      query: "",
      // LINE登录配置
      lineClientId: "", // LINE Channel ID
      lineRedirectUri: "", // 回调地址
      scope: "profile openid email",
      // 手机号更换弹窗
      showPhoneModal: false,
      userTel: '',
      hasHandledLineCode: false,
    };
  },
  onLoad(options) {
    console.log("options", options, process.env.NODE_ENV);
    if (options && options.backI) {
      this.query = options;
    }
    this.util.setNT("登录");
    //没有绑定手机号 绑定手机号
    if (this.isLogin && !this.user.userTel) {
      this.loginShow = true;
    }
    // 初始化LINE登录配置
    this.initLineLogin();

    // 检查URL中是否包含LINE登录回调的code
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get("code");
    if (code && !this.hasHandledLineCode) {
      this.hasHandledLineCode = true;
      console.log("Found LINE code:", code);
      this.handleLineCallback(code);
    }
  },
  onShow() {
    // 检查URL中是否包含LINE登录回调的code
    // const urlParams = new URLSearchParams(window.location.search);
    // const code = urlParams.get("code");
    // if (code) {
    //   console.log("Found LINE code in onShow:", code);
    //   this.handleLineCallback(code);
    // }
  },
  methods: {
    // 初始化LINE登录配置
    initLineLogin() {
      this.lineClientId = "2007525457";
      const currentUrl =
        window.location.origin +
        (process.env.NODE_ENV !== "development" ? "/h5" : "") +
        "/#/yb_o2ov2/my/login";
      this.lineRedirectUri = currentUrl;
      console.log("LINE redirect URL:", this.lineRedirectUri);
    },
    // LINE登录
    lineLogin() {
      if (!this.form.ty) {
        uni.showModal({
          title: "温馨提示",
          content: "请认真阅读并同意用户服务协议",
          showCancel: false,
        });
        return;
      }
      // 构建LINE OAuth 2.0 授权URL
      const lineAuthUrl = `https://access.line.me/oauth2/v2.1/authorize?response_type=code&client_id=${this.lineClientId}&redirect_uri=${encodeURIComponent(this.lineRedirectUri)}&state=${Date.now()}&scope=${this.scope}`;

      // 在H5环境下打开LINE授权页面
      window.location.href = lineAuthUrl;
    },
    // 处理LINE登录回调
    async handleLineCallback(code) {
      if (!code) return;

      // if (process.env.NODE_ENV === "development") {
      //   // 按照正确的数据格式存储用户信息
      //   this.refreshUser({
      //     nickname: "zhong333",
      //     userTel: "13800138000",
      //     userName: "用户6164b3f52f",
      //     email: null,
      //     portrait: "https://profile.line-scdn.net/0h9iG6B_W3ZkZqT3hgH7sYeBofZSxJPj9UFS18cg9GO3RTdylCQX0udQhIaHYAfCYVRXl5dAhLPnRIGVwZRFVCRhYsUB8XN3QYMXFtUAROSj0iGVRSJFNadyUQUx9TfEdILixgcl4zMWpUNHFEJE9PRhYGYy8pHl8YORgKEG99CMUFTRETRygvJFxLPXLR",
      //     realName: null,
      //     birthday: null,
      //     sex: null,
      //     ucoin: "0.00",
      //     loginAt: "1749175672",
      //     token: "cfvBplcw6GGG9oobZwHSS3VdXyTF13gM"
      //   });

      //   uni.setStorageSync("token", "cfvBplcw6GGG9oobZwHSS3VdXyTF13gM");
      //   this.util.message("登录成功", 1, 1000);

      //   // 用户选择更换手机号，显示弹窗
      //   this.userTel = '13800138000';
      //   this.showPhoneModal = true;

      //   // uni.navigateBack({ delta: 1 });
      //   return;
      // }

      try {
        console.log("Processing LINE code:", code);
        const response = await this.util.request({
          url: this.api.lineLogin,
          method: "GET",
          data: {
            code,
            redirectUri: this.lineRedirectUri,
          },
        });

        console.log("LINE login response:", response);
        if (response && response.code === 1 && response.data ) {
          // 按照正确的数据格式存储用户信息
          this.refreshUser({
            nickname: response.data.nickname || '',
            userTel: response.data.userTel || '', // 设置手机号，用于触发isLogin
            userName: response.data.userName || '',
            email: response.data.email || null,
            portrait: response.data.portrait || '',
            realName: response.data.realName || null,
            birthday: response.data.birthday || null,
            sex: response.data.sex || null,
            ucoin: response.data.ucoin || '0.00',
            loginAt: response.data.loginAt || '',
            token: response.data.token
          });

          if(response.data.token){
            uni.setStorageSync("token", response.data.token);
            this.util.message("登录成功", 1, 1000);
          }

          // 用户选择更换手机号，显示弹窗
          this.userTel = response.data.userTel;
          this.showPhoneModal = true;
          // uni.navigateBack({ delta: 1 });
        } else {
          throw new Error(response?.msg || "登录失败");
        }
      } catch (error) {
        console.error("LINE login error:", error);
        uni.showModal({
          title: "温馨提示",
          content: error.message || "LINE登录失败，请重试",
          showCancel: false,
        });
      }
    },
    //弹窗 得到用户头像，昵称
    getAvatar() {
      if (!this.form.ty) {
        uni.showModal({
          title: "温馨提示",
          content: "请认真阅读并同意用户服务协议",
          showCancel: false,
        });
        return;
      }
      uni.getUserProfile({
        desc: "用于完善用户资料",
        success: (result) => {
          // 将信息临时保存在本页面
          this.tempUserInfo = {
            portrait: result.userInfo.avatarUrl,
            userName: result.userInfo.nickName,
          };
          // 成功获取用户头像,昵称后 进入下一步
          this.loginShow = true;
          // console.log('getUserInfo success', result);
        },
        fail: (error) => {
          console.log("getUserInfo fail", error);
          uni.showModal({
            title: "温馨提示",
            content: "获取头像等信息失败",
            showCancel: false,
          });
        },
      });
    },
    login() {
      //收集用户头像、昵称、手机号 上报登录
      this.refreshUser(this.tempUserInfo);
      // this.go('back')
    },
    async decryptPhoneNumber(result) {
      // console.log(this.user)
      if (!this.form.ty) {
        uni.showModal({
          title: "温馨提示",
          content: "请认真阅读并同意用户服务协议",
          showCancel: false,
        });
        return;
      }
      if (result.detail.errMsg == "getPhoneNumber:ok") {
        let sessionKey = getApp().globalData.session_key,
          data = result.detail.encryptedData,
          iv = result.detail.iv;
        let { data: userTel } = await this.util.request({
          url: this.api.jm,
          method: "POST",
          data: {
            sessionKey,
            data,
            iv,
          },
        });
        this.tempUserInfo.userTel = userTel;
        this.login();
        // this.go('back')
        this.util.message("绑定成功", 1, 1000);
        if (this.query && this.query.backI) {
          this.go("reLaunch", `/yb_o2ov2/index/index`);
        } else {
          utils.swnb(1000);
        }
      } else {
        uni.showModal({
          title: "温馨提示",
          content: "授权手机号失败",
          showCancel: false,
        });
        return;
      }
    },
    // 确认更换手机号
    async confirmChangePhone() {
      if (!this.userTel) {
        this.util.message(this.$t('user.enter_phone'), 3)
        return
      }
      await this.util.request({
        url: this.api.xgyh,
        method: 'POST',
        data: {
          userTel: this.userTel
        }
      }).then(res => {
        if (res.code == 1) {
          this.util.message(res.msg || '修改成功', 3)
          this.showPhoneModal = false;
          this.go('reLaunch', '/yb_o2ov2/my/index')
        }
      })
    },
  },
};
</script>
<style scoped lang='scss'>
.snbtnc {
  margin-top: 20rpx;
}

.btni {
  margin-top: 30rpx;
  border: 1px solid #000;
  height: 100rpx;
  border-radius: 10rpx;
}

.btni-login {
  margin-top: 30rpx;
  height: 100rpx;
  border-radius: 20rpx;
}

.loginbg {
  top: -240rpx;
  left: 0;
}

.sqdl {
  border-radius: 60rpx;
  border: none;
}

.codeBtn {
  width: 180rpx;
  height: 60rpx;
  border-radius: 60rpx;
}

/* 手机号更换弹窗样式 */
.phone-modal {
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;

  .modal-content {
    padding: 60rpx 40rpx 40rpx;

    .current-phone {
      display: flex;
      flex-direction: column;
      align-items: center;

      .label {
        font-size: 32rpx;
        color: #333;
        font-weight: 500;
        margin-bottom: 30rpx;
      }

      .phone-number {
        width: 100%;
        font-size: 36rpx;
        color: #007aff;
        text-align: center;
        padding: 20rpx;
        background: #f8f9fa;
        border-radius: 12rpx;
        border: 2rpx solid #e9ecef;
      }
    }
  }

  .modal-footer {
    padding: 20rpx 40rpx 40rpx;
    display: flex;
    justify-content: center;

    .confirm-btn {
      width: 200rpx;
      height: 80rpx;
      background: #007aff;
      color: #fff;
      border: none;
      border-radius: 40rpx;
      font-size: 32rpx;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;

      &:active {
        background: #0056b3;
      }
    }
  }
}
</style>