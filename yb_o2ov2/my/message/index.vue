<template>
	<view class="bf">
		<!-- 头部 start -->
		<view class="p-f w100 " :style="{top:0,height:`${statusNavBarHeight}px`,paddingTop:`${menuButtonTop}px`,zIndex:3,background:'#fff'}">
			<!-- 返回 -->
			<view class="p-a" :style="{top:`${menuButtonTop}px`,left:`20rpx`,zIndex:4}" @click="go('back')">
				<text class="iconfont iconback f40"></text>
			</view>
			<view class="t-c f32 wei">
				<view>消息中心</view>
			</view>
		</view>
		<!-- 头部 end -->
		<view class="p2" :style="{paddingTop:`${statusNavBarHeight}px`}">
			<view v-for="item in list" :key="item.storeId" class="f-x-bt p20" style="border-top: 1rpx solid #ededed;" 
			@click="go('navigateTo',`/yb_o2ov2/my/message/chat?storeId=${item.storeId}`)">
				<view class="bsf mr20" style="width: 100rpx;height: 100rpx;"><image class="wh" :src="item.icon" mode=""></image></view>
				<view class="f-g-1 f-col">
					<view class="f-bt">
						<view class="f28 wei t-o-e">{{item.name}}</view>
						<view class="f24 c9 f-s-0">{{getDay(item.createdAt)}}</view>
					</view>
					<view style="width: 500rpx;" class="c9 t-o-e">{{item.content.startsWith('https://img')?'[图片]':item.content}}</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data(){
			return {
				query:{
					page:1,
					size:100,
					chatType:1 // 1用户查看商店   2商店查看用户
				},
				list:[]
			}
		},
		onShow(){
			this.fetchData()
		},
		methods: {
			async fetchData() {
				let { data } = await this.util.request({
					'url': this.api.getChatList,
					data:this.query
				})
				this.list = data
			},
			getDay(day){
				const date = new Date(day*1000)
				const week = '日一二三四五六'.charAt(date.getDay())
				let hours = toDouble(date.getHours())
				let minutes = toDouble(date.getMinutes())
				function toDouble(s){
					return ('0'+s.toString()).substr(-2)
				}
				return `周${week} ${hours}:${minutes}`
			},
		},
	}
</script>

<style>
</style>
