<template>
	<view>
		<!-- 头部 start -->
		<view class="posi-s w100 "
			:style="{top:0,height:`${statusNavBarHeight}px`,paddingTop:`${menuButtonTop}px`,zIndex:3,background:'#fff'}">
			<!-- 返回 -->
			<view class="p-a" :style="{top:`${menuButtonTop}px`,left:`20rpx`,zIndex:4}" @click="go('back')">
				<text class="iconfont iconback f40"></text>
			</view>
			<view class="f32 wei t-c">{{storeName}}</view>
		</view>
		<!-- 头部 end -->
		<!-- 聊天窗 -->
		<scroll-view @scrolltoupper="getHisLog" scroll-y="true" class="p02" scroll-with-animation :scroll-top="bottomIndex"
			:style="{height:`${scrollHeight}px`}" @click="disableShow">
			<view class="pt20">
				<view v-for="(item,index) in list" :key="index">
					<!-- 时间 todo -->
					<view class="time" v-if="item.type === 'timeStamp'"><text class="stamp">{{item.content}}</text>
					</view>
					<!-- 用户发送消息 -->
					<block v-else-if="item.type === chatConfig.userType">
						<!-- 用户发送消息 -->
						<view class="right">
							<view class="content">
								<template v-if="item.msgType === 'text'">
									<view>{{item.content}}</view>
								</template>
								<template v-else-if="item.msgType === 'image'">
									<view class="flex bs10" style="width: 350rpx;">
										<!-- <image class="w100" @load="toBottom" :src="item.content" mode="widthFix"> -->
										<image class="w100" :src="item.content" mode="widthFix">
										</image>
									</view>
								</template>
							</view>
							<view class="bsf ml20 f-s-0" style="width: 80rpx;height: 80rpx;">
								<image class="wh" :src="user.portrait" mode=""></image>
							</view>
						</view>
					</block>
					<!-- 对方发消息 -->
					<block v-else>
						<!-- 商户 -->
						<view class="left">
							<view class="bsf mr20 f-s-0" style="width: 80rpx;height: 80rpx;">
								<image class="wh" :src="storeAvatar" mode=""></image>
							</view>
							<view class="content">
								<!-- 发送文本 -->
								<template v-if="item.msgType === 'text'">
									<view>{{item.content}}</view>
								</template>
								<!-- 发送图片 -->
								<template v-else-if="item.msgType === 'image'">
									<view class="flex bs10" style="width: 350rpx;">
										<image class="w100" :src="item.content" mode="widthFix"></image>
									</view>
								</template>
							</view>
						</view>
					</block>
				</view>
			</view>
		</scroll-view>
		<!-- 底部输入框 快捷回复 -->
		<view class="p-f w100 bf" style="bottom:0">
			<view class="pt30" :style="{height:`${inputBoxHeight}rpx`}">
				<view class="f-x-ad pb40" style="border-bottom: 1rpx solid #f5f6f9;">
					<input v-model="value" :adjust-position="false" class="input" cursor-spacing="20"
						placeholder-style="line-height:70rpx" confirm-type="send" type="text" placeholder="输入消息..."
						@focus="focus" @confirm="sendMessage" @keyboardheightchange="handlekeyboardheightchange" />
					<view style="line-height: 70rpx;" @click.stop="showEmoji = true"><text
							class="iconfont icontchat1 fc-icon"></text></view>
					<view style="line-height: 70rpx;" @click.stop="open = true"><text
							class="iconfont icontchat3  fc-icon"></text></view>
					<view style="line-height: 70rpx;" @click="sendImg"><text
							class="iconfont icontchat2  fc-icon"></text></view>
				</view>
			</view>
			<view class="p-r" style="transition: .2s ease-in-out;" :style="{height:`${bottomHeight}rpx`}">
				<!-- 表情 -->
				<block v-if="showEmoji">
					<u-swiper height="350" custom :list="emojiPage" mode="round" bg-color="transparent"
						:autoplay="false">
						<template v-for="(item,index) in emojiPage" class="p02" :slot="'element-'+index">
							<view :key="index" class="f-x-bt f-w">
								<view v-for="e in item" :key="e" class="f60 t-c" style="width: 80rpx;"
									@click="value +=e.emoji">
									{{e.emoji}}
								</view>
							</view>
						</template>
					</u-swiper>
				</block>
				<!-- 快捷回复 -->
				<block v-if="open">
					<scroll-view scroll-y="true" class="wh">
						<view v-for="item in 8" :key="item" class="t-c wei p30"
							style="border-bottom: 1rpx solid #f5f6f9;" @click="quickReply('请问门店位置在哪？')">
							<text v-if="open">请问门店位置在哪？</text>
						</view>
					</scroll-view>
				</block>

			</view>
			<!-- 安全区域 -->
			<view class="w100" style="height: 40rpx;"></view>
		</view>
	</view>
</template>

<script>
	const wsUrl=getApp().globalData.siteInfo.siteroot.replace(/(https|http)/,'wss')
	import Socket from "./socket.js"
	import {
		uploadImg
	} from '@/common/wechat-util.js'
	export default {
		data() {
			return {
				chatConfig: {
					userType: 1, //1用户  2商户  3骑手
					url: `${wsUrl}:6388`,
					userId: '',
					storeId: ''
				}, // 聊天配置
				emojiPage: [], // 表情分页
				showEmoji: false, //表情 show
				query: { //查询消息记录参数
					page: 1,
					size: 20,
				},
				storeName: '',
				storeAvatar: '',
				socket: null, // websocket
				bottomIndex: 0, // 滚动条位置
				open: false, // 快捷回复 show
				inputBoxHeight: 150, //输入框高度
				value: '', //输入框的值
				bottomHeight: 0, //弹出键盘时底部UI位移
				list: [], //聊天记录
				//list {
				// 	type:msg.type,
				// 	msgType:msg.msgType,
				// 	content:msg.content
				// }

			}
		},
		onUnload() {
			// 页面销毁 关闭socket
			this.socket.close()
		},
		onLoad(option) {
			//用户端逻辑
			const app = getApp().globalData
			this.query.storeId = option.storeId
			this.chatConfig.uniacid = app.siteInfo.uniacid
			this.chatConfig.storeId = option.storeId
			this.chatConfig.userId = uni.getStorageSync('userId')
			//商户端逻辑
			// const account = uni.getStorageSync('account')
			// this.query.userId = option.userId
			// this.chatConfig.userId = option.userId
			// this.chatConfig.uniacid = account.uniacid
			// this.chatConfig.storeId=account.storeId
			this.init()
		},
		watch: {
			open(val) {
				if (val) {
					this.bottomHeight = 400
					this.showEmoji = false
				}
			},
			showEmoji(val) {
				if (val) {
					this.bottomHeight = 350
					this.open = false
				}
			}
		},
		computed: {
			length() {
				return this.list.length
			},
			scrollHeight() {
				return this.wHeight - this.statusNavBarHeight - (this.bottomHeight + this.inputBoxHeight + 40) / this
					.pxToRpxRate
			}
		},

		methods: {
			focus() {
				this.open = false
				this.showEmoji = false
			},
			disableShow() {
				this.open = false
				this.showEmoji = false
				this.bottomHeight = 0
			},
			async init() {
				this.getEmoji()
				this.fetchMsgLog()
				this.initScoket()
				this.getStoreInfo()
			},
			//获取商店消息
			async getStoreInfo() {
				let { data } = await this.util.request({
					url: this.api.getStoreInfo,
					method: 'GET',
					data: {
						storeId: this.chatConfig.storeId,
						lat: this.latLng.latitude,
						lng: this.latLng.longitude
					}
				})
				const { shopData } = data
				this.storeName = shopData.name
				this.storeAvatar = shopData.icon
			},
			// 获取表情list
			async getEmoji() {
				let { data } = await this.util.request({
					'url': this.api.getEmojiList
				})
				let list = Object.values(data)
				let length = Math.ceil(list.length / 24)
				for (let i = 0; i < length; i++) {
					this.emojiPage[i] = list.slice(i * 24, (i + 1) * 24)
				}
				this.emoji = data
			},
			// 获取聊天记录
			async fetchMsgLog(type='init') {
				let { data } = await this.util.request({
					'url': this.api.chatRecord,
					data: this.query
				})
				const isInit = type === 'init'
				const method = isInit?'push':'unshift'
				let timeStap = 0
				// new Date(timeStamp).format("yyyy-MM-dd")
				const DIFF = 60 * 5 * 1000
				const ALLDAY = 60*60*24 * 1000
				isInit && data.reverse()
				data.forEach(log=>{
					const AT = log.createdAt * 1000
					const isNeedStap = AT - timeStap >= DIFF
					const isOverDay = AT - timeStap >  ALLDAY
					if(isNeedStap){
						if(!isInit&&(timeStap == 0)){
							timeStap = AT
						}else{
							timeStap = AT
							this.list[method]({
								type: 'timeStamp',
								content:isOverDay? new Date(AT).format("MM-dd hh:mm"): new Date(AT).format("hh:mm")
							})
						}
						
					}
					this.list[method]({
						type: Number(log.chatType),
						msgType: Number(log.contentType) === 1 ? 'text' : 'image',
						content: log.content
					})
				})
				if(!isInit) return
				this.toBottom()
			},
			async getHisLog(){
				this.query.page++
				await this.fetchMsgLog('history')
				// this.$nextTick(() => {
				// 	let query = uni.createSelectorQuery()
				// 	query.selectAll('.content').boundingClientRect((res) => {
				// 		this.bottomIndex = res[20].top
				// 	}).exec()
				// })
			},
			//初始化socket连接
			initScoket() {
				this.socket = new Socket(this.chatConfig)
				//监听消息回调
				this.socket.onMessage((msg) => {
					console.log('回调事件', msg)
					if (msg.userId == this.chatConfig.userId && msg.storeId == this.chatConfig
						.storeId) { //聊天没有做分池  所有人都在一个聊天室  需要用storeId和userId 来控制展示
						this.list.push({
							type: msg.type,
							msgType: msg.msgType,
							content: msg.content
						})
						setTimeout(()=> {
							this.toBottom()
						}, 150)
						// this.toBottom()
					}
				})
			},
			handlekeyboardheightchange(e) {
				if (e.detail.height !== 0) {
					this.bottomHeight = e.detail.height * this.pxToRpxRate
				}
			},
			sendMessage() {
				this.disableShow()
				if (this.value.trim() === '') return
				this.sendMsg(this.value, 'text')
			},
			quickReply(c) {
				this.value = c
			},
			sendImg() {
				uni.chooseImage({
					count: 1,
					sizeType: ['compressed'],
					success: async res => {
						let imgUrls = await uploadImg({
							files: [res.tempFilePaths[0]],
							formData: { imageType: 1 }
						})
						this.sendMsg(imgUrls[0], 'image')
					}
				})
			},
			sendMsg(content, msgType) {
				this.socket.sendMsg(content, msgType)
				this.localPush(content, msgType)
			},
			localPush(content, msgType) {
				//type 1用户端   2系统后台  3骑手端
				//msgType 'text' 'image'
				this.list.push({
					type: this.chatConfig.userType,
					msgType,
					content
				})
				this.value = ''
				setTimeout(()=> {
					this.toBottom()
				}, 150)
				// this.toBottom()
			},
			toBottom() {
				//收到消息滑动到底
				// this.bottomIndex = 'index' + (this.list.length)
				this.$nextTick(() => {
					let query = uni.createSelectorQuery()
					query.selectAll('.content').boundingClientRect((res) => {
						this.bottomIndex = this.wHeight * this.list.length
					}).exec()
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	.fc-icon {
		font-weight: bold;
		padding: 10rpx;
		border: 1px solid #f5f5f5;
		color: #000;
		border-radius: 50%;
		font-size: 48rpx;
	}

	.time {
		width: 100%;
		padding: 20rpx 0;
		text-align: center;
		.stamp {
			color: #fff;
			background: #dedede;
			border-radius: 10rpx;
			padding: 0 6rpx;
		}
	}

	.left {
		display: flex;
		justify-content: flex-start;
		margin-top: 20rpx;

		.content {
			display: flex;
			position: relative;
			font-size: 30rpx;
			background-color: #fff;
			padding: 20rpx;
			border-radius: 20rpx;

			&:after {
				content: "";
				position: absolute;
				width: 0;
				height: 0;
				left: -10rpx;
				border-top: 10rpx solid transparent;
				border-bottom: 10rpx solid transparent;
				border-right: 10rpx solid #FFFFFF;
			}
		}

	}

	.right {
		display: flex;
		justify-content: flex-end;
		margin-top: 20rpx;

		.content {
			display: flex;
			position: relative;
			font-size: 30rpx;
			background-color: #ecd079;
			padding: 20rpx;
			border-radius: 20rpx;

			&:after {
				content: "";
				position: absolute;
				width: 0;
				height: 0;
				right: -10rpx;
				border-top: 10rpx solid transparent;
				border-bottom: 10rpx solid transparent;
				border-left: 10rpx solid #ecd079;
			}
		}
	}

	.input {
		width: 450rpx;
		height: 70rpx;
		background: #f5f6f9;
		border-radius: 35rpx;
		padding: 10rpx 20rpx;
	}
</style>
