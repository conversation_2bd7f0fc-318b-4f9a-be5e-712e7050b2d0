import {message} from '@/common/we7/util';

export default class Socket {
	constructor(config){
		this.$config = config
		this.user = {
			msgType:'',
			uniacid:config.uniacid,
			userId:config.userId,
			storeId:config.storeId,
			type:config.userType
		}
		this.sTask = null
		this.init()
	}
	init(){
		let o = {
			url:this.$config.url,
			success:(res)=>{
				//console.log('创建socket成功',res)
				},
			fail:(err)=>{
				//console.log('创建socket失败：',err)
				},
		}
		this.sTask =  uni.connectSocket(o)
		this._onSocketOpened()
	}
	
	_reconnect(){
		this.init()
		//更新onMessage
		this.onMessage(this.$cb)
	}
	
	onMessage(cb){
		this.$cb = cb
		this.sTask.onMessage(res=>{
			if(res.data === 'success'){
				//心跳检测
			}else{
				const ms = JSON.parse(res.data)
				if(ms.msgType !== 'login'){
					cb(ms)
				}
			}
		})
	}
	
	//重置心跳
	_reset(){
		clearTimeout(this._timeOutHeartBeat)
		return this
	}
	
	//心跳检测
	_start(){
		this._timeOutHeartBeat = setInterval(()=>{
			this.sTask.send({
				data:'heartbeat',
				success:res=>{
					// console.log('心跳检测')
				},
				fail:err=>{
					// console.log(err)
					this._reconnect()
				}
			})
		},3000)
	}
	
	_onSocketOpened(){
		// 创建socket监听事件
		this.sTask.onOpen(res=>{
			console.log('连接聊天室成功：',res)
			//发送登录信息
			this.sendMsg('','login')
			//心跳检测
			this._reset()._start()
			})
		this.sTask.onClose(res=>{
			const code = res.code
			if(code === 1006)
				message(`聊天服务未开启：${code}`, 3)
			})
		this.sTask.onError(res=>{console.log(res)})
	}
	
	sendMsg(content,type){
		let message = this.user
		message.msgType = type
		message.content = content
		message = JSON.stringify(message)
		console.log('msg:',message)
		this.sTask.send({
			data:message,
			success:res=>{
				// console.log('发送成功：',res)
			},
			fail:err=>{
				console.log('发送失败：',err)
			}
		})
	}
	
	close(){
		this._reset()
		uni.closeSocket({
			success:res=>{console.log(res)}
		})
	}
}