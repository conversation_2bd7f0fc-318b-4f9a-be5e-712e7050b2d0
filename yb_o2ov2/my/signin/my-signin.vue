<template>
	<view class="mh100 pb115">
		<view class="bf mb30" v-if="type=='1'">
			<view class="f-c-c p30">
				<view class="f28">当前{{jfName}}</view>
				<view class="f56 cfc wei" :style="{color:tColor}">{{user.integral}}</view>
			</view>
			<view class="f-x-bt">
				<view class="f-c p3" @click='integralMall'>
					<text class="iconfont iconjfsc mr10 cfc f34"></text>
					<text>{{jfName}}商城</text>
				</view>
				<view class="f-c p3" @click='integralRule'>
					<text class="iconfont iconjfgz mr10 cfc f34"></text>
					<text>{{jfName}}规则</text>
				</view>
			</view>
		</view>
		<view class="bf">
			<view class="f32 wei p3 hlb">签到记录</view>
			<view class='p3 bf hlb' v-for="(v,i) in list" :key="i">
				<view class="f-x-bt">
					<view class='t-o-e f28'>{{v.note}}</view>
					<view class="cfc f28">+{{v.integral}}</view>
				</view>
				<view class="f-x-bt">
					<view class='c9 f24 m10'>{{v.createdAt}}</view>
				</view>
			</view>
		</view>
		<mescroll-empty v-if="list.length===0" :option="{icon:'/static/empty/9.png',tip:'~ 暂无订单 ~'}"></mescroll-empty>
		<u-loadmore v-if="list.length!==0" @loadmore="nextPage" :status="status" />
	</view>
</template>

<script>
	import {
		mapState,
		mapActions
	} from 'vuex'
	import {
		sljz
	} from "@/common/util-mixins.js"
	export default {
		name: 'mySignin',
		components: {
			
		},
		data() {
			return {
				params: {
					page: 1,
					size: 10,
				},
				refreshLoading:false,
				status:'loading',
				list: [],
				integral: '',
				type: '',
			}
		},
		onLoad(options) {
			this.getSystem(this)
			this.util.setNT('帳戶' + (options.type == 1 ? this.jfName : '签到'))
			this.integral = this.user.integral
			this.getList()
			this.type = options.type
		},
		mixins: [sljz],
		computed: {
			jfName() {
				return this.system.custom.integral
			},
		},
		onShow() {
			if (this.uId) {
				this.refreshUser({
					nomask: 1,
					get: 1,
					now: 1,
				})
			}
		},
		methods: {
			async refresh() {
				this.refreshLoading = true
				this.params.page = 1
				await this.getList()
				setTimeout(()=>{
					this.refreshLoading = false
				},500)
			},
			async getList(type) {
				this.status = 'loading'
				let {
					data
				} = await this.util.request({
					'url': this.api.wdqdjl,
					data: this.params,
				})
				if(type !=='nextPage' || this.params.page === 1){
					this.list = data
				}else{
					if(data.length === 0){
						this.status = 'nomore'
						return
					}
					this.list = this.list.concat(data)
				}
				this.status = 'loadmore'
			},
			nextPage() {
				if (this.status === 'loading') { return }
				this.params.page++
				this.getList('nextPage')
			},
			integralMall() {
				this.go('redirectTo','/yb_o2ov2/my/integral/integral-mall')
			},
			integralRule() {
				this.go('navigateTo',`/yb_o2ov2/my/other/gywm?t=${this.jfName}规则&p=4`)
			},
		},
	}
</script>

<style scoped lang="scss">
	.cfc {
		color: #333ABE;
	}
</style>
