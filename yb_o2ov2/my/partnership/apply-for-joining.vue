<template>
	<view v-if="src">
		<web-view :webview-styles="webviewStyles" :src="src" ></web-view>
	</view>
	<!-- <view class="p3 bf" style="padding-bottom: 100rpx;">
		<view>
			<view class="f40 wei">入驻说明</view>
			<view class="c9 f24">申请开店过程中需要您提供符合国家法律规定的经营许可证照，包括但不限于营业执照、各类许可证、特许证件等;</view>
		</view>
		<view class="pl20">
			<u-form :model="form" ref="uForm" label-width="260" :label-style="{fontSize:'30rpx',color:'#000'}">
				<view class="title mt30">店铺信息</view>
				<u-form-item required label="商店名称:" ><u-input class="c9" size="30" placeholder-style="font-size:30rpx" v-model="form.no" placeholder="请输入商店名称" /></u-form-item>
				<u-form-item required label="商店logo:" >
					<view @click="chooseImg" class="f-c-c bs10 mt10" style="border: 1rpx dashed #bbb;width: 150rpx;height: 150rpx;">
						<view class="wh"><image class="wh" :src="form.logo" mode=""></image></view>
					</view>
				</u-form-item>
				<u-form-item required label="联系人:" ><u-input class="c9" size="30" placeholder-style="font-size:30rpx" v-model="form.no" placeholder="请输入联系人" /></u-form-item>
				<u-form-item required label="联系电话:" ><u-input class="c9" size="30" placeholder-style="font-size:30rpx" v-model="form.no" placeholder="请输入联系电话" /></u-form-item>
				<u-form-item required label="所属区域:">
					<view class="f-y-c">
						<text class="c3 f30" @click="showPicker=true" >{{title||'请选择城市'}}</text>
						<text class="iconfont iconinto f24 c3 ml10"></text>
					</view>
				</u-form-item>
				<u-form-item required label="详细地址:" ><u-input class="c9" size="30" placeholder-style="font-size:30rpx" v-model="form.no" placeholder="请输入详细地址" /></u-form-item>
				<u-form-item required label="店铺经纬度:" ><u-input class="c9" size="30" placeholder-style="font-size:30rpx" v-model="form.no" placeholder="请选择" /></u-form-item>
				<u-form-item required label="商店环境:" ><u-input class="c9" size="30" placeholder-style="font-size:30rpx" v-model="form.no" placeholder="请选择" /></u-form-item>
				<u-form-item required label="食品安全档案:" ><u-input class="c9" size="30" placeholder-style="font-size:30rpx" v-model="form.no" placeholder="请选择" /></u-form-item>
				<view class="title mt30">账号信息</view>
				<u-form-item required label="手机号:" ><u-input class="c9" size="30" placeholder-style="font-size:30rpx" v-model="form.no" placeholder="请输入手机号" /></u-form-item>
				<u-form-item label="账号名称:" ><u-input class="c9" size="30" placeholder-style="font-size:30rpx" v-model="form.no" placeholder="请输入账号名称" /></u-form-item>
				<u-form-item required label="登录密码:" ><u-input class="c9" size="30" placeholder-style="font-size:30rpx" v-model="form.no" placeholder="请输入登录密码" /></u-form-item>
				<u-form-item required label="确认登录密码:" ><u-input class="c9" size="30" placeholder-style="font-size:30rpx" v-model="form.no" placeholder="确认密码" /></u-form-item>
				<view class="title mt30">入驻套餐</view>
				<u-form-item required label="入驻频道:" ><u-input class="c9" size="30" placeholder-style="font-size:30rpx" v-model="form.no" placeholder="请选择入驻频道" /></u-form-item>
				<u-form-item label="经营品类:" >
					<u-radio-group v-model="form.sex">
						<u-radio v-for="(item, index) in [{name:'有'},{name:'无'}]" :active-color="tColor" label-size="30" :key="index" :name="item.name" :disabled="item.disabled">
							{{ item.name }}
						</u-radio>
					</u-radio-group>
				</u-form-item>
				<u-form-item label="套餐选择:" >
					<u-radio-group v-model="form.sex">
						<u-radio v-for="(item, index) in [{name:'有'},{name:'无'}]" :active-color="tColor" label-size="30" :key="index" :name="item.name" :disabled="item.disabled">
							{{ item.name }}
						</u-radio>
					</u-radio-group>
				</u-form-item>
			</u-form>
		</view>
		<button class="btn" style="background:#F8C10D" type="default" @click="saveAddress">提交</button>
		<u-popup v-model="showPicker" mode="bottom">
			<picker-date :show.sync="showPicker" :title.sync="title"></picker-date>
		</u-popup>
	</view> -->
</template>

<script>
	import PickerDate from '../common/Picker.vue'
	export default {
		components:{PickerDate},
		data(){
			return {
				webviewStyles: {
					progress: {
						color: '#FF3333'
					}
				},
				uniacid:'',
				title:'',
				showPicker:false,
				form:{
					logo:''
				},
				src:'',
			}
		},
		onLoad(options) {
			this.src = JSON.parse(decodeURIComponent(options.src));
			// console.log(JSON.parse(decodeURIComponent(options.src)))
		},
		// computed: {
		// 	src() {
		// 		return `https://o2o.bkycms.com/views/H5/shop/#/join?uniacid=${this.uniacid}`
		// 	}
		// },
		created(){
			// const app = getApp().globalData
			// this.uniacid = app.siteInfo.uniacid
		},
		methods:{
			chooseImg(){
				uni.chooseImage({
					sizeType:['compressed'],
					count:1,
					success:res=>{
						this.form.logo = res.tempFilePaths[0]
					}
				})
			}
		}
	
	}
</script>

<style scoped lang="scss">
	.btn{
		width: 700rpx;
		margin: 0 auto;
		margin-top: 20rpx;
	}
	.title{
		position: relative;
		font-size: 36rpx;
		font-weight: bold;
		padding-left: 20rpx;
		margin-left: -20rpx;
	}
	.title::after{
		content: '';
		position: absolute;
		left: 0;
		top: 50%;
		transform: translateY(-50%);
		width: 6rpx;
		height: 32rpx;
		background: #FFD24D;
	}
</style>
