<template>
	<view class="bf" v-if="src">
		 <web-view :webview-styles="webviewStyles" :src="src"></web-view>
		<!-- <view class="w100" style="height: 300rpx;">
			<image class="wh" src="https://img1.baidu.com/it/u=2365567670,1494725885&fm=15&fmt=auto&gp=0.jpg" mode="">
			</image>
		</view>
		<view class="p2">
			<view class="f40 wei">合作申请</view>
			<view class="c9 f24">请认真填写以下信息。提交成功后，我们的工作人员会尽快与您联系。</view>
			<view class="pl20">
				<u-form :model="form" ref="uForm" label-width="240" :label-style="{fontSize:'32rpx',color:'#000'}">
					<view class="title mt30">联系信息</view>
					<u-form-item required label="申请合作城市:">
						<view class="f-y-c">
							<text class="c3 f32" @click="showPicker=true">{{form.city||'请选择城市'}}</text>
							<text class="iconfont iconinto f24 c3 ml10"></text>
						</view>
					</u-form-item>
					<u-form-item required label="联系人:">
						<u-input class="c9" size="32" placeholder-style="font-size:32rpx" v-model="form.userName"
							placeholder="请输入联系人" />
					</u-form-item>
					<u-form-item required label="联系电话:">
						<u-input class="c9" size="32" type="number" placeholder-style="font-size:32rpx"
							v-model="form.userTel" placeholder="请输入联系手机号" />
					</u-form-item>
					<u-form-item required label="联系邮箱:">
						<u-input class="c9" size="32" placeholder-style="font-size:32rpx" v-model="form.email"
							placeholder="请输入联系邮箱" />
					</u-form-item>
					<u-form-item required label="预计投入资金:">
						<u-input class="c9" size="32" type="number" placeholder-style="font-size:32rpx"
							v-model="form.money" placeholder="请输入数字(单位:万元)" />
					</u-form-item>
				</u-form>
			</view>
		</view>

		<button class="btn" style="background:#F8C10D" type="default" @click="submit">提交</button>
		<u-popup v-model="showPicker" mode="bottom">
			<picker-date :show.sync="showPicker" :title.sync="form.city"></picker-date>
		</u-popup> -->
	</view>
</template>

<script>
	import PickerDate from '../common/Picker.vue'
	export default {
		components: { PickerDate },
		data() {
			return {
				webviewStyles: {
					progress: {
						color: '#FF3333'
					}
				},
				showPicker: false,
				form: {
					name:'',
					city: "",
					email: "",
					money: "",
					userName: "",
					userTel: ""
				},
				src:'',
			}
		},
		onLoad(options) {
			this.src = JSON.parse(decodeURIComponent(options.src));
		},
		// computed: {
		// 	src() {
		// 		return ` https://o2o.bkycms.com/views/H5/shop/#/person?uniacid=${this.uniacid}`
		// 	}
		// },
		methods: {
			async submit() {
				this.form.name = this.form.city
				await this.util.request({
					'url': this.api.regionSave,
					method: 'POST',
					mask: 1,
					data: this.form
				})
				await this.util.modal('工作人员将在七个工作日联系您', '申请成功').then(res => {
					this.go('back')
				})
			},
		}

	}
</script>

<style scoped lang="scss">
	.btn {
		width: 700rpx;
		margin: 0 auto;
		margin-top: 20rpx;
	}

	.title {
		position: relative;
		font-size: 36rpx;
		font-weight: bold;
		padding-left: 20rpx;
		margin-left: -20rpx;
	}

	.title::after {
		content: '';
		position: absolute;
		left: 0;
		top: 50%;
		transform: translateY(-50%);
		width: 6rpx;
		height: 32rpx;
		background: #FFD24D;
	}
</style>
