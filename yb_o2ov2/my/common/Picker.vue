<template>
    <view>
        <view class="f-x-bt p1">
			<view class="f24" @click="$emit('update:show', false)">取消</view>
            <view class="uni-title">请选择</view>
			<view class="f24" @click="submit">确定</view>
        </view>
        <picker-view :indicator-style="indicatorStyle" :value="value"  @change="bindChange" class="picker-view">
            <picker-view-column>
                <view class="item" v-for="(item,index) in provinces" :key="index">{{item.provinceName}}</view>
            </picker-view-column>
            <picker-view-column>
                <view class="item" v-for="(item,index) in cityArr" :key="index">{{item.citysName}}</view>
            </picker-view-column>
        </picker-view>
    </view>
</template>
<script>
	import city from './city.json'
    export default {
		props:{
			show:{
				type:Boolean,
				default:false
			}
		},
        data() {
            return {
                indicatorStyle: `height: 30rpx;`,
				value:[0,0],
				provinces:'',
				cityArr:[]
            }
        },
		created() {
			this.provinces = city.provinces
			this.cityArr = city.provinces[0].citys
		},
        methods: {
            bindChange: function (e) {
                const val = e.detail.value
				this.cityArr = this.provinces[val[0]].citys
				this.value = val
            },
			submit(){
				this.$emit('update:title',`${this.provinces[this.value[0]].provinceName} ${this.cityArr[this.value[1]].citysName}`)
				console.log(`${this.provinces[this.value[0]].provinceName} ${this.cityArr[this.value[1]].citysName}`)
				this.$emit('update:show', false)
			}
        }
    }
</script>
<style>
    .picker-view {
        width: 750rpx;
        height: 600rpx;
        margin-top: 20rpx;
    }
    .item {
        height: 50px;
        align-items: center;
        justify-content: center;
        text-align: center;
    }
</style>