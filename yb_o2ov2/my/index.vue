<template>
	<view class="wh">
		<scroll-view scroll-y="true" class="wh">
			<!-- 个人信息 -->
			<Vip></Vip>
			<!-- 一键添加至桌面 -->
			<!-- <view class="add-desktop">
				<view class="add-desktop-content">
					<view class="but" @click="addToDesktop">{{$t('my.one_click_add_to_desktop')}}</view>
				</view>
			</view> -->
			<!-- 功能按钮 -->
			<VipButtonGroup></VipButtonGroup>
			<!-- 图片组 -->
			<ImageGroup></ImageGroup>
		</scroll-view>
		<Load :show="showLoad"></Load>
		<tab-bar :current="2"></tab-bar>
	</view>
</template>

<script>
	import Vip from "../index/component/drag/Vip.vue"
	import VipButtonGroup from '../index/component/drag/VipButtonGroup.vue'
	import ImageGroup from "../index/component/drag/ImageGroup.vue"
	import TabBar from "@/components/TabBar.vue"
	export default {
		components: {
			Vip,
			VipButtonGroup,
			ImageGroup,
			TabBar,
		},
		data() {
			return {
				showLoad: true,
				deferredPrompt: null, // 用于存储 beforeinstallprompt 事件
			}
		},
		methods: {
			async init() {
				this.refreshUser()
				await this.getLayout({
					page: 'personalcenter',
					id: '2'
				})
				this.showLoad = false
			},
			// 添加到桌面
			addToDesktop() {
				// #ifdef H5
				
				if (this.deferredPrompt) {
					// 显示安装提示
					this.deferredPrompt.prompt();

					// 等待用户响应
					this.deferredPrompt.userChoice.then((choiceResult) => {
						if (choiceResult.outcome === 'accepted') {
							this.util.message(this.$t('my.add_success_tips'), 3);
						} else {
							this.util.message(this.$t('my.add_cancel_tips'), 3);
						}
						this.deferredPrompt = null; // 清除事件，避免重复触发
					});
				} else if (window.matchMedia('(display-mode: standalone)').matches) {
					this.util.message(this.$t('my.been_added_to_desktop'));
				} else {
					const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
					if (isIOS) {
						uni.showModal({
							title: this.$t('my.add_to_home_screen'),
							content: this.$t('my.ios_add_guide'),
							showCancel: false
						});
					} else {
						uni.showModal({
							title: this.$t('my.add_to_home_screen'),
							content: this.$t('my.android_add_guide'),
							showCancel: false
						});
					}
				}
				// #endif

				// #ifndef H5
				this.util.message(this.$t('my.use_in_h5'));
				// #endif
			}
		},
		onLoad() {
			this.init()
			// #ifdef H5
			window.addEventListener('beforeinstallprompt', (e) => {
				// 阻止默认行为，Chrome 67 及更早版本会显示提示
				e.preventDefault();
				// 存储事件，以便稍后触发
				this.deferredPrompt = e;
				console.log('beforeinstallprompt event fired');
			});
			// #endif
		}
	}
</script>

<style lang="scss" scoped>
	.card {
		background: #ffffff;
		border-radius: 20rpx;
		padding: 20rpx;
		margin-top: 20rpx;
	}

	.title {
		font-size: 28rpx;
		font-weight: bold;
	}

	.add-desktop {
		padding: 0 30rpx;
		margin-top: 30rpx;

		.add-desktop-content {
			display: flex;
			width: 100%;
			height: 160rpx;
			justify-content: center;
			align-items: center;
			background: linear-gradient(90deg, #FDF8E2 0%, #FEE8B7 100%);
			border-radius: 20rpx;
			box-shadow: 0 8rpx 20rpx rgba(254, 232, 183, 0.5);

			.but {
				display: flex;
				width: 360rpx;
				height: 70rpx;
				justify-content: center;
				align-items: center;
				border-radius: 35rpx;
				background-color: #FFCC00;
				color: #fff;
				font-weight: bold;
				font-size: 28rpx;
				box-shadow: 0 4rpx 16rpx rgba(255, 204, 0, 0.3);
				transition: all 0.3s ease;

				&:active {
					transform: scale(0.98);
					box-shadow: 0 2rpx 8rpx rgba(255, 204, 0, 0.2);
				}
			}
		}
	}
</style>