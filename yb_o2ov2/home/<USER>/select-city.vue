<template>
	<view class="bf">
		<!-- 头部 start -->
		<view class="t-c p-f w100 bf" style="z-index: 2;top: 0;"
			:style="{height:`${statusNavBarHeight}px`,background:'#fff'}">
			<view class="p2">
				<view class="bf5 t-l f-y-c f-g-1 p10" style="border-radius: 40rpx;padding: 5rpx 30rpx;height: 70rpx;">
					<text class="iconfont iconsearch f28 mr10 c9"></text>
					<input type="text" value="" placeholder="输入城市名进行搜索" />
				</view>
			</view>
			<!-- 位置信息 -->
			<view class="p2 f-x-bt head-end bf">
				<view class="f-y-c f32">
					<text class="iconfont iconposition f28 mr10 c9"></text>
					<text class="mr10">当前定位城市</text>
					<text>武汉</text>
				</view>
			</view>

		</view>
		<!-- 头部 end -->
		<scroll-view scroll-y="true" @scroll="scroll"
			:style="{height:`${scrollHeight}px`,marginTop:`${marginTop}px`,background:'#fff'}"
			:scroll-into-view="indexId">
			<view class="p2 c9" style="padding-bottom: 0;">热门城市</view>
			<view class="f-y-c f-w p02">
				<text v-for="item in list" :key="item" class="label" :class="active===item?'active':'inactive'"
					@click="active=item">{{item.name}}</text>
			</view>
			<view class="p2 f-col">
				<view v-for="(item, index) in indexList" :key="index">
					<view class="c9" :id="'index'+index">{{item}}</view>
					<view class="list-cell">
						列表1
					</view>
					<view class="list-cell">
						列表2
					</view>
					<view class="list-cell">
						列表3
					</view>
				</view>
			</view>
			<!-- 快速定位 -->
			<view class="p-f f-col f22 t-c" :style="{top:`${marginTop+30}px`,right:'0'}" style="line-height: 30rpx;">
				<view v-for="(item, index) in indexList" :key="index" style="width:30rpx"
					:style="indexId===('index'+index)?`background:${tColor};`:''" @click="indexId=('index'+index)">
					{{item}}</view>
			</view>
			<!-- 安全区域 -->
			<view class="w100" style="height: 40rpx;"></view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				list: ['北京', '成都', '重庆', '广州', '杭州', '南京', '上海', '深圳', '苏州', '天津', '武汉', '西安'],
				scrollHeight: '',
				marginTop: '',
				expand: false,
				active: '',
				scrollTop: 0,
				indexList: ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S",
					"T", "U",
					"V", "W", "X", "Y", "Z"
				],
				indexId: ''
			}
		},
		computed: {

		},
		onLoad() {
			this.getCity()
		},
		onReady() {
			this.getElement()
		},
		methods: {
			async getCity(){
				let {data} = await this.util.request({
					'url': this.api.shopCity,
				})
				this.list = data.slice(0,12)
			},
			scroll(e) {
				this.scrollTop = ed.detail.top
			},
			getElement() {
				let query = uni.createSelectorQuery().in(this);
				query.select('.head-end').boundingClientRect((res) => {
					this.marginTop = res.bottom + 10
					this.scrollHeight = Math.floor(this.wHeight - res.bottom - this.statusNavBarHeight)
				}).exec()

			}
		}
	}
</script>

<style scoped lang="scss">
	.label {
		width: 150rpx;
		height: 75rpx;
		line-height: 75rpx;
		margin-right: 20rpx;
		margin-top: 20rpx;
		border-radius: 10rpx;
		text-align: center;
	}

	.active {
		color: #FFD679;
		border: 1px solid #FFD679;
		background: #FFFBF1;
	}

	.inactive {
		background: #F8F8F8;
	}

	.list-cell {
		padding: 20rpx 0;
		border-top: 1rpx solid #efefef;
	}
</style>
