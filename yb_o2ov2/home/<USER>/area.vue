<template>
	<view class="bf">
		<scroll-view scroll-y="true" :style="{height:`${scrollHeight}px`,marginTop:`${marginTop}px`}">
			<view class="f-y-c p2" style="padding-bottom: 0;">
				<text class="f24 c9">选择区域</text>
			</view>
			<view v-if="myAddress.length!==0" class="p23" style="padding: 0 60rpx;transition: .3s ease;overflow:hidden"
				:style="expand?`height:${140*myAddress.length}rpx;`:'max-height:1220rpx;'">
				<view v-for="(item,index) in myAddress" :key="item" class="p20 f-c-xc" style="height: 140rpx;border-bottom: 1px solid #fafafa;"
				@click="changePosition(item)">
					<view class="t-o-e f-y-c" style="width: 630rpx;">
						<text class="f30 wei">{{item.name}}</text>
					</view>
				</view>
			</view>
			<view v-if="myAddress.length>10" @click="expand = !expand" style="padding: 0 60rpx 20rpx;">
				展开更多地址
				<text class="iconfont iconback m01 f16"
					:style="expand?'transform: rotate(90deg);':'transform: rotate(-90deg);'"></text>
			</view>
			<view class="w100 bf5" style="height: 20rpx;"></view>
		</scroll-view>
	</view>
</template>

<script>
	import {
		mapState,
		mapMutations
	} from 'vuex'
	import utils from '@/common/utils.js'
	export default {
		data() {
			return {
				scrollHeight: '',
				marginTop: '',
				expand: false,
				myAddress:[],
				nearbyArr:[]
			}
		},
		computed: {
			...mapState('dndc',['regionId']),
		},
		onLoad() {
			this.init()
		},
		mounted() {
			this.getElement()
		},
		methods: {
			...mapMutations('dndc',['setRegionId']),
			refreshPosition:utils.throttle(function(){
				this.getLocInfo()
			},10000),
			async init(){
				this.fetchData()
			},
			async fetchData(){
				let {data} =  await this.util.request({
					url: this.api.regionList,
					method: 'GET',
					data: {
						lat:this.latLng.latitude,
						lng:this.latLng.longitude
					},
					is_login: 0
				})
				this.myAddress = data
			},
			changePosition(item){
				this.setRegionId(item)
				uni.setStorageSync('setRegionId',item)
				//更新订单列表
				getCurrentPages()[0].$vm.$refs.initIndex.refresh()
				this.go('back')
			},
			getElement() {
				let query = uni.createSelectorQuery().in(this);
				query.select('.head-end').boundingClientRect((res) => {
					this.marginTop =  10
					this.scrollHeight = Math.floor(this.wHeight - this.statusNavBarHeight)
				}).exec()

			}
		}
	}
</script>

<style scoped lang="scss">
	.label {
		width: 60rpx;
		height: 30rpx;
		border-radius: 5rpx;
		margin-right: 10rpx;
		font-size: 20rpx;
		font-weight: bold;
		line-height: 30rpx;
		text-align: center;
	}
</style>
