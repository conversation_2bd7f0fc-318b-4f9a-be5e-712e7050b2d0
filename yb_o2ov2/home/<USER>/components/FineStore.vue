<template>
	<view>
		<!-- 头部 start -->
		<view class="p-f w100 " :style="{top:0,height:`${statusNavBarHeight}px`,paddingTop:`${menuButtonTop}px`,zIndex:3}">
			<!-- 返回 -->
			<view class="p-a" :style="{top:`${menuButtonTop}px`,left:`20rpx`,zIndex:4}" @click="go('back')">
				<text class="iconfont iconback f40"></text>
			</view>
			<view class="t-c f32 wei cf">
				<view>优选好店</view>
			</view>
		</view>
		<!-- 头部 end -->
		<!-- 背景 -->
		<view class="p-a w100" style="top: 0;z-index: -1;height: 600rpx;background:linear-gradient(0, #fff0, #CEA86A 400rpx);">
		</view>
		<view class="p-r p02" :style="{marginTop:`${statusNavBarHeight}px`,paddingBottom:'200rpx'}">
			<!-- card -->
			<view class="mt30">
				<view v-for="item in 10" class="bs20 bf p2 mt20">
					<view class="flex">
						<view class="bs20 mr20" style="width: 120rpx;height: 120rpx;"><image class="wh" src="https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fimg.zcool.cn%2Fcommunity%2F010abe58c0dd94a801219c77ca8b11.jpg" mode=""></image></view>
						<!-- 商户信息 -->
						<view class="f-col" style="width: 530rpx;">
							<view class="f36 wei t-o-e">茶百道（虎泉店）茶百道（虎泉店）茶百道（虎泉店）<text class="iconfont iconinto f28 wei"></text></view>
							<view class="f-y-c c6 f24">
								<text class="mr10">月售1635</text>
								<text class="mr10">免配送费</text>
								<text class="mr10">起送 $20</text>
								<text class="mr10">配送约48分钟</text>
							</view>
							<view>
								<!-- 满减优惠 -->
								<view class="label-coupon f20" style="color: #f1755a;border: 1rpx solid #FCF3F6;background: #fcfbfb;">
									<text>25减13</text><view class="line" style="background:#f9cedc;"></view>
									<text>38减23</text><view class="line" style="background:#f9cedc;"></view>
									<text>45减33</text>
								</view>
								<text class="label-coupon brown f20 " style="border: 1rpx solid;">10元无门槛红包</text>
							</view>
						</view>
					</view>
					<!-- 减免优惠 -->
					<view>
						<!-- 商品 -->
						<view class="flex o-x-s f26">
							<view v-for="element in 10" :key="element" class="f-c-c mr10" style="width: 220rpx;">
								<view class="wh bs20 bfa">
									<view style="width: 220rpx; height: 160rpx;"><image class="wh" src="https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fwww.cy8.com.cn%2Fcontent%2FUpLode%2F898%2F44d6a410-4b0e-278d-cf03-5c8eecd73f49.jpg" mode="" ></image></view>
									<view class="mt10 f28 t-o-e pl10">茉莉奶绿</view>
									<view class="f24 pl10 pb10" style="margin-right: auto;">
										<text class="wei" style="color: #ff5454;">$</text>
										<text class="f32" style="color: #ff5454;">19.8</text>
										<text class="t-d-l c6">$50</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				coupons: [{
						name: '10元无门槛红包',
						type: '1'
					},
				],
				labelColorArr:['red','brown']
			}
		}
	}
</script>

<style scoped lang="scss">
	.label-coupon {
		display: inline-block;
		height: 40rpx;
		line-height: 38rpx;
		color: #c57771;
		border: 1rpx solid #f0dbdb;
		border-radius: 8rpx;
		padding: 0rpx 8rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.red{
		color: #ea5a3b;
		border-color: #f0dbdb;
	}
	.brown{
		color: #bba278;
		border-color: #d0c8b3;
	}
	.line{
		position: relative;
		display: inline-block;
		width: 2rpx;
		height: 20rpx;
		margin: 0 10rpx;
	}
</style>
