<template>
	<view>
		<!-- 头部 start -->
		<view class="p-f w100 " :style="{top:0,height:`${statusNavBarHeight}px`,paddingTop:`${menuButtonTop}px`,zIndex:3}">
			<!-- 返回 -->
			<view class="p-a" :style="{top:`${menuButtonTop}px`,left:`20rpx`,zIndex:4}" @click="go('back')">
				<text class="iconfont iconback f40"></text>
			</view>
		</view>
		<!-- 头部 end -->
		<!-- 背景 -->
		<view class="p-a w100" style="top: 0;z-index: -1;height: 600rpx;background:linear-gradient(0, #fff0, #ff7606 400rpx);">
			<!-- <image class="wh" src="../../../../static/imgTemp/202103021557073620.jpg" mode=""></image> -->
		</view>
		<view class="p-r p2" :style="{marginTop:`${statusNavBarHeight}px`,paddingBottom:'200rpx'}">
			<!-- 大牌特惠 -->
			<!-- <view class="cf f60 wei">大牌特惠</view>
			<view class="cf bs10 dis-in" style="padding: 0 8rpx;background: #f5eeee3b;">
				超值套餐 品牌满减 优惠叠加
			</view> -->
			<!-- card -->
			<view class="mt30">
				<view v-for="item in 10" class="bs20 bf p2 mt20">
					<view class="flex">
						<!-- <view class="bs20 mr20" style="width: 120rpx;height: 100rpx;"><image class="wh" src="https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fimg.zcool.cn%2Fcommunity%2F010abe58c0dd94a801219c77ca8b11.jpg" mode=""></image></view> -->
						<!-- 商户信息 -->
					<!-- 	<view class="f-col" style="width: 530rpx;">
							<view class="f36 wei t-o-e">茶百道（虎泉店）茶百道（虎泉店）茶百道（虎泉店）</view>
							<view class="f-y-c mt10 c6">
								<text class="f20 mr10">月售1635</text>
								<text class="f20 mr10">免外送费</text>
								<text class="f20 mr10">起送 $20</text>
								<text class="f20 mr10">外送约48分钟</text>
							</view>
						</view> -->
					</view>
					<!-- 减免优惠 -->
					<view>
						<!-- 满减优惠 -->
						<!-- <view class="label-coupon f24 mt30" style="color: #f1755a;border: 1rpx solid #FCF3F6;background: #FCF3F6;">
							<text>满减优惠：</text>
							<text>满25减13</text><view class="line" style="background:#f9cedc;"></view>
							<text>满38减23</text><view class="line" style="background:#f9cedc;"></view>
							<text>满45减33</text>
						</view> -->
						<!-- 叠加使用 -->
					<!-- 	<view class="f24">
							<text class="c9">叠加使用：</text>
							<text v-for="(item,index) in coupons"  class="label-coupon" :class="labelColorArr[item.type]"
								style="border: 1rpx solid;">{{item.name}}</text>
						</view> -->
						<!-- 商品 -->
						<!-- <view class="flex o-x-s f26">
							<view v-for="element in 10" :key="element" class="f-c-c mr10" style="width: 220rpx;">
								<view class="bs20 p-r" style="width: 220rpx; height: 160rpx;">
									<image class="wh" src="https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fwww.cy8.com.cn%2Fcontent%2FUpLode%2F898%2F44d6a410-4b0e-278d-cf03-5c8eecd73f49.jpg" mode="" ></image>
		
									<view class="p-a cf f22" style="bottom: 0;left: 0;background:linear-gradient(90deg, #f39525, #ff5454); ;border-radius: 0 20rpx 0 0;padding:0 5rpx;">5.16折</view>
								</view>
								<view class="mt10 f28 wei t-o-e pl10">茉莉奶绿</view>
								<view class="f24 pl10" style="margin-right: auto;">
									<text class="wei" style="color: #ff5454;">$</text>
									<text class="f32 wei" style="color: #ff5454;">2.58</text>
									<text class="t-d-l c6">$5</text>
								</view>
							</view>
						</view> -->
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				coupons: [{
						name: '天天神券',
						type: '0'
					}, {
						name: '首单减13',
						type: '0'
					}, {
						name: '新客立减',
						type: '0'
					},
					{
						name: '返3元券',
						type: '0'
					},{
						name: '收藏领2元券',
						type: '1'
					},{
						name: '10元无门槛红包',
						type: '1'
					},
				],
				labelColorArr:['red','brown']
			}
		}
	}
</script>

<style scoped lang="scss">
	.label-coupon {
		display: inline-block;
		height: 40rpx;
		line-height: 38rpx;
		color: #c57771;
		border: 1rpx solid #f0dbdb;
		border-radius: 8rpx;
		padding: 0rpx 8rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.red{
		color: #ea5a3b;
		border-color: #f0dbdb;
	}
	.brown{
		color: #bba278;
		border-color: #d0c8b3;
	}
	.line{
		position: relative;
		display: inline-block;
		width: 2rpx;
		height: 20rpx;
		margin: 0 10rpx;
	}
</style>
