<template>
	<view class="p02" style="padding-bottom: 220rpx;" :style="{paddingTop:`${statusNavBarHeight}px`}">
		<view class="w100 t-c wei f32 bf p-f" style="top: 0;left: 0;z-index: 10;"
			:style="{height:`${statusNavBarHeight}px`,paddingTop:`${menuButtonTop}px`}">
			<view class="p-a" :style="{top:`${menuButtonTop}px`,left:`20rpx`,zIndex:4}" @click="go('back')">
				<text class="iconfont iconback f40"></text>
			</view>
			<view class="t-c f32 wei">
				<view>商店代金券</view>
			</view>
		</view>
		<!-- 可使用 -->
		<view class="mt30">
			<view class="f32 wei mb20">可使用代金券<text class="f24 nowei ml10 c6">{{list.normal.length}}张</text></view>
			<view v-for="(item,index) in list.normal" :key="item.id" class="bf p2 bs20 mt10 p-r" @click="selectCoupon(item)">
				<view class="f-y-c" style="border-bottom: 1px dashed #ddd;">
					<view class="mr20 mt10">
						<image class="bs20" :src="shopGoodsInfo.shopData.icon" mode="" style="width: 70rpx;height: 70rpx;"></image>
					</view>
					<view class="f-col f-g-1">
						<view class="f30 wei">{{shopGoodsInfo.shopData.name}}</view>
						<view class="f24 c6 t-o-e">{{item.useExplain}}</view>
					</view>
					<view class="f-col mr20 t-c pb20" style="color: #FF5837;">
						<view class="f24">$<text class="f50 wei">{{Number(item.money)}}</text></view>
						<view class="f24" v-if="item.fullMoney<=0">无门槛</view>
						<view class="f24" v-else>满{{item.fullMoney}}可用</view>
					</view>
					<view>
						<text :class="item.id===value?'iconselect black':'iconnoselect'" class="iconfont f28" :style="{color:tColor,background:item.id===value?fontColor:''}"></text>
					</view>
				</view>
				<view class="f-y-c c9 f24 mt20" @click.stop="selectIndex = selectIndex===index?'':index">
					<view style="width: 650rpx;transition: .2s;" :style="selectIndex === index?'height:auto':'overflow: hidden;text-overflow: ellipsis;white-space: nowrap;'">{{item.explain}}</view>
					<view class="ml10"><text class="iconfont iconinto f24 c9" style="transition: .2s;" :style="selectIndex === index?'transform:rotate(-90deg)':'transform:rotate(90deg)'"></text></view>
				</view>
			</view>
		</view>
		<!-- 不可使用 -->
		<view class="mt30">
			<view v-if="list.ban.length!==0" class="f32 wei mb20">不可使用代金券<text class="f24 nowei ml10 c6">{{list.ban.length}}张</text></view>
			<view v-for="item in list.ban" :key="item.id" class="bf p2 bs20 mt10 p-r">
				<view class="f-y-c" style="border-bottom: 1px dashed #ddd;opacity: .5;">
					<view class="mr20 mt10"><image class="bs20" :src="shopGoodsInfo.shopData.icon" mode="" style="width: 70rpx;height: 70rpx;"></image></view>
					<view class="f-col f-g-1">
						<view class="f30 wei">{{shopGoodsInfo.shopData.name}}</view>
						<view class="f24 c6">{{item.useExplain}}</view>
					</view>
					<view class="f-col mr20 t-c pb20" style="color: #FF5837;">
						<view class="f24">$<text class="f50 wei">{{Number(item.money)}}</text></view>
						<view class="f24" >{{item.explain}}</view>
					</view>
					<view>
						<text class="iconfont iconnoselect f28" :style="{color:tColor}"></text>
					</view>
				</view>
				<view class="f20 mt20" style="line-height: 28rpx;">
					<view class="" style="width: 650rpx;color: #FF5837;">本单不可用原因</view>
					<view class="">{{item.banFormat}}</view>
				</view>
			</view>
		</view>
		<!-- 确认使用红包 -->
		<view class="confirmBox f28">
			<view class="f-x-bt">
				<view class="f-col">
					<view class="f28">
						<text>可抵扣</text>
						<text style="color:#FF5837 ;">$</text>
						<text class="f44" style="color:#FF5837 ;">{{discountInfo.discountMoney}}</text>
					</view>
					<view class="c9 f20">已选{{discountInfo.selectNum}}张店铺券</view>
				</view>
				<view>
					<view class="f30 wei t-c bs10 p10" style="width: 180rpx" :style="{background:tColor,color:fontColor}" @click="confirm">确定</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState
	} from "vuex";
	export default {
		data() {
			return {
				value:'',
				selectIndex:'',
				params:{
					state:1,//  1 可使用 2 已使用 3 已过期
					item:2,// 1平台红包 2 商店代金券
				},
				list:[],
				discountInfo:{
					discountMoney:0,
					selectNum:0
				},
				useType:'',
				tableId:'',
				item:''
			}
		},
		computed: {
			...mapState({
				shopGoodsInfo: state => state.shopGoodsInfo
			}),
		},
		async onLoad(option) {
			if(option.useType){
				this.useType = option.useType
			}
			if(option.tableId){
				this.tableId=option.tableId
			}
			if(option.item){
				this.item=option.item
			}
			await this.fetchData(option.storeId)
			if(option.couponId){
				this.selectCoupon(this.list.normal.find(t=>t.id===option.couponId))
			}else{
				if(this.list.normal.length && this.list.normal.length>=0){
					let couponId = this.list.normal[0].id
					this.selectCoupon(this.list.normal.find(t=>t.id===couponId))
				}
			}

		},
		methods:{
			async fetchData(storeId){
				let {data} =  await this.util.request({
					url: this.api.kyyhq,
					method: 'POST',
					data:{
						storeId:storeId,
						type:2,//1平台 2商户代金券
						useType:this.useType,
						tableId:this.tableId,
						item:this.item
					}
				})
				this.list = data
			},
			selectCoupon(item){
				if(this.value === ''){
					this.value = item.id
					this.discountInfo = {
						discountMoney:Number(item.money),
						selectNum:1
					}
				}else{
					this.value = ''
					this.discountInfo = {
						discountMoney:0,
						selectNum:0
					}
				}
				
			},
			confirm(){
				// console.log(44,this.discountInfo.discountMoney,this.value,this.type)
				let pages = getCurrentPages()
				let currentPage = pages[pages.length-2]
				currentPage.$vm.discount.storeConponMoney = this.discountInfo.discountMoney
				currentPage.$vm.form.couponId.store = this.value
				
				// if(this.type==1){
				// 	currentPage.$vm.discount.platConponMoney = this.discountInfo.discountMoney
				// 	currentPage.$vm.form.couponId.platform = this.value
				// }
				// if(this.type==2){
				// 	currentPage.$vm.discount.storeConponMoney = this.discountInfo.discountMoney
				// 	currentPage.$vm.form.couponId.store = this.value
				// }
				
				this.go('back')
			}
		}
		
	}
</script>

<style scoped lang="scss">
	.confirmBox {
		position: fixed;
		bottom: 0rpx;
		left: 0;
		width: 750rpx;
		height: 150rpx;
		background: #FFF;
		padding: 20rpx;
		padding-top: 0;
	}
	.black{
		background: #000;
		border-radius: 50rpx;
		line-height: 0.5;
	}
</style>
