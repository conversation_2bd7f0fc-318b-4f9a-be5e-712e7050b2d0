<template>
	<view>
	<!-- 	<SpecialOffer v-if="type === '1'"></SpecialOffer>
		<StoreDeals v-else-if="type === '2'"></StoreDeals>
		<FineStore  v-else-if="type === '3'"></FineStore> -->
	</view>
</template>

<script>
	// import SpecialOffer from "./components/SpecialOffer"
	// import StoreDeals from "./components/StoreDeals"
	// import FineStore from "./components/FineStore"
	
	export default {
		components:{
			// SpecialOffer,StoreDeals,FineStore
		},
		data(){
			return {
				comp:'components',
				type:'', //1天天特价  2大牌减免  3优选好店
			}
		},
		onLoad(option) {
			this.type = option.type || '1'
		}
	}
</script>

<style>
</style>
