<template>
	<view class="mh100 pb130">
		<view class="page-navigation-bar">
			<view class="go-icon">
				<text class="iconfont iconback f40 " @click="go('back')"></text>
			</view>
		</view>
		<!-- <nav-tab cname="" ifsize='32' v-model="tIdx" @changeTab='changeTab' :tabs='tabs'></nav-tab> -->
		<view class="m2 bf p20 bs10">
			<view class="p02 f30">商店资质信息公示</view>
			<!-- <view class="f-raw f-x-bt p03">
				<view class="imgc bs10" v-for="(v,i) in list" :key='i' @click="yl(i)">
					<mg-img :src="v"></mg-img>
				</view>
			</view> -->
			<view class="p43">宣傳影片
				<video :src="info.video"/>
			</view>
			<view class="p43">消殺記錄
				<view class="f-raw">
					<view class="imgc bs10" v-for="(v,i) in info.xiaosha" :key='i'>
						<mg-img :src="v"></mg-img>
					</view>
				</view>
			</view>
			<view class="p43">管道清洗記錄
				<view class="f-raw">
					<view class="imgc bs10" v-for="(v,i) in info.guandao" :key='i'>
						<mg-img :src="v"></mg-img>
					</view>
				</view></view>
			<view class="p43">商店資質
				<view class="f-raw">
					<view class="imgc bs10" v-for="(v,i) in info.zizhi" :key='i'>
						<mg-img :src="v"></mg-img>
					</view>
				</view></view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapActions
	} from 'vuex'
	import MgImg from '@/components/common/mg-img.vue'
	export default {
		name: 'bzf',
		components: {
			MgImg,
		},
		data() {
			return {
				info: {},
				list: [],
				tIdx: 0,
				tabs: [{
						name: '商品安全档案'
					},
					{
						name: '营业执照'
					},
				],
			}
		},
		async onLoad(options) {
			this.getSystem()
			this.info = JSON.parse(decodeURIComponent(options.info))
			this.changeTab(this.tIdx)
		},
		computed: {},
		methods: {
			changeTab(e) {
				if (this.tIdx == 0) {
					this.util.setNT('商品安全档案')
					this.list = this.info.foodRecords.map(v => this.getImgS(v))
				} else if (this.tIdx == 1) {
					this.util.setNT('营业执照')
					this.list = this.info.license.map(v => this.getImgS(v))
				}
			},
			yl(i) {
				this.util.preImg({
					idx: i,
					urls: this.list,
				})
			}
		},
	}
</script>

<style scoped lang="scss">
	.imgc {
		width: 300rpx;
		height: 280rpx;
		margin-top: 30rpx;
	}
</style>
