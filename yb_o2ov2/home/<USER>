<template>
	<view>
		<view class="f-x-bt p3">
			<!-- 返回 -->
			<view
				:style="{top:`${menuButtonTop}px`,left:`20rpx`,zIndex:4,marginTop:'6rpx',width:'50rpx',height:'56rpx'}"
				@click="go('back','/yb_o2ov2/index/index')">
				<text class="iconfont iconback f30 cf" :style="{color:opacity<1?'#000':'#fff'}"></text>
			</view>
			<!-- 收藏 -->
			<!-- <view :style="{top:`${menuButtonTop + 6/pxToRpxRate}px`,left:`${menuButtonLeft - 80/pxToRpxRate}px`}"
				style="z-index: 3;" @click="collectionStore()">
				<text class="iconfont  f44 ml20" :class="isCollection === 1?'icontcollectionfill':'icontcollection'"
					:style="{color:isCollection === 1?tColor:opacity<1?'#000':'#fff'}"></text>
			</view> -->
		</view>

		<view>
			<!-- 上半部 -->
			<view class="info">
				<!-- 背景遮罩图 -->
				<view class="bg-img">
					<view class="wh" style="filter: brightness(0.5);">
						<image class="wh" lazy-load v-if="shopData.takeOutSet && shopData.takeOutSet.signature"
							:src="shopData.takeOutSet.signature" mode="widthFix"></image>
						<image class="wh" lazy-load v-else :src=" shopData.icon" mode="widthFix"></image>
					</view>
				</view>
				<!-- //必须设置高度才有动画的效果 -->
				<view class="card p-r" style="transition:1s ease;z-index: 2;" :style="{background:'#ffffff'}">
					<!-- logo -->
					<view class="bs20 p-a" style="right: 30rpx;top:-30rpx;width: 140rpx;height: 140rpx;">
						<image class="wh" lazy-load :src="shopData.icon" mode="scaleToFill" style=""></image>
					</view>
					<!-- 店铺信息 start-->
					<view class="f-bt">
						<view>
							<view class="f36 wei" :class="showStoreTitle?'':'t-o-e'" style="width: 500rpx;"
								@click="showStoreTitle = !showStoreTitle">{{shopData.name}}</view>
							<view class="f24 c9 m10 f-y-c">
								<text class="iconfont iconstar f24" style="color:#ff5454"></text>
								<text class="wei mr10" style="color: #ff5454;">{{shopData.score}}</text>
								<text class="f20 mr10">{{$t('good.monthly_sales')}}{{shopData.outSales}}</text>
								<text
									class="f20 mr10">{{$t('good.delivery_Schedule')}}{{shopData.deliveryTime}}{{$t('good.minute')}}</text>
								<text class="f20 bs10 wei c0" :style="{background:tColor,color:fontColor}"
									style="padding: 0 4rpx;">{{shopData.deliveryMode}}</text>
							</view>
						</view>
					</view>
					<!-- 店铺信息 end-->
					<!-- 折扣 start-->
					<view class="f-raw">
						<view class="gyhqc gyhqc2 cf f20 f-row mr15 p-r mb10" :style="{color:'',background:'#F3D58C'}"
							v-if="user.isVip && vipCouponsData.data" @click="vipcoupon">
							<view class="gyhql f-c p-r">
								<view class="mr5 mt10" :style="{color:'#FE624B'}">$</view>
								<view class="f32 wei f-c"
									v-if="vipCouponsData.data.vipMoney && vipCouponsData.data.money">
									<text
										:style="{color:'#FE624B'}">{{Number(vipCouponsData.data.vipMoney) + Number(vipCouponsData.data.money)}}</text>
									<text class="f22 nowei c6 p01">无门槛</text>
								</view>
								<view class="p-a gdot gdot1"></view>
								<view class="p-a gdot gdot2"></view>
							</view>
							<view class="gyhqr f-c  c6" style="border-left: 1px dashed #f5f5f5;">兑换</view>
						</view>
						<view v-for="item in claimedCoupons" :key="item.id"
							class="gyhqc cf f20 f-row mr15 p-r mb10" :style="{color:'#FE624B',background:'#FEF5F6',}">
							<view class="gyhql f-c p-r">
								<view class="mr5 mt10">$</view>
								<view class="f32 wei">{{Number(item.money)}}</view>
								<view class="p-a gdot gdot1"></view>
								<view class="p-a gdot gdot2"></view>
							</view>
							<view class="gyhqr f-c">已领</view>
						</view>
						<view v-for="item in unclaimedCoupons" :key="item.id"
							@click="couponsShow = true" class="gyhqc cf f20 f-row mr15 p-r mb10"
							:style="{color:'',background:''}">
							<view class="gyhql f-c p-r">
								<view class="mr5 mt10">$</view>
								<view class="f32 wei">{{Number(item.money)}}</view>
								<view class="p-a gdot gdot1"></view>
								<view class="p-a gdot gdot2"></view>
							</view>
							<view class="gyhqr f-c" style="border-left: 1px dashed #f5f5f5;">领</view>
						</view>
					</view>
					<!-- 平台新客红包 -->
					<!-- <view class="dis-in f20"  v-if="discount.platformReduction && discount.platformReduction.name && discount.platformReduction.data && discount.platformReduction.data.money">
							<view class="f20 f-y-c mr20" style="background-color: #FA3534;color: #fff;border: none;border-radius: 8rpx;padding: 0rpx 8rpx;">
								<text>{{discount.platformReduction.name}}{{discount.platformReduction.data.money}} 元</text>
							</view>
						</view> -->
					<view class="dis-in mr10 f20" v-if="reduceArr">
						<view class="wh bs10 f-c b-d"
							style="color: #FA3534;border: 1rpx solid #FA3534;padding: 0rpx 8rpx;height: 32rpx;">
							<!-- 循环满减 -->
							<block v-if="reduceArr.data.type === '1'">
								<text>每满{{reduceArr.data.fullMoney}}减{{reduceArr.data.money}}</text>
							</block>
							<!-- 阶梯满减 -->
							<block v-else-if="reduceArr.data.type === '2'">
								<text v-for="(text,index) in reduceArr.data.moneyArr" :key='text.fullMoney'>
									<text>满{{`${text.fullMoney}减${text.money}`}}</text>
									<text v-if="index !== reduceArr.data.moneyArr.length-1">|</text>
								</text>
							</block>
						</view>
					</view>
					<view class="dis-in mr10 f20"
						v-if="discount.platformNewReduction && discount.platformNewReduction.money && discount.platformNewReduction.money>0">
						<!-- 平台新客立减 -->
						<view class="label-coupon f-y-c"
							style="color: #FA3534;border: 1rpx solid #FA3534;height: 32rpx;">
							<text>{{ $t('location.discountNew') }}{{discount.platformNewReduction.money}}</text>
						</view>
					</view>
					<view class="dis-in mr10 f20" v-if="discount.newReduction && discount.newReduction>0">
						<!-- 新客 -->
						<view class="label-coupon f-y-c"
							style="color: #FA3534;border: 1rpx solid #FA3534;height: 32rpx;">
							<text>{{ $t('location.storeCustomer') }}{{discount.newReduction}}</text>
						</view>
					</view>
					<!-- <view class="dis-in mr10 f20" v-if="deliveryArr">
						<view class="wh bs10 f-c b-d"
							style="color: #FA3534;border: 1rpx solid #FA3534;padding: 0rpx 8rpx;height: 32rpx;">
							<block v-if="deliveryArr.type === '1'">
								<text>{{ $t('location.deliveryFee') }}{{deliveryArr.money}}</text>
							</block>
							<block v-else-if="deliveryArr.type === '2'">
								{{ $t('good.delivery_fee') }}
								<text v-for="(text,index) in deliveryArr.moneyArr" :key='text.fullMoney'>
									<text>{{`${text.fullMoney}减${text.money}`}}</text>
									<text v-if="index !== deliveryArr.moneyArr.length-1">|</text>
								</text>
							</block>
						</view>
					</view> -->
					<!-- 折扣 end-->
					<!-- 公告 -->
					<view class="f24 c9 f-x-bt mt20 pb20">
						<view class="f-1 t-o-e">{{$t('good.notice')}}:{{moreSet.notice}}</view>
						<!-- <view class="f-y-c">更多信息
							</view> -->
					</view>
					<!-- 拼单开始 -->
					<view v-show="showTogether" class="f24 share-conent c9 mt20">
						<view class="f-bt share-conent-head">
							<view class="" style="font-size: 30rpx;">
								拼单进行中 <text style="padding-left: 10rpx;font-size: 20rpx;">更优惠|一起送|便捷分账</text>
							</view>
							<view class="">
								<u-button type="error" shape="circle" size="mini" :plain="true" @click="handleCancle()">
									取消</u-button>
							</view>
						</view>
						<view class="share-conent-body">
							<view class="share-conent-body-c flex f-w">
								<view class="meus" v-for="(item, index) in togetherUser" :key="index">
									<view class="meus-list">
										<view class="circle">
											<image class="circle-img" lazy-load :src="item.portrait"></image>
										</view>
										<view class="btns">
											<button type="error" class="status btn" size="mini" :plain="true"
												v-if="item.status==1">选购中</button>
											<button type="primary" class="status btn" size="mini" :plain="true"
												v-if="item.status==2">已选好</button>
										</view>
									</view>
								</view>
								<view class="add circle f-c">
									<u-icon color="#FF9E7A" size="28" name="plus" @click="hanleShowShare()"></u-icon>
								</view>
							</view>
							<view class="share-conent-body-c mt20 f-bt">
								<view class="txt">微信好友一起点餐，快速分账~</view>
								<view class="btns">
									<button type="error" class="detail btn" size="mini" :plain="true"
										@click="showDetails()">拼单詳情</button>
									<button :plain="true" class="share btn" size="mini"
										@click="hanleShowShare()">邀请好友</button>
								</view>
							</view>
						</view>
					</view>
					<!-- 拼单结束 -->
				</view>
			</view>
			<!-- 预订单通知 -->
			<view v-if="shopData.businessState && shopData.businessState.state===3" class="f-c" style="height: 160rpx;">
				<view class="bf t-c f-c p1" style="border-radius: 30rpx;">
					<view style="width: 32rpx;height: 32rpx;">
						<image class="wh" lazy-load src="/static/img_home/bighorn.png"></image>
					</view>
					<text>现在预订，{{timeText.text[0]}}<text
							:style="{color:tColor}">{{timeText.time}}</text>{{timeText.text[1]}}</text>
				</view>
			</view>
			<!-- 下半部分 -->
			<view :class="showTogether ? 'pt20':''" class="store-conent">
				<view :style="showTogether ? 'top: 25rpx;':'top: 0rpx;'" class="spell-order"
					v-if="moreSet.distributionSupport.includes('3')">
					<button class="sqbtn" type="default" @click="hanleShowShare()">好友拼单</button>
				</view>
				<view class="tabNav" :style="tabNavStyle">
					<tab-nav gutter="60" :activeColor="tColor" inactiveColor="#666666" :height="tabNavHeight"
						:bg-color="'#ffffff'" :current-index="current" :list="tabs" @change="tabsChange"></tab-nav>
				</view>
				<!-- 商品list -->
				<view v-show="current === 0">
					<GoodsList
						ref="GoodsListRef"
						:storeId="params.storeId"
						:tableId="params.tableId"
						:scroll="handlerScroll"
						:together.sync='togetherId'
						:callbackScroll.sync="scrollTop"
						:showTogether.sync="showTogether"
						:togetherUser.sync="togetherUser"
						@setTogether='setTogetherId'
						@cancelTimer="cancelTimer"
						@bigenTimer="bigenTimer"></GoodsList>
				</view>
				<!-- 评价 -->
				<!-- <view v-show="current === 1">
					<CommentList ref="CommentList" :shopData="shopData" :storeId="params.storeId"
						:scroll="handlerScroll"></CommentList>
				</view> -->
				<!-- 商店 -->
				<view v-show="current === 1">
					<StoreInfo ref="StoreInfo" :shopData="shopData" :moreSet="moreSet"></StoreInfo>
				</view>
				<!-- 应用 -->
				<!-- <view v-show="current === 3">
					<Plugin ref="StoreInfo" :shopData="shopData"></Plugin>
				</view> -->
			</view>
		</view>

		<!-- 领取优惠券 -->
		<CouponsPopup v-model="couponsShow" :data="couponsPopupData" :shopData="shopData"></CouponsPopup>
		<!-- 收藏有礼 -->
		<u-modal v-model="giftShow" :zoom="false" title="店铺收藏成功" :title-style="{paddingTop:'20rpx',textAlign:'center'}">
			<view class="p02 pb40">
				<view class="f26 t-c w100 c6">优惠券已发放到您的账户</view>
				<view class="f-y-c mt20 collection">
					<view class="f-c-c bs10 mr20"
						style="padding:0 15rpx;padding-bottom: 10rpx;background: #f9e1bb;color: #bc7436;">
						<text class="f46">
							<text class="f24">$</text>
							<text>{{collectionData.money}}</text>
						</text>
						<text class="f24" style="margin-top: -16rpx;">满{{collectionData.fullMoney}}可用</text>
					</view>
					<view class="f-col mr10">
						<text>{{collectionData.money}}元代金券</text>
						<text class="f24 c6">有效期至{{collectionData.day}}</text>
					</view>
					<view style="margin-left: auto;">已领取</view>
				</view>
			</view>
		</u-modal>
		<!-- 弹窗优惠券 -->
		<!-- <ActivePopup></ActivePopup> -->
		<!-- 加载 -->
		<Load :show="showLoad"></Load>
		<mg-popup v-model="showShare" position="middle">
			<view class="bf t-c bs15" style="width: 540rpx;">
				<view class="m30">
					<p class='c0 wei f30'>提示</p>
					<p class="c6 f26 mt10">邀请链接已生成快分享给你的好友吧</p>
				</view>
				<view class="f-row hlt f-x-bt" style="height: 100rpx;">
					<button hover-class='be' class="bs0 p0 f-1 bf f30 h100 f-c c9" @click="showShare=false">取消</button>
					<sq-btn cname="f-1 bf f30 h100 f-c hll" w='270' h='100' :sname="{color:tColor}" t='分享' type="3"
						@refresh='hanleCloseShare'></sq-btn>
				</view>
			</view>
		</mg-popup>
	</view>
</template>

<script>
	import {
		mapMutations,
		mapState,
		mapActions
	} from "vuex";
	import utils from '@/common/utils.js'
	import TabNav from '@/components/TabNav.vue'
	import Load from '@/components/Load.vue'
	import CouponsPopup from './components/CouponsPopup.vue'
	import GoodsList from './components/GoodsList.vue'
	import CommentList from './components/CommentList.vue'
	import StoreInfo from './components/StoreInfo.vue'
	// import Plugin from './components/Plugin.vue'
	import ActivePopup from './components/ActivePopup.vue'
	import mgPopup from '@/components/common/popup.vue'
	import sqBtn from '@/components/common/sq-btn.vue'

	export default {
		name: 'store',
		components: {
			TabNav,
			CouponsPopup,
			GoodsList,
			CommentList,
			StoreInfo,
			Load,
			ActivePopup,
			mgPopup,
			sqBtn,
		},
		computed: {
			tabNavStyle() {
				//部分机型rpx转化px丢失精度过大 所以-1px
				return `position:sticky;top:${this.statusNavBarHeight-1}px;z-index: 3;width: 100%;`
			},
			reduceArr() {
				if (this.discount.reduce instanceof Array) return false
				return this.discount.reduce
			},
			deliveryArr() {
				if (this.discount.delivery instanceof Array) return false
				return this.discount.delivery
			},
			...mapState({
				user: state => state.user,
				uId: state => state.user.id || '',
			}),
		},
		data() {
			return {
				claimedCoupons: [],
				unclaimedCoupons: [],
				timeText: {
					time: '',
					text: []
				},
				mustCategoryIndex: '', //用来跳转必选商品的分类位置
				params: {
					storeId: null
				},
				showStoreTitle: false, //暂时用来显示点名全称
				couponsPopupData: {}, //优惠券信息
				vipCouponsData: {}, //优惠券信息
				collectionData: {}, //收藏有礼
				false: true, //收藏有礼弹窗
				isCollection: 2, //店铺收藏
				goodsId: false, //从大牌臻选进来会传goodsid
				shopData: {}, //商户信息
				moreSet: {
					distributionSupport: []
				}, //后台设置
				categorySet: {}, // 总店设置
				discount: {}, // 折扣信息
				showLoad: true,
				handlerScroll: '', //将scroll信息传给子组件
				couponsShow: false, //领取优惠券弹窗
				opacity: 0, //搜索框透明度
				scrollTop: '', // 整个页面的scroll的滚动位置
				tempScrollTop: 0, //记录页面滚轴位置
				tabNavHeight: 81, //tabNav高度单位rpx
				current: 0,
				tabs: [{
					name: this.$t('good.tabnev_all_good')
				},
				//  {
				// 	name: this.$t('good.evaluate')
				// }, 
				{
					name: this.$t('good.merchant')
				}],
				scrollCurrentTopArr: [0, 0, 0], //记录页面滚动条历史位置
				scrollDot: '',
				showTogether: false,
				togetherUser: [],
				togetherId: 0,
				timer: null,
				showShare: false,
				giftShow: false
			}
		},
		watch: {
			scrollTop(val) {
				console.log(val)
				uni.pageScrollTo({
					scrollTop: val,
					duration: 50
				})
			}
		},
		onReachBottom() {
			this.nextPage()
		},
		onPageScroll(e) {
			this.scroll(e)
		},
		async onLoad(option) {
			this.showLoad = true
			this.params = {
				storeId: option.storeId,
				tableId: option.tableId,
				lat: this.latLng.latitude,
				lng: this.latLng.longitude
			}
			if (option.goodsId) { //从大牌臻选进来
				this.goodsId = option.goodsId
			}
			if (option.togetherId && !option.cancel) {

				this.showTogether = true
				this.togetherId = option.togetherId
				this.handleShare()

			}
			if (option.cancel) {
				this.togetherId = 0
				this.showTogether = false
			}
			if (option.storeId) {
				this.GetPxToRpxRate()
				this.getSystem()
				await this.getLocInfo()
				this.params = {
					storeId: option.storeId,
					tableId: option.tableId,
					lat: this.latLng.latitude,
					lng: this.latLng.longitude
				}
			}

			await this.init()
			setTimeout(() => {
				this.showLoad = false
			}, 300)
			await this.getConfig({
				name: 'orderSet',
				api: this.api.orderSet,
				data: {}
			})
		},
		onShow() {
			this.collectionData.day = this.formatDate(3) //收藏有礼
			// 获取购物车信息 需要等storeId传递到子组件
			this.refresh()
		},
		onHide() {
			this.cancelTimer()
		},
		onUnload() {
			this.cancelTimer()
		},
		onReady() {
			this.getScrollDot()
		},
		methods: {
			...mapActions(["setSystemInfo", "getConfig"]),
			showDetails() {
				this.$refs.GoodsListRef.showDetails()
			},
			bigenTimer() {
				if (!this.timer) {
					this.timer = setInterval(() => {
						setTimeout(this.refresh, 0)
					}, 6000); //6秒刷新一次
				}
			},
			cancelTimer() {
				if (this.timer) {
					clearInterval(this.timer)
					this.timer = null
				}
			},
			async cancelTogether() {
				let {
					code
				} = await this.util.request({
					url: this.api.cancelTogether,
					method: 'GET',
					data: {
						storeId: this.params.storeId,
						tableId: this.params.tableId,
						togetherId: this.togetherId
					}
				})
				this.togetherId = 0
				this.showTogether = false
				this.refreshUser()
			},
			handleCancle() {
				this.util.modal('确定取消不会保留本次拼单内容', '提示').then(res => {
					this.cancelTogether()
				})
			},
			async handleShare() { //开始拼单
				let {
					data,
					code
				} = await this.util.request({
					url: this.api.bigenTogether,
					method: 'GET',
					data: {
						storeId: this.params.storeId,
						tableId: this.params.tableId,
						togetherId: this.togetherId
					}
				})
				if (code == 1) {
					this.showTogether = true
					this.togetherId = data.togetherId
					console.log(this.user)
					return this.togetherId
				}
				console.log(data)
				return 0;
			},
			hanleShowShare() {
				if (!this.togetherId) {
					this.handleShare()
				}
				this.showShare = true
			},
			hanleCloseShare() {
				this.showShare = false
			},
			handleTogether(e) {
				console.log(e)
				this.showTogether = true
			},
			getTimeText() {
				if (!this.shopData.businessState || !this.shopData.businessState.msg) return
				let time = ''
				let text = ''
				try {
					time = this.shopData.businessState.msg.match(/[0-9]{2}/g)[0] + ':' + this.shopData.businessState.msg
						.match(/[0-9]{2}/g)[1]
					text = this.shopData.businessState.msg.split(time)
				} catch (e) {
					console.log(e)
					//TODO handle the exception
				}

				this.timeText.time = time
				this.timeText.text = text
			},
			formatDate(day) {
				let timeStamp = new Date().valueOf() + day * 24 * 60 * 60 * 1000
				return new Date(timeStamp).format("yyyy-MM-dd")
			},
			collectionStore: utils.debounceImmediate(async function() {
				let {
					data
				} = await this.util.request({
					url: this.api.scjk,
					method: 'GET',
					data: {
						collectionId: this.params.storeId,
						type: '1'
					}
				})
				if (this.isCollection === 1) {
					this.isCollection = 2
					this.util.message('取消收藏成功', 3)
				} else {
					this.isCollection = 1
					if (data && data.money) {
						this.collectionData = data
						this.collectionData.day = this.formatDate(3)
						this.giftShow = true
					} else {
						this.util.message('收藏成功', 3)
					}
				}
			}),
			nextPage() {
				if (this.current === 1) { // 评价
					this.$refs.CommentList.nextPage()
				}
				return
			},
			...mapMutations(["setShopGoodsInfo", "setCanOrder", "setStoreInfo"]),
			async init() {
				//获取商品列表 门店信息
				await this.fetchData()
				//获取优惠券信息
				// this.getCoupons()
				//this.refresh()
				// setTimeout(async () => {
					this.addFwjl({
						storeId: this.params.storeId,
						origin: '2'
					})
				// }, 500)
				if (this.user.isVip) {
					this.getVipCoupons()
				}
				// if (!this.uId) {
				// 	this.refreshUser()
				// }
			},
			async refresh() {
				//获取购物车信息 需要等storeId传递到子组件
				await this.$nextTick(() => {
					if (this.$refs.GoodsListRef && this.$refs.GoodsListRef.getCartList) {
						this.$refs.GoodsListRef.getCartList()
					} else {
						console.log('GoodsList 组件或方法未就绪')
					}
				})
			},
			async getCoupons() {
				let {
					data
				} = await this.util.request({
					url: this.api.qtsjjh,
					method: 'GET',
					data: {
						storeId: this.params.storeId,
						tableId: this.params.tableId,
						location: 1
					},
					is_login: 0
				})
				this.couponsPopupData = data
			},
			async getVipCoupons() {
				let {
					data
				} = await this.util.request({
					url: this.api.vipsc,
					method: 'GET',
					data: {
						storeId: this.params.storeId,
						tableId: this.params.tableId
					}
				})
				this.vipCouponsData = data
			},
			async vipcoupon() {
				if (!this.isLogin) {
					this.go('navigateTo', `/yb_o2ov2/my/login`)
					return
				}
				uni.showModal({
					title: '提示',
					content: '您确定要兑换此红包吗？',
					success: async (res) => {
						if (res.confirm) {
							let vipData = await this.util.request({
								url: this.api.vipcpe,
								mask: '兑换中',
								method: 'POST',
								data: {
									storeId: this.params.storeId,
									tableId: this.params.tableId,
									couponId: this.vipCouponsData.id
								}
							})
							this.getVipCoupons()
							if (vipData.code == 1) {
								return this.util.message(vipData.msg || vipData.data, 1, 2000)
							} else {
								return this.util.message(vipData.msg || vipData.data, 3, 2000)
							}
						}
					}
				});
			},
			async fetchData() {
				await this.getStoreInfo()
				await this.getGoodsList()

			},
			async getStoreInfo() {
				let {
					data
				} = await this.util.request({
					url: this.api.getStoreInfo,
					method: 'GET',
					data: this.params,
					is_login: 0
				})
				this.shopData = data.shopData
				this.isCollection = data.shopData.isCollection
				this.moreSet = data.moreSet
				this.categorySet = data.categorySet
				this.discount = data.discount
				this.getTimeText()
				this.setShopGoodsInfo(data)
				this.setStoreInfo(data.shopData)
			},
			async getGoodsList() {
				let {
					data
				} = await this.util.request({
					url: this.api.shopGoodsInfo,
					method: 'GET',
					data: {
						storeId: this.params.storeId,
						tableId: this.params.tableId,
					},
					is_login: 0
				})
				this.$refs.GoodsListRef.backupCategory(data)
				//单点不送 商品ids
				let singleIds = []
				data.forEach(category => {
					category.goods.forEach(item => {
						if (item.aloneType === '1') {
							singleIds.push(item.id)
						}
					})
				})
				//必选分类下的所有商品（cartlist没有返回商品的分类id 所以需要分类下所有商品id）
				let mustIds = []
				data.forEach((category, index) => {
					if (category.isRequire === '1') {
						this.mustCategoryIndex = index
						category.goods.forEach(item => {
							mustIds.push(item.id)
						})
					}
				})
				this.setCanOrder({
					singleIds,
					mustIds
				}) //设置单点不送 和必选商品的id
			},
			async tabsChange(e) {
				this.current = e;
				this.scrollCurrentTopArr[this.current] = this.tempScrollTop
				if (this.tempScrollTop > this.scrollDot && this.scrollCurrentTopArr[e] < this.scrollDot) {
					this.scrollTop = this.scrollDot
				}
				if (this.tempScrollTop <= this.scrollDot) {
					this.scrollTop = this.tempScrollTop
				}
				if (e === 1 && !this.$refs.CommentList.init) {
					await this.$refs.CommentList.fetchData()
					this.scrollTop = 0 //计算吸顶评价分类必须的一步
					this.$nextTick(() => {
						this.$refs.CommentList.getFixedDot()
					})
				}
			},
			scroll(e) {
				var top = e.scrollTop
				this.tempScrollTop = top
				// this.opacity = (top - 200 / this.pxToRpxRate) / 50 > 1 ? 1 : (top - 200 / this.pxToRpxRate) /50
				this.opacity = top > 50 ? 1 : 0
				this.handlerScroll = top
			},
			getScrollDot() {
				// 以下页面跳转指的是v-show
				// 当前ScrollTop超过这个点(ScrollDot) 跳转页面的历史scroll位置最低也得是ScrollDot
				// 小于ScrollDot 跳转页面的scroll位置等于当前页面的ScrollTop
				let query = uni.createSelectorQuery().in(this);
				console.log(query, 'query')
				query.select('.tabNav').boundingClientRect((res) => {
					if (!res) return
					this.scrollDot = res.bottom - Math.floor(this.statusNavBarHeight + this.tabNavHeight / this
						.pxToRpxRate)
					// console.log('res',res)
				}).exec()

			},
			async GetPxToRpxRate() {
				if (!this.pxToRpxRate) {
					await this.setSystemInfo()
				}
			},
		},
		onShareAppMessage() {

			let p = `yb_o2ov2/home/<USER>
			return this.util.mpShare({
				t: this.shopData.name,
				p,
			})

		},
		// onShareTimeline(e) {
		// 	return {
		// 		title: this.system.shareTitle,
		// 		imageUrl: this.getImgS(this.system.shareIcon),
		// 	}
		// },
	}
</script>

<style scoped lang="scss">
	.collection {
		border-radius: 20rpx;
		border: 1px solid #f5f6f9;
		padding: 15rpx;
	}

	.info {
		position: relative;
		padding-top: 200rpx;
	}

	.bg-img {
		position: absolute;
		overflow: hidden;
		width: 100%;
		height: 100%;
		top: -118rpx;
		z-index: 2;
	}

	.card {
		position: relative;
		border-radius: 30rpx 30rpx 0 0;
		padding: 20rpx 20rpx 0;
	}

	.gyhqc {
		background: #FE624B;
		height: 44rpx;
		border-radius: 6rpx;
		border: 1px solid #ff7569;

		.gyhql {
			min-width: 80rpx;
			padding: 3rpx;
		}

		.gyhqr {
			// padding: 0 16rpx;
			min-width: 56rpx;
			border-left: 1px dashed #ff7569;
		}

		.gdot {
			width: 8rpx;
			height: 8rpx;
			border-radius: 4rpx;
			background: #f5f5f5;
			right: -4rpx;
		}

		.gdot1 {
			border-bottom: 1px solid #ff7569;
			border-left: 1px solid #ff7569;
			border-right: 1px solid #ff7569;
			border-top: 1px solid #f5f5f5;
			top: -2rpx;
		}

		.gdot2 {
			border-top: 1px solid #ff7569;
			border-left: 1px solid #ff7569;
			border-right: 1px solid #ff7569;
			border-bottom: 1px solid #f5f5f5;
			bottom: -2rpx;
		}
	}

	.gyhqc2 {
		border: 1px solid #F3D58C;

		.gdot1 {
			border-bottom: 1px solid #F3D58C;
			border-left: 1px solid #F3D58C;
			border-right: 1px solid #F3D58C;
			border-top: 1px solid #f5f5f5;
			top: -2rpx;
		}

		.gdot2 {
			border-top: 1px solid #F3D58C;
			border-left: 1px solid #F3D58C;
			border-right: 1px solid #F3D58C;
			border-bottom: 1px solid #f5f5f5;
			bottom: -2rpx;
		}
	}

	.search {
		width: 340rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		margin-left: 100rpx;
		background: #f5f5f5;
		border-radius: 60rpx;
		line-height: 60rpx;
	}

	.label-coupon {
		font-size: 20rpx;
		height: 36rpx;
		// line-height: 38rpx;
		color: #c57771;
		border: 1rpx solid #f0dbdb;
		border-radius: 8rpx;
		padding: 0rpx 8rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}

	.store-conent {
		position: relative;
		background-color: #fff;
	}

	.spell-order {
		position: absolute;
		z-index: 5;
		left: 60%;
		top: 0rpx;
	}

	.spell-order-btn {
		width: 67.5rpx;
		height: 29rpx;
		line-height: 29rpx;
		border-radius: 15rpx;
	}

	.share-conent {
		border: 2px solid #FFCCCC;
		border-radius: 20rpx;
		overflow: hidden;
	}

	.share-conent-head {
		padding: 26rpx 18rpx;
		border-bottom: 2rpx solid #FFCCCC;
		background: linear-gradient(-90deg, #FFEFE8 0%, #FFDED0 100%);
	}

	.share-conent-body {
		padding: 12rpx 18rpx 20rpx 18rpx;
		min-height: 95rpx;
		background: #FFF8F5;

		.share-conent-body-c {
			line-height: 50rpx;

			.txt {
				color: #333;
			}

			.btns {
				.btn {
					background: #FFFFFF;
					color: #FC6953;
					border: 2rpx solid #FFCCCC;
					display: inline-flex;
					width: auto;
					font-size: 22rpx;
					padding-top: 1px;
					height: 50rpx;
					line-height: 50rpx;
					padding: 0 20rpx;
				}

				.share {
					margin-left: 15rpx;
					background: linear-gradient(90deg, #FFA17D 0%, #FC624D 100%);
					color: #fff;
				}
			}

			.circle {
				border-radius: 50%;
				width: 68rpx;
				height: 68rpx;
				overflow: hidden;
				background: #FFE6DD;
				border: 1px solid #FF9E7A;
				margin-right: 25rpx;

				.circle-img {
					width: 68rpx;
					height: 68rpx;
					border-radius: 50%;
				}
			}

			.meus-list {
				position: relative;
				padding-bottom: 25rpx;

				.status {
					font-size: 18rpx;
					height: 30rpx;
					padding: 0 10rpx;
					line-height: 30rpx;
					position: absolute;
					bottom: 0;
				}
			}
		}
	}
</style>