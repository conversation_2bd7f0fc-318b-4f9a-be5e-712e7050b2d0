<template>
	<view class="bf wh">
		<!-- 头部 start -->
		<view class="page-navigation-bar">
			<view class="go-icon">
				<text class="iconfont iconback f40 " @click="go('back')"></text>
			</view>
			<text>{{$t('good.remark')}}</text>
		</view>
		<!-- 头部 end -->
		<view class="p2">
			<!-- 文本编辑 -->
			<view class="p-r">
				<textarea :maxlength="maxlength" class="p1 wh bs20 bfa" type="textarea" style="height:300rpx;" placeholder-style="color:#999999" v-model="value" :placeholder="$t('home.tip')" />
				<view class="p-a" style="bottom: 10rpx;right: 15rpx;"><text class="c9">{{value.length || 0}}</text>/{{maxlength}}</view>
			</view>
			<!-- <view class="c9 f24 mt30">推荐标签</view>
			<view class="mt10">
				<view class="r-label" :style="{border:tColor,color:fontColor}" v-for="(item,index) in sotreLabel" :key="index" @click="add(item)">{{item}}</view>
			</view> -->
		</view>
		<button class="btn" :style="{background:tColor,color:fontColor}" type="default" @click="back">{{$t('common.submit')}}{{$t('good.remark')}}</button>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				value:'',
				maxlength:50,
				// sotreLabel:[]
			}
		},
		watch:{
			value(val){
				this.value = val.substring(0,this.maxlength)
			}
		},
		onLoad(option) {
			console.log(option)
			this.value = option.note || ''
			// this.sotreLabel = option.sotreLabel.split(',')||[]
		},
		methods:{
			add(item){
				if(this.value === ''){
					this.value =  item
				}else{
					this.value = this.value + '，' + item
				}
			},
			back(){
				uni.setStorageSync('_temp_note',this.value)
				this.go('back')
			}
		}
	}
</script>

<style scoped lang="scss">
	.r-label{
		display: inline-block;
		border-radius: 10rpx;
		font-size: 24rpx;
		padding: 6rpx 12rpx;
		color: #ffaa00;
		border:1rpx solid #ffaa00;
		margin-right: 20rpx;
		margin-top: 10rpx;
	}
	.btn{
		width: 700rpx;
		margin: 0 auto;
		margin-top: 80rpx;
	}
</style>
