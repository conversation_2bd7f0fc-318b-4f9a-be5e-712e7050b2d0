<template>
	<view>
		<!-- 返回 -->
		<view class="p-f" :style="{top:`${menuButtonTop}px`,left:`20rpx`,zIndex:4,width:'50rpx',height:'56rpx'}" @click="handleBack">
			<text class="iconfont iconback f40"></text>
		</view>
		<!-- 商品图片 -->
		<view class="dis-b w100" style="height: 750rpx">
			<u-swiper :list="swiper" name='icon' :height='750' v-if="swiper.length" imgMode='aspectFill'></u-swiper>
			<image class="wh" :src="result.icon" mode="scaleToFill" v-else></image>
		</view>
		<!-- 标签 -->
		<view class="f-bt bf p2" v-if="result.id">
			<view class="f-y-bt f-1">
				<view class="f32 wei">{{result.name}}</view>
				<view class="mt10 f-w f-1 flex">
					<text class="mr10" v-for="tip in tips(result)" :key="tip"
						style="font-size: 24rpx;padding: 2rpx 6rpx;background: #f5f6f9;border-radius: 10rpx;">{{tip}}</text>
				</view>
				<view class="f24 c6 mt10"  v-if="system.switch.saleShow==1">{{$t('good.monthly_sales')}}{{result.salesNum||0}}</view>
				<view v-if="result.activityGoodData.type>0" class="mt5 f-row f20 mb10" :style="{color:tColor}">
					<view v-if="result.activityGoodData.type<3" class="f-row bs5 b-d p1 l-h1" :style="{borderColor:tColor}">
						<view class="">{{result.activityGoodData.type==1?'特价':Number(result.activityGoodData.discount)+'折'}}</view>
						<view class="ml10 sphdir" v-if="result.activityGoodData.limitNum>0" :style="{borderColor:tColor}">限{{result.activityGoodData.limitNum}}份</view>
					</view>
					<view v-else-if="result.activityGoodData.type==3">
						<text>商品立减{{sl+result.activityGoodData.discount}}</text>
						<text v-if="result.activityGoodData.limitNum>0" class="ml10">限{{result.activityGoodData.limitNum}}份</text>
					</view>
					<view v-else>{{result.activityGoodData.type==4?`第二件${Number(result.activityGoodData.discount)}折`:'买一送一'}}</view>
				</view>
				<view class="mt30">
					<text style="color: #ff5454;">$</text>
					<block v-if="result.activityGoodData.type<1||result.activityGoodData.type>=4">
						<text class="f38 wei mr10" style="color: #ff5454;">{{result.price}}</text>
					</block>
					<block v-else>
						<text class="f38 wei mr10" style="color: #ff5454;">{{result.activityGoodData.activityMoney}}</text>
						<text class="t-d-l nowei c9 f26">{{result.price}}</text>
					</block>
					<text v-if="result.crossedPrice" class="f24 c9 t-d-l mr10">${{result.crossedPrice}}</text>
					<text class="f24 c9 ml10">
						<text v-if="Number(result.boxMoney) !== 0">包装费${{result.boxMoney}}</text>
						<text v-if="system.switch.unitShow==1" class="ml10">/{{result.unit}}</text>
					</text>
				</view>
			</view>
			<view class="f-y-bt f-0">
				<view class="t-r" @click="great">
					<view><text class="c9 mr10 iconfont iconlike f28" :style="{color:greatedGoods?'#ff1100':''}"></text>
					</view>
					<view class="c9">{{$t('good.like')}}</view>
				</view>
				<block v-if="businessState.state === 1">
					<view v-if="result.isSpecs ==='1'||result.isAttr ==='1'||result.isMaterial ==='1'">
						<text
							style="padding: 5rpx 16rpx;border-radius: 15rpx;font-weight: bold;font-size: 30rpx;"
							:style="{background:categorySet.delColor,color:categorySet.addColor}"
							@click="getSpecs">
							{{$t('good.select_specs')}}</text>
					</view>
					<view v-else class="f-x-bt" style="min-width: 150rpx;">
						<block v-if="carNum>0">
							<view class="f-y-e pb10" style="height: 60rpx;" @click.stop="removeCart(goodsId)">
								<view class="f-c" :class="gwcstyle==='1'?'bs10':'bsf'"
									style="height: 48rpx;width: 48rpx;text-align: center;"
									:style="{border: `1px solid ${categorySet.delColor}`}"
									>
									<!-- 减号 -->
									<text class="iconfont iconminus f20 bs10 wei" style="padding: 4rpx 0;"
										:style="{color:categorySet.addColor2}"></text>
								</view>
							</view>
							<view style="padding-bottom: 5rpx;">{{carNum}}</view>
						</block>
						<view v-else style="width: 20rpx;height: 100%;"></view>
						<view class="f-y-e pb10" style="height: 60rpx;" @click.stop="addCart(e,goodsId)">
							<!-- 起购 -->
							<view v-if="MINNUM !=='1'&&carNum===0" class="bs10 f24 wei" style="padding: 2rpx 12rpx;"
								:style="{background:categorySet.delColor,color:categorySet.addColor}">
								<text>{{MINNUM}}{{$t('good.min_purchase')}}</text>
							</view>
							<!-- 加号 -->
							<view v-else class="f-c" :class="gwcstyle==='1'?'bs10':'bsf'"
								style="height: 48rpx;width: 48rpx;text-align: center;"
								:style="{background: categorySet.delColor}">
								<text class="iconfont iconplus f20 bs10 wei" style="padding: 4rpx 0;"
									:style="{color:categorySet.addColor}"></text>
							</view>
						</view>
					</view>
				</block>
			</view>
		</view>
		<!-- 詳情 -->
		<view class="mt20 p02 bf pb115" style="padding:0 6px 78px;" v-if="result.id">
			<tab-nav height="80" :activeColor="tColor" bg-color="#FFFFFF" :current-index="current" :list="tabs">
			</tab-nav>
			<view class="flex mt20">
				<view class="detail-title">{{$t('good.product_intro')}}：</view>
				<view class="detail-content">{{result.body}}</view>
			</view>
			<view class="flex mt20" v-if="result.details">
				<u-parse  :html="result.details" />
			</view>
		</view>
		<!-- 闭店 休息中 -->
		<view v-if="businessState.state!==1" class="t-c f30"
			style="z-index:3;position: fixed;bottom: 0;width: 100%;height: 160rpx;line-height: 100rpx;background: rgba(0,0,0,.9);">
			<block v-if="businessState.state===2">
				<view class="cf">
					<text class="wei">{{$t('good.store_closed')}}</text>
					<text>（{{businessState.msg}}）</text>
				</view>
			</block>
			<block v-else-if="businessState.state===0">
				<view class="cf"><text class="wei">{{$t('good.store_paused')}}</text></view>
			</block>
		</view>
		<!-- 购物车 -->
		<view v-else style="z-index:3;position: fixed;bottom: 0;width: 100%;">
			<ShoppingCart :cartInfo="cartInfo" :storeId="storeId" @getAniEndDot="handleGetDotEnd" background="#ffffff">
			</ShoppingCart>
		</view>
		<!-- 選擇 -->
		<SelectSpecs :show.sync="specsShow" :cartList="cartList" :goodsId="specsGoodsId" :specsData="specsData"
			:goodsInfoSpecs="goodsInfoSpecs" @getXY="handleGetDotStart" @startAni="handleStartAni" @addCart="addCart" @removeCart="removeCart"></SelectSpecs>
	</view>
</template>

<script>
	import {
		mapMutations,
		mapActions
	} from "vuex";
	import utils from '@/common/utils.js'
	import TabNav from '@/components/TabNav.vue'
	import SelectSpecs from './components/SelectSpecs.vue'
	import ShoppingCart from './components/ShoppingCart.vue'
	export default {
		components: {
			TabNav,
			SelectSpecs,
			ShoppingCart,
		},
		data() {
			return {
				gwcstyle: '1', // 1正方形 2圆形
				businessState: {},
				params: {},
				greatedGoods: false,
				goodsInfoSpecs: {},
				specsData: {},
				cartList: [],
				specsShow: false,
				current: 0,
				tabs: [{ name: '詳情' }],

				// option传递参数
				result: {},
				storeId: '',
				tableId: '',
				cartInfo: {},
				carNum: '',
				goodsId: '',
				specsGoodsId: '',
				MAXNUM: '',
				MINNUM: '',
				categorySet:{},
				swiper:[],
				loading: false,
			}
		},
		computed: {
			tips() {
				return good => {
					return (good.showLabel) || []
				}
			}
		},
		async onLoad(option) {
			if(option.storeId){
				this.GetPxToRpxRate()
				this.getSystem()
				await this.getLocInfo()
				this.params = {
					storeId:option.storeId,
					tableId:option.tableId,
					lat:JSON.parse(uni.getStorageSync('locationInfo')).latitude,
					lng:JSON.parse(uni.getStorageSync('locationInfo')).longitude
				}
			}else{
				this.params = {
					storeId: option.storeId,
					tableId:option.tableId,
					lat: JSON.parse(uni.getStorageSync('locationInfo')).latitude,
					lng: JSON.parse(uni.getStorageSync('locationInfo')).longitude
				}
			}
			this.goodsId = option.goodsId
			this.storeId = option.storeId
			this.tableId = option.tableId
			this.refresh()
		},
		methods: {
			...mapMutations(["setShopGoodsInfo","setCanOrder"]),
			...mapActions(["setSystemInfo"]),
			handleBack() {
				const pages = getCurrentPages();
				if (pages.length > 1) {
					this.go('back');
				} else {
					// 如果没有上一页，跳转到首页
					this.go('reLaunch', '/yb_o2ov2/index/index');
				}
			},
			async great() {
				await this.util.request({
					url: this.api.saveGoodsZan,
					method: 'GET',
					data: {
						goodsId: this.goodsId,
						type: 1
					}
				})
				this.greatedGoods = !this.greatedGoods
				if (!this.greatedGoods) return
				this.util.message('讚成功', 3)
			},
			handleGetDotStart(x, y) {
				this.startXY = {
					x: x,
					y: y
				}
			},
			handleGetDotEnd(x, y) {
				this.endXY = {
					x: x,
					y: y
				}
			},
			getGoodInfo() {
				let good = this.cartInfo.data.find(t => t.goodsId === this.goodsId)
				if (good) {
					
					this.carNum = Number(good.num) || 0
					this.cartId = good.id || ''
				} else {
					this.carNum = 0
					this.cartId = ''
				}

			},
			async refresh() {
				this.fetchData()
				this.getStoreConfig()
				await this.getCartList()
				this.getGoodInfo()
			},
			async fetchData() {
				let { data } = await this.util.request({
					url: this.api.goodsDetail,
					method: 'GET',
					data: {
						goodsId: this.goodsId
					},
					is_login: 0
				})
				this.result = data
				this.swiper = data.media?data.media:[]
				this.MAXNUM = data.maxNum || ''
				this.MINNUM = Number(data.minNum) || 1
				this.greatedGoods = data.isZan === '0' ? false : true
			},
			async getStoreConfig() {
				let { data } = await this.util.request({
					url: this.api.getStoreInfo,
					method: 'GET',
					data: this.params
				})
				this.businessState = data.shopData.businessState
				this.categorySet = data.categorySet
				this.gwcstyle = data.categorySet.gwcstyle
				this.shopData = data.shopData
				this.setShopGoodsInfo(data)
				//单点不送 商品ids
				let singleIds = []
				//必选分类下的所有商品（cartlist没有返回商品的分类id 所以需要分类下所有商品id）
				let mustIds = []
				if(Array.isArray(data.data)){
					data.data.forEach(category => {
						category.goods.forEach(item => {
							if (item.aloneType === '1') {
								singleIds.push(item.id)
							}
						})
					})
					
					data.data.forEach(category => {
						if (category.isRequire === '1') {
							category.goods.forEach(item => {
								mustIds.push(item.id)
							})
						}
					})
				}
				
				this.setCanOrder({ singleIds, mustIds }) //设置单点不送 和必选商品的id
			},
			//获取商品规格
			async getSpecs() {
				this.specsShow = true
				let { data } = await this.util.request({
					url: this.api.getGoodsSpecs,
					method: 'GET',
					data: {
						id: this.goodsId
					}
				})
				this.specsGoodsId = this.goodsId
				this.specsData = data
				this.goodsInfoSpecs = {
					goodName: this.result.name || '',
					goodOriginPrice: this.result.price || 0
				}
			},
			async getCartList() {
				console.log('this.params.storeId',this.storeId)
				let { data } = await this.util.request({
					url: this.api.getCartList,
					method: 'GET',
					data: {
						storeId: this.storeId,
						tableId: this.tableId,
						item: '1' //1外卖 2店内 3快餐
					}
				})
				this.cartInfo = data
				this.cartList = data.data
			},
			async modifyCart(goodsId, type, id, spec) {
				let { count, data } = await this.util.request({
					url: this.api.saveShopCar,
					method: 'POST',
					contentType: 'application/json',
					data: {
						attribute: spec ? spec.attribute || null : null,
						groupId: spec ? spec.groupId || null : null,
						material: spec ? spec.material || null : null,
						storeId: this.storeId,
						tableId: this.tableId,
						goodsId: goodsId,
						type: type,
						id: id || null,
						item: '1', //1外卖 2店内 3快餐
					}
				})
				
				if(data.code == 99 ){
					this.util.message(data.msg || data, 3, 2000)
					const pages = getCurrentPages();
					const currentPage = pages[pages.length - 1];
					const route = currentPage.route;
					const fullPath = route + '?' + Object.entries(currentPage.options || {}).map(([key, val]) => `${key}=${val}`).join('&');
					uni.setStorageSync('login_redirect', fullPath);
					uni.navigateTo({
						url: `/yb_o2ov2/my/login`
					})
				}
				// 修改后会返回购物车信息  覆盖
				this.cartInfo = data
				this.cartList = data.data.reverse()
				return count
			},
			handleStartAni() {},
			addCart: utils.throttle(async function(e, goodsId, cIndex, gIndex, GetcartId = '', spec = '') {
				try {
					this.loading = true
					await this.modifyCart(goodsId, '1', GetcartId, spec)
					//本地加MINNUM
					this.getGoodInfo()
					if (this.carNum < this.MINNUM) {
						this.carNum = this.MINNUM
					}
					this.handleStartAni()
				} catch (error) {
					this.util.message(this.$t('good.sold_out'), 3)
				} finally {
					this.loading = false
				}
			}, 400),
			removeCart: utils.throttle(async function(goodsId, cIndex, gIndex, GetcartId = '', spec = '') {
				try {
					this.loading = true
					await this.modifyCart(goodsId, '2', GetcartId, spec)
					//本地减MINNUM
					this.getGoodInfo()
					if (this.carNum < this.MINNUM) {
						this.cartId = ''
						this.carNum = 0
					}
				} catch (error) {
					this.util.message('移除失败，请重试', 3)
				} finally {
					this.loading = false
				}
			}, 400),
			async GetPxToRpxRate(){
				if(!this.pxToRpxRate){
					await this.setSystemInfo()
				}
			},
		},
		onShareAppMessage() {
			let p = `yb_o2ov2/home/<USER>
			return this.util.mpShare({
				t: this.shopData.name,
				p,
			})
		},
		// onShareTimeline(e) {
		// 	return {
		// 		title: this.system.shareTitle,
		// 		imageUrl: this.getImgS(this.system.shareIcon),
		// 	}
		// },
	}
</script>

<style scoped lang="scss">
	.detail-title {
		flex-shrink: 0;
		width: 140rpx;
		color: #a5a5a5;
	}

	.detail-content {
		text-align: left;
		font-size: 28rpx;
		word-break: break-all;
	}
</style>
