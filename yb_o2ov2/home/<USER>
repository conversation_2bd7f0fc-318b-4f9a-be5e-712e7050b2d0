<template>
	<view class="bf" style="padding-bottom: 220rpx;">
		<!-- 头部 start -->
		<view class="bf posi-s w100 " :style="{top:0,height:`${statusNavBarHeight}px`,paddingTop:`${menuButtonTop}px`,zIndex:3}">
			<!-- 返回 -->
			<view class="p-a" :style="{top:`${menuButtonTop}px`,left:`20rpx`,zIndex:4}" @click="back">
				<text class="iconfont iconback f40"></text>
			</view>
			<view class="t-c f32 wei">
				<view>天天特价</view>
			</view>
		</view>
		<!-- @click="go('navigateTo',`/yb_o2ov2/home/<USER>" -->
		<view>
			<view v-for="(good,index) in goodsList" :key="good.id" class="card"
			@click="go('navigateTo',`/yb_o2ov2/home/<USER>">
				<!-- 商品左边图片 -->
				<view class="p-r mr20">
					<view class="bs10" style="width: 140rpx;height: 140rpx;">
						<image :src="good.icon" class="wh" mode="aspectFit"></image>
					</view>
					<!-- 招牌 -->
					<view v-if="good.hotsaleType === '1'" class="p-a bs10 f20 wei"
						style="top: -10rpx;right: -10rpx;padding: 0 6rpx;" :style="{background:tColor,color:fontColor}">招牌
					</view>
				</view>
				<!-- 右侧介绍 -->
				<view class="f-g-1">
					<view style="min-height: 150rpx;">
						<view class="wei f30">{{good.name}}</view>
						<view v-if="false"><text class="f22 bs10"
								style="padding: 2rpx 8rpx;background: #FEF8E3;color: #EF8D38;">网友点名推荐</text></view>
						<!-- 商品简介 -->
						<view v-if="good.body" class="f24 c9" style="width: 320rpx;">{{good.body.length>25?good.body.substring(0,25)+'...':good.body}}</view>
						<view class="f24 c9 mt10">月售 {{good.salesNum||0}} 库存 {{good.stock}}</view>
						<view class="f30 f-bt">
							<view>
								<text class="crb">${{good.money}}</text>
								<text class="c9 f26 ml10 t-d-l">{{good.price}}</text>
							</view>
							<view class="bs10 p02 ml20 f24" style="height: 46rpx;line-height: 46rpx;" :style="{background:tColor,color:fontColor}"><text>去下单</text></view>
						</view>
					</view>
				</view>
			</view>
			<mescroll-empty v-if="goodsList.length === 0"
				:option="{icon:'/static/empty/4.png',tip:'~ 暂无特价商品哦 ~'}"></mescroll-empty>
			<u-loadmore  v-if="goodsList.length!==0" @loadmore="nextPage" :status="status" />
		</view>
	</view>
</template>

<script>
	import {
		mapState,
	} from 'vuex'
	import utils from '@/common/utils.js'
	export default {
		components:{
			
		},
		data(){
			return {
				goodsList:[],
				storeId:undefined,
				lenovoArr:[],
				showGoods:false, 
				lenovoShow:false, 
				keywords:[],
				keyWord:'',
				showResult:false, 
				searchRecords:[],
				placeholderText:'',
				params:{
					page: 1,
					size: 10,
				},
			}
		},
		computed:{
			...mapState({
				layout:state=>state.layout.index.body
			}),
			scrollHeight(){
				return this.wHeight - 104 - this.statusNavBarHeight
			}
		},
		onLoad(option) {
			this.getGoodsList()
		},
		methods:{
			lenovo:utils.debounce(async function(){
				let {data} =  await this.util.request({
					url: this.api.lenovo,
					method: 'GET',
					data:{keyword:this.keyWord}
				})
				// this.lenovoArr = data
			},400),
			back(){
				this.go('back')
			},
			fetchData(){
				this.$refs.storelist.setApi(this.api.storeList,{lat:this.latLng.latitude,lng:this.latLng.longitude,keyword:this.keyWord})
				this.$refs.storelist.refresh()
			},
			async getGoodsList(type){
				this.params.lat=this.latLng.latitude
				this.params.lng=this.latLng.longitude
				let {data} =  await this.util.request({
					url: this.api.specialOffer,
					method: 'GET',
					data:this.params
				})
				if(type !=='nextPage'){
						this.goodsList = data
					}else{
						if(data.length === 0){
							this.status = 'nomore'
							return
						}
						if(this.params.page === 1){
							this.goodsList = data
						}else{
							this.goodsList = this.goodsList.concat(data)
						}
					}
				this.status='loadmore'
			},
			nextPage(){
				if(this.status === 'loading'){return}
				this.params.page++
				this.getGoodsList('nextPage')
			},
		}
		
	}
</script>

<style scoped lang="scss">
	.label{
		border-radius: 30rpx;
		padding: 10rpx 20rpx;
		background: #f5f6f9;
		margin-top: 20rpx;
	}
	.card {
		display: flex;
		width: 700rpx;
		background: #FFF;
		border-radius: 20rpx;
		margin: 0 auto;
		margin-bottom: 20rpx;
		padding: 20rpx;
	}
	
	.label-title {
		font-size: 22rpx;
		font-weight: bold;
		color: #fff;
		border-radius: 10rpx;
		margin-right: 10rpx;
		padding: 2rpx 8rpx;
		background: #ea5a3b;
	}
	
	.tag {
		margin-right: 10rpx;
		padding: 2rpx 4rpx;
		border-radius: 10rpx;
		color: #d28f50;
		background: #fdf7e1;
	}
	
	.label-coupon {
		height: 40rpx;
		line-height: 38rpx;
		color: #c57771;
		border: 1rpx solid #f0dbdb;
		border-radius: 8rpx;
		padding: 0rpx 8rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	
	.red {
		color: #ea5a3b;
		border-color: #f0dbdb;
	}
	
	.brown {
		color: #bba278;
		border-color: #d0c8b3;
	}
</style>
