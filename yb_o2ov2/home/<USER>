<template>
	<view>
		<!-- 头部 start -->
		<view class="p-f w100 " :style="{top:0,height:`${statusNavBarHeight}px`,paddingTop:`${menuButtonTop}px`,zIndex:3,background:'#fff'}">
			<!-- 返回 -->
			<view class="p-a" :style="{top:`${menuButtonTop}px`,left:`20rpx`,zIndex:4}" @click="go('back')">
				<text class="iconfont iconback f40"></text>
			</view>
			<view class="t-c f32 wei f-c">
				<view class="ggtit t-o-e t-c">{{result.title?result.title:'公告详情'}}</view>
			</view>
		</view>
		<!-- 头部 end -->
		<view class="p3 h100v bf" :style="{marginTop:`${statusNavBarHeight}px`}">
			<scroll-view scroll-y="true" :style="{height:`${wHeight-statusNavBarHeight-10-(60/pxToRpxRate)}px`}">
				<!-- <view v-if="result.title" class="wei mb20 f30">{{result.title}}</view> -->
				<view class="f-y-c c9 f24 mb20">
					<view v-if="result.createdAt" class="mr10">发布时间：{{getTime(result.createdAt)}}</view>
					<view v-if="result.views">阅览量：{{result.views}}</view>
				</view>
				<rich-text :text="result.body"></rich-text>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	import RichText from '@/components/RichText.vue'
	import utils from '@/common/utils.js'
	export default {
		components: {
			RichText
		},
		data() {
			return {
				result:{
					body:''
				}
			}
		},
		onLoad(option) {
			this.result = uni.getStorageSync('noticeDetail')
			uni.removeStorage('noticeDetail')
		},
		methods: {
			getTime(v) {
				return utils.settime(v)
			}
		},
	}
</script>

<style scoped lang="scss">
	.ggtit{
		width: 400rpx;
	}
</style>
