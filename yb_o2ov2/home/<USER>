<template>
	<scroll-view scroll-y class="bf h100v" @scrolltolower="onPullUpBottom">
		<view class="f-y-c p3 header">
			<view class="mr20" @click="back">
				<text class="iconfont iconback f40"></text>
			</view>
			<view class="mr20 f-g-1">
				<view class="bf t-l f-y-c f-g-1 p10" style="border-radius: 40rpx;">
					<text class="iconfont iconsearch f28 ml20 mr10 c9"></text>
					<input type="text" v-model="keyWord" @input="lenovo" :placeholder="$t('home.input_placeholder')" />
				</view>
			</view>
			<view @click="search">{{$t('home.search_text')}}</view>
		</view>
		<view class="p3" v-show="!showResult && !lenovoShow">
			<block v-if="keywords">
				<!-- 探索发现 -->
				<view class="f-x-bt">
					<view class="wei">{{$t('search.explore_and_discover')}}</view>
					<!-- <view class="c9 f24">换一批</view> -->
				</view>
				<view class="f-w f-y-c f24">
					<view v-for="(item,index) in keywords" :key="index" class="mr15 label" @click="search(item)">
						{{item}}
					</view>
				</view>
			</block>
			<!-- 历史搜索 -->
			<view class="f-x-bt mt30">
				<view class="wei">{{$t('search.historical_search')}}</view>
				<view class="c9 f24" @click="clearRecords">{{$t('common.delete')}}</view>
			</view>
			<view class="f-w f-y-c f24">
				<view v-for="(item,index) in searchRecords" :key="index" class="mr15 label" @click="search(item)">
					{{item}}
				</view>
			</view>
		</view>
		<!-- 搜商店 -->
		<view v-show="showResult">
			<view v-if="list.length">
				<shopList v-for="item in list" :key="item.id" :item="item"></shopList>
				<view class="no-data">--{{$t('common.no_data')}}--</view>
			</view>
			<mescroll-empty v-else imageWH="280" :option="{icon:'/static/empty/2.png', tip:$t('location.localText')}">
			</mescroll-empty>
		</view>
		<!-- 词库联想 -->
		<scroll-view v-if="lenovoShow" scroll-y :style="{height:`${scrollHeight}px`}" class="p02">
			<view v-for="(item,index) in lenovoArr" :key="index" class="p15 wei"
				style="border-bottom: 1px solid #f5f6f9;" @click="search(item)">
				<text class="iconfont iconsearch f28 ml20 mr10 c9"></text>
				<text>{{item}}</text>
			</view>
		</scroll-view>
	</scroll-view>
</template>

<script>
	import {
		mapState,
	} from 'vuex'
	import utils from '@/common/utils.js'
	import shopList from '@/yb_o2ov2/index/component/shopList.vue'
	export default {
		components: {
			shopList
		},
		data() {
			return {
				lenovoArr: [],
				lenovoShow: false, //联想list
				keywords: [],
				keyWord: '',
				showResult: false, //店家list
				searchRecords: [],
				list: [],
				pageData: {
					page: 1,
					size: 15
				},
				flag: true
			}
		},
		computed: {
			...mapState({
				layout: state => state.layout.index.body
			}),
			scrollHeight() {
				return this.wHeight - 104 - this.statusNavBarHeight
			}
		},
		onLoad(option) {
			this.placeholderText = option.tip || '请输入关键词'
			//搜索历史
			this.searchRecords = uni.getStorageSync('searchRecords') || []
			if (this.searchRecords.length) {
				this.searchRecords = this.searchRecords.slice(0, 10)
			}
			//获取关键词
			try {
				let styles = this.layout.list.find(i => i.title === '地址定位').styles
				this.keywords = (styles.fixedSearch.keyword).split(',') || false
				this.placeholderText = styles.fixedSearch.text
			} catch (e) {
				this.keywords = false
				//TODO handle the exception
			}
			if (option.keyword) {
				this.search(option.keyword)
			}
		},
		methods: {
			lenovo: utils.debounce(async function() {
				if (this.keyWord === '') {
					this.showResult = false
					this.lenovoShow = false
					return
				}
				this.showResult = false
				this.lenovoShow = true
				this.list = []
				let {
					data
				} = await this.util.request({
					url: this.api.lenovo,
					method: 'GET',
					data: {
						keyword: this.keyWord
					}
				})
				this.lenovoArr = data
			}, 400),
			onPullUpBottom() {
				this.fetchData()
			},
			async clearRecords() {
				await this.util.modal(`${this.$t('search.del_seach_historical')}`)
				this.searchRecords = []
				uni.setStorageSync('searchRecords', this.searchRecords)
			},
			addRecords() {
				if (!this.keyWord) return
				let index = this.searchRecords.findIndex(i => i === this.keyWord)
				// 以前没搜过 记录在第一个
				if (index === -1) {
					this.searchRecords.unshift(this.keyWord)
				} else { //以前搜过 去掉以前的记录 新增到第一个
					this.searchRecords.splice(index, 1)
					this.searchRecords.unshift(this.keyWord)
				}
				if (this.searchRecords.length) {
					this.searchRecords = this.searchRecords.slice(0, 10)
				}
				uni.setStorageSync('searchRecords', this.searchRecords)

			},
			search(item) {
				if (Object.prototype.toString.call(item) === '[object String]') {
					this.keyWord = item
				}
				this.lenovoShow = false
				// 搜索记录在本地
				this.addRecords()
				this.showResult = true
				this.flag = true
				this.pageData.page = 1
				this.list = []
				this.fetchData()
			},
			back() {
				if (this.showResult) {
					this.searchRecords = uni.getStorageSync('searchRecords')
					this.keyWord = ''
					this.showResult = false
					return
				}
				this.go('back', '/yb_o2ov2/index/index')
			},
			async fetchData() {
				if (!this.flag) return
				this.flag = false
				const {
					data
				} = await this.util.request({
					url: this.api.storeList,
					data: {
						page: this.pageData.page,
						size: this.pageData.size,
						lat: this.latLng.latitude,
						lng: this.latLng.longitude,
						keyword: this.keyWord
					}
				})
				this.list = this.list.concat(data.list)
				if (this.list.length < data.count) {
					this.flag = true
					this.pageData.page++
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	.header {
		background-color: #FFCC00;
	}

	.label {
		border-radius: 30rpx;
		padding: 10rpx 20rpx;
		background: #f5f6f9;
		margin-top: 20rpx;
	}
</style>