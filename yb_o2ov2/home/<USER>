<template>
	<view>
		<view v-if="storeSet.storeModel!='1'" class="p-f" :style="{top:`${menuButtonTop}px`,left:`20rpx`,zIndex:4,marginTop:'6rpx'}" @click="go('back')">
				<text class="iconfont iconback f30 cf" :style="{color:opacity>=1?'#000':'#fff'}">返回</text>
			</view>
	
	<view class="mh100 flex"  :style="{background:tColor, height: height + 'rpx'}">
		<view class="gyhead posi-r">
			<view class="wh" ></view>
			<view class="posi-a t270 wh">
				<view class="m03">
					<view class="bf mb20 bs20 p3 minhe" >
						<view class="iconwidth bs10">
							<mg-img :src="shopData.icon"></mg-img>
						</view>
						
						<view class="ml175">
							<view class=" c0 f32 mt40  wei ">{{shopData.name}}</view>
							<view class="f28 c6 mt10 ">
								<text class="iconfont iconstar f24" style="color:#ff5454"></text>
								<text class="wei mr10" style="color: #ff5454;">{{shopData.score}}</text>
							</view>
						</view>

						<view class="f-raw  p-a icon-list" style="padding: 10rpx;">
							<view class="mr20 iconleft" @click="collectionStore()">
								<text class="iconfont  f36 wei" :class="isCollection === 1?'icontcollectionfill':'icontcollection'"
							 :style="{color:isCollection === 1?tColor:opacity>=1?'#e8e5e9':'#333'}"></text>
							 <view :style="{color:isCollection === 1?tColor:opacity>=1?'#e8e5e9':'#333'}">{{shopData.collection>999?'999+':shopData.collection}}</view>
							</view>
                             <view style="width: 1px;height: 65rpx;background-color: #e8e5e9;">
                             	
                             </view>
							<view class="ml20 iconright" @click="zanStore()">
								<text class="iconfont iconlike f36 wei" :style="{color:isZan === 1?tColor:opacity>=1?'#e8e5e9':'#333'}"></text>
								<view :style="{color:isZan === 1?tColor:opacity>=1?'#e8e5e9':'#333'}">{{shopData.zan>999?'999+':shopData.zan}}</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="posi-a t300 w100v">
				<view class="bf mb20 bs2000 p3 info" :style="{height: bottom + 'rpx'}">
					<video class="video" :src="info.video"
						style="width: 100%;height: 200rpx;" objectFit="fill"></video>
					<view>
						<image src="../../static/img/sjzl.png" mode=""></image>
					</view>
					<view class="store-info">
						<view  @click="xiaosha()">
							<image src="../../static/img/sjtj.png" mode=""></image>
							<view style="font-size: 22rpx;font-weight: bold;color: #000000;">消杀记录</view>
							<view style="font-size: 18rpx;color: #999999;">可查看每日消杀情况</view>
							<view>查看</view>
						</view>
						<view  @click="guandao()">
							<image src="../../static/img/qinxi.png" mode=""></image>
							<view style="font-size: 22rpx;font-weight: bold;color: #000000;">管道清洗记录</view>
							<view style="font-size: 18rpx;color: #999999;">可查看每月消杀情况</view>
							<view style="background: linear-gradient(90deg, #FF934C 0%, #FC686F 100%);">查看</view>
						</view>
						<view  @click="zizhi()">
							<image src="../../static/img/zizhi.png" mode=""></image>
							<view>商店资质</view>
							<view>可查看营业执照</view>
							<view style="background: linear-gradient(90deg, #3AD788 0%, #29C677 100%);">查看</view>
						</view>
					</view>
					<view class="goStore" @click="go('navigateTo',`/yb_o2ov2/home/<USER>">
						进入商店
					</view>
				</view>
			</view>
		</view>
	</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapActions,
		mapMutations
	} from 'vuex'
	import mgImg from '@/components/common/mg-img.vue'
	import utils from '@/common/utils.js'
	export default {
		name: 'store-index',
		components: {mgImg},

		
		data() {
			return {
				loading: false,
				content: '',
				systemData: '',
				isCollection:2,
				isZan:2,
				info:{},
				params:{
					storeId:null
				},
				shopData:{}, //商户信息
			}
		},
		async onLoad(option) {
			this.params = {
				storeId:option.storeId,
				lat:this.latLng.latitude,
				lng:this.latLng.longitude
			}
			await this.init()
		},
		computed: {
			...mapState({
				height:state=>state.systemInfo.MainPageHeight+120,
				bottom:state=>state.systemInfo.MainPageHeight-260,
			}),
		},
		onReady() {
		 this.setSystemInfo();
		},
		methods: {
			...mapActions(["setSystemInfo"]),
			...mapMutations(["setShopGoodsInfo","setCanOrder","setStoreInfo"]),
			async init(){
				//获取商品列表 门店信息
				await this.getStoreInfo()
			},
			xiaosha(){
						uni.previewImage({
							urls: this.info.xiaosha,
							longPressActions: {
								itemList: ['发送给朋友', '保存图片', '收藏'],
								success: function(data) {
									console.log('选中了第' + (data.tapIndex + 1) + '个按钮,第' + (data.index + 1) + '张图片');
								},
								fail: function(err) {
									console.log(err.errMsg);
								}
							}
						});
			},
			guandao(){
						uni.previewImage({
							urls: this.info.guandao,
							longPressActions: {
								itemList: ['发送给朋友', '保存图片', '收藏'],
								success: function(data) {
									
								},
								fail: function(err) {
									
								}
							}
						});
			},
			zizhi(){
						uni.previewImage({
							urls: this.info.zizhi,
							longPressActions: {
								itemList: ['发送给朋友', '保存图片', '收藏'],
								success: function(data) {
									
								},
								fail: function(err) {
									
								}
							}
						});
			},
			async getStoreInfo(){
				let {data} =  await this.util.request({
					url: this.api.getStoreInfo,
					method: 'GET',
					data: this.params
				})
				this.shopData = data.shopData
				this.isCollection = data.shopData.isCollection
				this.isZan = data.shopData.isZan
				this.moreSet = data.moreSet
				this.categorySet = data.categorySet
				this.discount = data.discount
				this.info=JSON.parse(data.shopData.index)
				this.setShopGoodsInfo(data)
				this.setStoreInfo(data.shopData)
			},
			collectionStore:utils.debounceImmediate(async function(){
				uni.showLoading({
					title: '載入中',
					mask:1
				});
				let {data} =  await this.util.request({
					url: this.api.scjk,
					method: 'GET',
					data: {
						collectionId: this.params.storeId,
						type:'1'
					}
				})
				uni.hideLoading()
				if(this.isCollection === 1){
					this.isCollection=2
					this.shopData.collection=parseInt(this.shopData.collection)-1
					this.util.message('取消收藏成功', 3)
				}else{
					this.isCollection=1
					this.shopData.collection=parseInt(this.shopData.collection)+1
					this.util.message('收藏成功', 3)
				}
			}),
			zanStore:utils.debounceImmediate(async function(){
				uni.showLoading({
					title: '載入中',
					mask:1
				});
				let {data} =  await this.util.request({
					url: this.api.zanStore,
					method: 'GET',
					data: {
						storeId: this.params.storeId,
						type:'1'
					}
				})
				uni.hideLoading()
				if(this.isZan === 1){
					this.isZan=2
					this.shopData.zan=parseInt(this.shopData.zan)-1
					this.util.message('取消讚成功', 3)
				}else{
					this.isZan=1
					this.shopData.zan=parseInt(this.shopData.zan)+1
					this.util.message('讚成功', 3)
				}
			}),
			isIntNum(val){
			    var regPos = / ^\d+$/; // 非负整数 
			    var regNeg = /^\-[1-9][0-9]*$/; // 负整数
			    if(regPos.test(val) && regNeg.test(val)){
			        return true;
			    }else{
			        return false;
			    } 
			}
		},
	}
</script>
<style scoped lang="scss">

	.gyhead {
		width: 100%;
		height: 300rpx;
	}

	.t270 {
		top: 100rpx;
	}

	.t300 {
		top: 400rpx;
	}
	.iconwidth {
		width: 120rpx;
		height: 120rpx;
		bottom: 22%;
		left: 18%;
		transform: translateX(-50%);
		position: absolute;
		//margin-top: -90rpx;
	}

	.minhe {
		min-height: 220rpx;
		margin-top: 60rpx;
	}
	.info {
		margin-top: 10rpx;
	}
	.iconright{
		   display: flex;
		   flex-direction: column;
		   align-items: center;
	}
	.iconleft{
		    display: flex;
			flex-direction: column;
			align-items: center;
	}

	.icon-list {
		right: 88rpx;
		bottom: 70rpx;
		border: 1rpx solid #e8e5e9;
		border-radius: 30rpx;
	}

	.video {
		width: 693rpx !important;
		height: 430rpx !important;
		background: #FFFFFF;
		border-radius: 20rpx;
	}

	.video+view {
		width: 100%;
		display: flex;
		justify-content: center;
		margin-top: 20rpx;
	}

	.video~view>image {
		width: 274rpx;
		height: 66rpx;
		margin: auto;
	}

	.store-info {
		width: 100%;
		display: flex;
		justify-content: space-between;
		margin-top: 8rpx;
	}

	.store-info>view {
		width: 220rpx;
		height: 211rpx;
		border: 1rpx solid #FFCD17;
		background: linear-gradient(-15deg, #FFFCEF 0%, #FFFFFF 100%);
		border-radius: 20rpx;
		padding: 19rpx 0rpx 19rpx 18rpx;
		display: flex;
		flex-direction: column;

		image {
			width: 50rpx;
			height: 50rpx;
		}

		view:nth-of-type(1) {
			font-size: 22rpx;
			font-weight: bold;
			color: #000000;
		}

		view:nth-of-type(2) {
			font-size: 18rpx;
			color: #999999;
		}

		view:nth-of-type(3) {
			background: linear-gradient(90deg, #015EEA 0%, #00C0FA 100%);
			border-radius: 20rpx;
			width: 90rpx;
			height: 40rpx;
			font-size: 20rpx;
			text-align: center;
			line-height: 40rpx;
			color: white;
			margin-top: 20rpx;
		}
	}

	.goStore {
		width: 693rpx;
		height: 100rpx;
		background: #FFCD17;
		border-radius: 50rpx;
		margin-top: 46rpx;
		text-align: center;
		line-height: 100rpx;
		font-weight: bold;
		color: #000000;
		position: fixed;
		bottom: 60rpx;
	}
	.ml175 {
		margin-left: 175rpx;
	}
</style>
