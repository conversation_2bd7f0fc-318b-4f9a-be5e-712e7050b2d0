<template>
	<view :style="{minHeight:viewHeight+'px'}">
		<view class="bf flex f22 wei mt20 p3">
			<view @click="go('navigateTo',`/yb_o2ov2/shop/reserve/index?storeId=${shopData.id}`)" class="bs10 p02 mr20"
				style="border: 1px solid #666;height: 60rpx;line-height: 60rpx;">餐桌预定</view>
			<view @click="go('navigateTo',`/yb_o2ov2/shop/lineup/pdqh?storeId=${shopData.id}`)" class="bs10 p02 mr20"
				style="border: 1px solid #666;height: 60rpx;line-height: 60rpx;">排队取号</view>
			<view @click="go('navigateTo',`/yb_o2ov2/order/jd?storeId=${shopData.id}`)" class="bs10 p02 mr20"
				style="border: 1px solid #666;height: 60rpx;line-height: 60rpx;">集点活动</view>
			<view @click="go('navigateTo',`/yb_o2ov2/shop/storage/index?storeId=${shopData.id}`)" class="bs10 p02 mr20"
				style="border: 1px solid #666;height: 60rpx;line-height: 60rpx;">酒水寄存</view>
		</view>
	</view>

</template>

<script>
	export default {
		props: {
			tabNavHeight: { //tabNav高度单位rpx
				type: [String, Number],
				default: 80
			},
			shopData:{
				type:Object,
				default:()=>{}
			},
			moreSet:{
				type:Object,
				default:()=>{}
			},
		},
		created() {
			console.log(123)
			this.payPlugin()
		},
		data() {
			return {
				title:{},
				plugin:[],
			}
		},
		computed: {
			viewHeight() {
				return this.wHeight - this.statusNavBarHeight - this.tabNavHeight / this.pxToRpxRate
			}
		},
		methods:{
			async payPlugin(){
				let {data} =  await this.util.request({
					url: this.api.payPlugin,
					method: 'GET',
					is_login: 0
				})
				this.plugin = data?data:[]
			},
			checkImg(index){
				uni.previewImage({
					urls:this.shopData.environment,
					current:index,
					indicator:true
				})
			},
			call(){
				uni.makePhoneCall({
				    phoneNumber: this.shopData.storeTel //仅为示例
				});
			},
			navigation(){
				let info = {
					lat:this.shopData.lat,
					lng:this.shopData.lng,
					name:this.shopData.name,
					address:this.shopData.address
				}
				this.util.ckWz(info)
			},
			navigation2(){
				this.go('navigateTo','/yb_o2ov2/home/<USER>' + encodeURIComponent(JSON.stringify(this.shopData)))
			},
		}
	}
</script>

<style>
</style>
