<template>
	<scroll-view class="page" scroll-y>
		<view class="navigation-bar">
			<text class="iconfont iconback f40 mr20" @click="go('back','/yb_o2ov2/index/index')"></text>
			<view class="search-box">
				<view class="search" @click="openMap">
					<text class="iconfont iconsearch f28 ml20 mr10 c9"></text>
					<input class="ipt" type="text" v-model="name" :placeholder="$t('location.search_placeholder')" />
				</view>
			</view>
		</view>
		<view class="content">
			<view class="f24 c9 bf t-l f-g-1">{{$t('location.current_location')}}</view>
			<view class="f-x-bt head-end bf">
				<view class="f-y-c">
					<text class="iconfont iconposition f30 mr10"></text>
					<text class="wei f30 t-o-e t-l" style="width: 480rpx;">{{locationInfo.maddress.replace('台湾省', '台湾')}}</text>
				</view>
				<view class="f-y-c f-g-1">
					<text class="iconfont icongps f28 ml20 mr10 c9"></text>
					<text @click="getLocation">{{$t('location.repositioning')}}</text>
				</view>
			</view>
			<view class="f-y-c mt20" v-if="myAddress.length">
				<text class="f24 c9">{{$t('location.my_shipping_address')}}</text>
			</view>
			<view v-if="myAddress.length!==0" class="p23">
				<view v-for="(item) in myAddress" :key="item.id" class="p20 f-c-xc"
					style="height: 140rpx;border-bottom: 1px solid #fafafa;" @click="changePosition(1,item)">
					<view class="t-o-e f-y-c">
						<text v-if="item.label" class="label"
							:style="{background:'#ecf7fd',color:'#5cb3e6'}">{{item.label}}</text>
						<text class="f30 wei">{{ item.address }}{{item.details}}</text>
					</view>
					<view class="c8 mt10 f24">
						<text class="mr10">{{item.userName}}</text>
						<text class="mr60">({{item.sex==0?$t('home.lady_text'):$t('home.sir_text')}})</text>
						<text>{{item.userTel}}</text>
					</view>
				</view>
			</view>
			<view v-if="myAddress.length>3" @click="expand = !expand" style="padding: 0 60rpx 20rpx;">
				{{$t('location.expand_more_addresses')}}
				<text class="iconfont iconback m01 f16"
					:style="expand?'transform: rotate(90deg);':'transform: rotate(-90deg);'"></text>
			</view>
			<view class="f-y-c mt20">
				<text class="f24 c9">{{$t('location.nearby_address')}}</text>
			</view>
			<view class="p03">
				<view v-for="item in nearbyArr" :key="item.id" class="p20" style="border-bottom: 1px solid #fafafa;"
					@click="changePosition(2,item)">
					<view class="f30">{{item.title}}</view>
				</view>
			</view>
			<view style="height: 120rpx;width: 1px;"></view>
		</view>
	</scroll-view>
</template>

<script>
	import {
		mapState,
		mapMutations
	} from 'vuex'
	import utils from '@/common/utils.js'
	export default {
		data() {
			return {
				scrollHeight: '',
				marginTop: '',
				expand: false,
				myAddress: [],
				nearbyArr: [],
				name: '',
				locationInfo: {
					maddress: '',
					latitude: 0,
					longitude: 0
				}
			}
		},
		async onLoad() {
			// 优先从本地存储加载位置信息
			const savedLocation = uni.getStorageSync('locationInfo');
			if (savedLocation) {
				this.locationInfo = JSON.parse(savedLocation);
				console.log('使用本地存储的位置信息:', this.locationInfo);
			} else {
				// 如果没有本地存储的位置信息，则获取新位置
				this.getLocation();
			}
			this.init()
		},
		onShow() {
			// 每次页面显示时检查本地存储的位置信息
			const savedLocation = uni.getStorageSync('locationInfo');
			if (savedLocation) {
				const parsedLocation = JSON.parse(savedLocation);
				// 如果位置信息有变化，更新数据
				if (JSON.stringify(this.locationInfo) !== JSON.stringify(parsedLocation)) {
					this.locationInfo = parsedLocation;
					console.log('位置信息已更新:', this.locationInfo);
					// 更新附近地址列表
					this.getNearby();
				}
			}
		},
		mounted() {
			this.getElement()
		},
		watch: {
			'locationInfo.maddress': {
				handler(val) {
					this.getNearby()
				}
			}
		},
		methods: {
			openMap() {
				// #ifdef  MP-WEIXIN
				uni.chooseLocation({
					success: (res) => {
						this.locationInfo = {
							...this.locationInfo,
							maddress: res.address,
							latitude: res.latitude,
							longitude: res.longitude
						};
						this.name = res.name;
						
						// 保存到本地存储
						uni.setStorageSync('locationInfo', JSON.stringify(this.locationInfo));
						this.getNearby();
					}
				})
				// #endif

				// #ifdef H5
				this.go('navigateTo', '/yb_o2ov2/home/<USER>/map?type=position')
				// #endif
			},
			async init() {
				this.fetchData()
				this.getNearby()
			},
			async fetchData() {
				let {
					data
				} = await this.util.request({
					url: this.api.getMyAddress,
					method: 'GET'
				})
				console.log(data,'data');
				if(data.code == 99 ){
					this.myAddress = []
					return
				}
				this.myAddress = data
			},
			async getNearby() {
				let {
					data
				} = await this.util.request({
					url: this.api.getNearby,
					method: 'GET',
					data: {
						lat: this.locationInfo.latitude,
						lng: this.locationInfo.longitude,
						pagesize: 10,
						page_index: 1
					},
					is_login: 0
				})
				this.nearbyArr = data.data
			},
			changePosition(type, item) {
				if (type === 1) {
					// 帳戶地址列表选择
					this.locationInfo = {
						...this.locationInfo,
						latitude: item.lat,
						longitude: item.lng,
						maddress: item.address
					};
				} else {
					// 附近地址列表选择
					this.locationInfo = {
						...this.locationInfo,
						latitude: item.location.lat,
						longitude: item.location.lng,
						maddress: item.title
					};
				}
				
				// 保存到本地存储
				try {
					uni.setStorageSync('locationInfo', JSON.stringify(this.locationInfo));
					console.log('位置信息已更新到本地存储:', this.locationInfo);
					// 提示用户位置已更新
					this.util.message('位置已更新');
				} catch (e) {
					console.error('保存位置信息失败:', e);
					this.util.message('保存位置信息失败');
				}
			},
			getElement() {
				let query = uni.createSelectorQuery().in(this);
				query.select('.head-end').boundingClientRect((res) => {
					this.marginTop = res.bottom + 10
					this.scrollHeight = Math.floor(this.wHeight - res.bottom - this.statusNavBarHeight)
				}).exec()
			},
			// 获取位置信息
			getLocation() {
				// #ifdef H5
				if (navigator.geolocation) {
					navigator.geolocation.getCurrentPosition(
						(position) => {
							const { latitude, longitude } = position.coords;
							this.locationInfo = {
								...this.locationInfo,
								latitude,
								longitude
							};
							this.getAddressFromCoordinates(latitude, longitude);
						},
						(error) => {
							console.error('获取位置失败:', error);
							this.util.message(this.$t('address.address_error'), 3);
						},
						{
							enableHighAccuracy: true,
							timeout: 5000,
							maximumAge: 0
						}
					);
				} else {
					this.util.message('您的浏览器不支持地理定位');
				}
				// #endif

				// #ifdef APP-PLUS
				uni.getLocation({
					type: 'gcj02',
					success: (res) => {
						this.locationInfo = {
							...this.locationInfo,
							latitude: res.latitude,
							longitude: res.longitude
						};
						this.getAddressFromCoordinates(res.latitude, res.longitude);
					},
					fail: (err) => {
						console.error('获取位置失败:', err);
						this.util.message(this.$t('address.address_error'), 3);
					}
				});
				// #endif
			},
			// 根据经纬度获取详细地址
			async getAddressFromCoordinates(latitude, longitude) {
				try {
					const language = sessionStorage.getItem('language') || 'zh-TW';
					const response = await fetch(
						`https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=AIzaSyBTUfOCvsKnvX2H9xr_bbYsho1PzJWQV6c&language=zh-${language}`
					);
					const data = await response.json();
					
					if (data.status === 'OK' && data.results.length > 0) {
						let address = data.results[0].formatted_address;
						// 移除郵遞區號
						address = address.replace(/\s*郵遞區號:\s*\d+/g, '').trim();
						// 替换台湾省为台湾
						address = address.replace('台湾省', '台湾');
						
						this.locationInfo = {
							...this.locationInfo,
							maddress: address
						};
						
						// 保存到本地存储
						uni.setStorageSync('locationInfo', JSON.stringify(this.locationInfo));
					} else {
						console.warn('未找到地址信息:', data.status);
					}
				} catch (error) {
					console.error('获取地址信息失败:', error);
					this.util.message('获取地址信息失败');
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	.page {
		display: flex;
		width: 100vw;
		height: 100vh;
		flex-direction: column;
		background-color: #fff;

		.navigation-bar {
			display: flex;
			width: 100%;
			height: 126rpx;
			justify-content: flex-start;
			align-items: center;
			padding: 30rpx;
			box-sizing: border-box;
			background-color: #FFCC00;

			.search-box {
				flex: 1;
				display: flex;
				height: 100%;
				align-items: center;
				background-color: #fff;
				margin-right: 20rpx;
				border-radius: 32rpx;
				padding: 0 20rpx;
				box-sizing: border-box;

				.search {
					flex: 1;
					display: flex;
					height: 100%;
					align-items: center;

					.ipt {
						flex: 1;
						height: 100%;
						font-size: 28rpx;
					}
				}
			}
		}

		.content {
			width: 100%;
			padding: 0 30rpx;
			box-sizing: border-box;
			margin-top: 20rpx;
		}
	}

	.label {
		width: 60rpx;
		height: 30rpx;
		border-radius: 5rpx;
		margin-right: 10rpx;
		font-size: 20rpx;
		font-weight: bold;
		line-height: 30rpx;
		text-align: center;
	}
</style>