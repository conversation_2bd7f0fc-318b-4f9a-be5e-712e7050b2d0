<template>
	<scroll-view class="page" scroll-y @scrolltolower="onPullUpBottom">
		<view class="page-navigation-bar">
			<view class="go-icon">
				<text class="iconfont iconback f40 " @click="go('back','/yb_o2ov2/index/index')"></text>
			</view>
			<text>{{categoryName}}</text>
		</view>

		<view class="content">
			<view v-if="list.length">
				<view class="item" v-for="item in list" :key="item.id">
					<shop-list :item="item"></shop-list>
				</view>
				<view class="no-data">--{{$t('common.no_data')}}--</view>
			</view>
			<mescroll-empty v-else imageWH="280" :option="{icon:'/static/empty/2.png', tip:$t('location.localText')}">
			</mescroll-empty>
		</view>
	</scroll-view>
</template>

<script>
	import shopList from "@/yb_o2ov2/index/component/shopList.vue"
	import crossIndustryStore from "@/yb_o2ov2/index/component/crossIndustryStore.vue"
	export default {
		components: {
			shopList,
			crossIndustryStore
		},
		data() {
			return {
				categoryName: '',
				shopType: 0,
				flag: true,
				pageData: {
					page: 1,
					size: 15
				},
				list: [],
				catId: 0
			}
		},
		methods: {
			onPullUpBottom() {
				this.getList()
			},
			async getList() {
				if (!this.flag) return
				this.flag = false
				let params = {}
				console.log('aa', JSON.parse(uni.getStorageSync('locationInfo')));
				
				if (this.shopType) {
					params = {
						page: this.pageData.page,
						size: this.pageData.size,
						lat: this.latLng.latitude || JSON.parse(uni.getStorageSync('locationInfo')).latitude,
						lng: this.latLng.longitude || JSON.parse(uni.getStorageSync('locationInfo')).longitude,
						storeType: 1
					}
				} else {
					params = {
						page: this.pageData.page,
						size: this.pageData.size,
						lat: this.latLng.latitude || JSON.parse(uni.getStorageSync('locationInfo')).latitude,
						lng: this.latLng.longitude || JSON.parse(uni.getStorageSync('locationInfo')).longitude,
						catId: this.catId
					}
				}
				const {
					data
				} = await this.util.request({
					url: this.api.storeList,
					data: params
				})

				this.list = this.list.concat(data.list)
				if (data && data.count > this.list.length) {
					this.pageData.page++
					this.flag = true
				}
			}
		},
		onLoad(option) {
			const options = JSON.parse(decodeURIComponent(option.item))
			this.categoryName = options.name
			this.shopType = options.type
			this.catId = options.id,
				this.getList()
		}
	}
</script>

<style lang="scss" scoped>
	.content {
		width: 100%;
		padding: 20rpx 30rpx;
		box-sizing: border-box;

		.item {
			margin-top: 20rpx;

			&:first-child {
				margin-top: 0;
			}
		}
	}
</style>