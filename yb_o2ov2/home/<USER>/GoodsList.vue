<!-- 商品列表 带分类 -->
<template>
	<view>
		<!-- 商品list -->
		<view class="pt20 bf p02 p-r" style="border-top: 1px solid #F5f6f9;z-index: 2;">
			<!-- 幻灯片 -->
			<block v-if="swiperList.length !== 0">
				<u-swiper height="200" bg-color="#ffffff" :list="swiperList"></u-swiper>
			</block>
		</view>
		<!-- 商品组 -->
		<view class="p-r flex listScroll">
			<view class="f-row">
				<!-- leftList 分类 -->
				<scroll-view :scroll-y="true" :scroll-top="scrollCategoryTop" class="f-col" style="width: 170rpx;">
					<view v-for="(item,index) in tempCategoryList" :key="item.id"
						:style="{minHeight:categoryHeight*pxToRpxRate + 'rpx'}" class="bf">
						<view class="category c9 f24 p-r f-c"
							:class="[active === index?'active':'inactive',{'bs-rb':active === (index+1)},{'bs-rt':active === (index-1)}]"
							@tap.stop="selectCategory(index)">
							<view v-if="categorySet.display!=1 && categorySet.display==2" class="f-c-c">
								<view v-if="item.icon" style="width: 30rpx;height: 30rpx;">
									<image class="wh" lazy-load :src="item.icon" mode=""></image>
								</view>
								<view class="mt10">{{item.name}}</view>
							</view>
							<view v-if="categorySet.display!=1 && categorySet.display==3" class="f-c">
								<view v-if="item.icon" style="width: 30rpx;height: 30rpx;">
									<image class="wh" lazy-load :src="item.icon" mode=""></image>
								</view>
								<view class="ml10">{{item.name}}</view>
							</view>
							<text v-if="categorySet.display!=2 && categorySet.display!=3">{{item.name}}</text>
							<!-- 分类标签（必选...） -->
							<view v-if="item.isRequire !=='2'" class="p-a category-label">
								<text>{{item.isRequire==='1'?$t('good.required_category'):item.customName}}</text>
							</view>
						</view>
					</view>
					<!-- 占位元素 -->
					<view :style="{height:categoryHeight*pxToRpxRate + 'rpx'}" class="bd"></view>
					<view :style="{height:categoryHeight*pxToRpxRate + 'rpx'}" class="bd"></view>
					<view :style="{height:categoryHeight*pxToRpxRate + 'rpx'}" class="bd"></view>
					<view :style="{height:categoryHeight*pxToRpxRate + 'rpx'}" class="bd"></view>
					<view :style="{height:categoryHeight*pxToRpxRate + 'rpx'}" class="bd"></view>
				</scroll-view>
			</view>
			<!-- rightList 商品-->
			<view class="f-col rightList">
				<view v-for="(item,categoryIndex) in tempCategoryList" :key="item.id">
					<view class="categoryTitle">{{item.name}}</view>
					<view v-for="(good,goodIndex) in item.goods" :key="good.id" style="min-height: 320rpx;"
						class="goods-box">
						<view class="flex pt20" @click="goToGoodsDetail(good,categoryIndex,goodIndex)">
							<!-- 商品左边图片 -->
							<view class="p-r mr20">
								<view class="bs10" style="width: 200rpx;height: 180rpx;">
									<image :src="good.icon" lazy-load class="wh" mode="aspectFill"></image>
								</view>
								<!-- 招牌 -->
								<view v-if="good.hotsaleType === '1'" class="p-a bs10 f20 wei"
									style="top: -10rpx;right: -10rpx;padding: 0 6rpx;">{{$t('good.sign')}}
								</view>
							</view>
							<!-- 商品右边介绍 -->
							<view class="f-g-1">
								<view style="min-height: 150rpx;">
									<view class="wei f30">{{good.name}}</view>
									<!-- 商品标签 -->
									<view class="f-y-c f-w">
										<!-- hd -->
										<view v-if="good.activityGoodData.type>0" class="mt5 f-row f22"
											:style="{color:tColor}">
											<view v-if="good.activityGoodData.type<4" class="f-row bs5 sptjc"
												:style="{background:ztqs,color:tColor,}">
												<text class="iconfont iconspzk f22" :style="{color:tColor}"></text>
												<view class="">
													{{good.activityGoodData.type==1?'特价':good.activityGoodData.type==2?Number(good.activityGoodData.discount)+'折':'商品立减'+sl+good.activityGoodData.discount}}
												</view>
												<view class="ml10 sphdir" v-if="good.activityGoodData.limitNum>0"
													:style="{borderColor:tColor}">{{$t('good.limit_purchase', { num: good.activityGoodData.limitNum })}}</view>
											</view>
										</view>
										<view v-if="good.labelName||good.activityGoodData.type>3" class="mt8">
											<text v-if="good.labelName" class='text-btnf f22 mr10 p-3-10 bs5'
												:style="{background:`rgba(${cTR(good.labelColor)},0.1)`,color:good.labelColor,}">{{good.labelName}}</text>
											<text v-if="good.activityGoodData.type>3"
												class='text-btnf f22 mr10 p-3-10 bs5'
												:style="{background:ztqs,color:tColor,}">{{good.activityGoodData.type==4?`第二件${Number(good.activityGoodData.discount)}折`:'买一送一'}}</text>
										</view>
										<text class="goods-label" v-for="tip in tips(good)" :key="tip">{{tip}}</text>
									</view>
									<!-- 簡介 -->
									<view v-if="good.body&&good.isBody==='1'" class="f24 c9" style="width: 320rpx;">
										{{good.body.length>25?good.body.substring(0,25)+'...':good.body}}
									</view>
									<view class="f24 c9">
										<text v-if="system.switch.saleShow==1&&good.salesNum>0">{{$t('good.monthly_sales')}} {{good.salesNum||0}}</text>
										<text v-if="system.switch.stockShow==1">{{$t('good.stock')}} {{good.stock}}</text>
									</view>
									<!-- 特殊要求 -->
									<view class="discount" v-if="good.maxNum && good.maxNum>0">
										<text>{{$t('good.limit_purchase', { num: good.maxNum })}}</text>
									</view>
									<!-- 单点不送 -->
									<view class="single" v-if="good.aloneType === '1'">
										<text class="text">{{$t('good.single_purchase')}}</text>
									</view>
									<view v-if="user.isVip && Number(good.vipPrice)" class="mt8 f-row">
										<view class="vipr f-y-c">
											<view class="vipimg">
												<mg-img :src='`${onImgurl}vipimg.png`'></mg-img>
											</view>
										</view>
										<view class="vipl f-y-c">{{sl+good.vipPrice}}</view>
									</view>
								</view>
								<view class="f-y-c f-bt">
									<view class="t-o-e f-g-1">
										<text class="f20 wei" :style="{color:categorySet.priceColor}">$</text>
										<block v-if="good.activityGoodData.type<1||good.activityGoodData.type>=4">
											<text class="f36 wei" style="margin-right: 5rpx;"
												:style="{color:categorySet.priceColor}" v-if="good.price && good.price>0">{{good.price}}</text>
										</block>
										<block v-else>
											<text class="f36 wei" style="margin-right: 5rpx;"
												:style="{color:categorySet.priceColor}">{{good.activityGoodData.activityMoney}}</text>
											<text class="t-d-l nowei ml10 c9 f26" v-if="good.price && good.price>0">{{good.price}}</text>
										</block>
										<text v-if="good.crossedPrice && good.crossedPrice>0"
											class="f22 c9 t-d-l">${{good.crossedPrice}}</text>
										<text v-if="system.switch.unitShow==1" class="ml10 f22 c9">/{{good.unit}}</text>
									</view>
									<!-- 购物车 -->
									<view v-if="bState===1||bState===3" class="f-e-c f-g-0" style="margin-left: auto;"
										@click.stop="">
										<view v-if="good.isSpecs ==='1'||good.isAttr ==='1'||good.isMaterial ==='1'"
											class="select-spec p-r"
											:style="{background:categorySet.delColor,color:categorySet.addColor}">
											<view @click.stop="getSpecs(good.id,good.name,good.price)">{{$t('good.select_specs')}}</view>
											<view v-if="good.carNum!==0" class="spec-dot">{{good.carNum}}</view>
										</view>
										<view v-else class="f-e-bt" style="width: 150rpx;">
											<block v-if="good.carNum>0">
												<view class="f-y-e pb10" style="height: 60rpx;"
													@click.stop="removeCart(good.id,categoryIndex,goodIndex)">
													<view class="f-c icon-bg"
														:class="categorySet.gwcstyle==='1'?'bs10':'bsf'"
														:style="{border: `1px solid ${categorySet.delColor}`}">
														<!-- 减号 -->
														<text class="iconfont iconminus f20 bs10 wei"
															style="padding: 4rpx 0;"
															:style="{color:categorySet.addColor2}"></text>
													</view>
												</view>
												<!-- 商品数量 -->
												<view class="f36" style="padding-bottom: 5rpx;">{{good.carNum}}</view>
											</block>
											<view v-else style="width: 20rpx;height: 100%;"></view>
											<view class="f-y-e pb10" style="height: 60rpx;"
												@click.stop="addCart($event,good.id,categoryIndex,goodIndex)"
												v-if="good.stock>0">
												<!-- 起购 -->
												<view v-if="good.minNum !=='1'&&good.carNum===0" class="bs10 f24 wei"
													style="padding: 2rpx 12rpx;"
													:style="{background:categorySet.delColor,color:categorySet.addColor}">
													<text>{{good.minNum}}{{$t('good.min_purchase')}}</text>
												</view>
												<!-- 加号 -->
												<view v-else class=" f-c icon-bg"
													:class="categorySet.gwcstyle==='1'?'bs10':'bsf'"
													:style="{background: categorySet.delColor}">
													<text class="iconfont iconplus f20 bs10 wei"
														style="padding: 4rpx 0;"
														:style="{color:categorySet.addColor}"></text>
												</view>
											</view>
											<view class="c9 f24" v-else>{{$t('good.sold_out_text')}}</view>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
				<view style="width: 100%;height: 70vh;"></view>
			</view>
		</view>
		<!-- 闭店 休息中 -->
		<view v-if="bState===0||bState===2" class="t-c f30"
			style="z-index:3;position: fixed;bottom: 0;width: 100%;height: 160rpx;line-height: 100rpx;background: rgba(0,0,0,.9);">
			<block v-if="shopData.businessState.state===2">
				<view class="cf">
					<text class="wei">本店已休息</text>
					<text>（{{shopData.businessState.msg}}）</text>
				</view>
			</block>
			<block v-else-if="shopData.businessState.state===0">
				<view class="cf"><text class="wei">非營業時間</text></view>
			</block>
		</view>
		<!-- 购物车 -->
		<view v-else style="z-index:3;position: fixed;bottom: 0;width: 100%;transition:.2s ease;">
			<ShoppingCart :cartInfo="cartInfo" :together="together" :togetherUser="togetherUser" :storeId="storeId" :tableId="tableId"
				:selfMoney="selfMoney" @getAniEndDot="handleGetDotEnd" background="#ffffff" ref="ShoppingCart"
				@removeCart="removeCart" @addCart="addCart" @refresh="getCartList">
			</ShoppingCart>
		</view>
		<!-- 選擇 -->
		<SelectSpecs :show.sync="specsShow" :cartList="cartList" :goodsId="specsGoodsId" :specsData="specsData"
			:goodsInfoSpecs="goodsInfoSpecs" @getXY="handleGetDotStart" @startAni="handleStartAni" :tableId="tableId"
			@removeCart="removeCart" @addCart="addCart"></SelectSpecs>
		<!-- 购物车效果 -->
		<AnimationAdd ref="aniAdd" :start="startXY" :end="endXY" :scroll="scroll"></AnimationAdd>
	</view>
</template>

<script>
	import {
		mapState,
		mapMutations
	} from "vuex";
	import utils from '@/common/utils.js'
	import ShoppingCart from './ShoppingCart.vue'
	import SelectSpecs from './SelectSpecs.vue'
	import AnimationAdd from './AnimationAdd.vue'
	import mgImg from '@/components/common/mg-img.vue'
	import {
		utilMixins
	} from "@/common/util-mixins.js"
	export default {
		components: {
			SelectSpecs,
			ShoppingCart,
			AnimationAdd,
			mgImg,
		},
		props: {
			storeId: {
				type: [String, Number],
				default: ''
			},
			tableId: {
				type: [String, Number],
				default: ''
			},
			scroll: {
				type: [String, Number],
				default: 0
			},
			callbackScroll: {
				type: [String, Number],
				default: 0
			},
			tabNavHeight: { //tabNav高度单位rpx
				type: [String, Number],
				default: 80
			},
			together: {
				type: [String, Number],
				default: 0
			}
		},
		data() {
			return {
				cartList: [],
				cartInfo: {},
				goodsInfoSpecs: {},
				specsData: {},
				specsGoodsId: '',
				active: 0, //当前激活分类的索引
				specsShow: false,
				scrollCategoryTop: 0, //左侧分类scroll的滚动位置
				rightScrollTop: 0, //右侧商品scroll的滚动位置
				categoryHeight: 65, //左侧分类一个元素的高度
				categoryTitleDotArr: [], //右侧每个categoryTitle的top位置信息arr
				startXY: {},
				endXY: {},
				tempCategoryList: [], //获取商品信息时候在当前组件备份数据 为了加减商品时反馈更快 本地做加减
				min: 0,
				max: 90,
				cacheKey: {}, //标记已经加载过的数据
				togetherUser: [],
				selfMoney: 0,
			}
		},
		mixins: [utilMixins],
		computed: {

			...mapState({
				categorySet: state => state.shopGoodsInfo.categorySet,
				moreSet: state => state.shopGoodsInfo.moreSet,
				shopData: state => state.shopGoodsInfo.shopData,
				bState: state => state.shopGoodsInfo.shopData.businessState.state,
				user: state => state.user,
			}),
			swiperList() {
				try {
					if (!this.moreSet) return []
					return this.moreSet.orderMedia.map(item => {
						return {
							image: item
						}
					})
				} catch (e) {
					return []
				}
			},
			scrollHeight() {
				return (this.wHeight - this.statusNavBarHeight) * this.pxToRpxRate - this.tabNavHeight + 4 || 0
			},
			leftScrollStyle() {
				return `position:sticky;top:${this.scrollStickyTop}px;height:${this.scrollHeight}rpx;z-index: 1;width:170rpx;`
			},
			rightCategoryTitleStyle() {
				return `position:sticky;top:${this.scrollStickyTop}px;`
			},
			scrollStickyTop() {
				//部分机型rpx转化px丢失精度过大 所以-1px
				return Math.floor(this.statusNavBarHeight + this.tabNavHeight / this.pxToRpxRate - 1)
			},
			tips() {
				return good => {
					return (good.showLabel && good.showLabel.split(',')) || ''
				}
			},
			ztqs() {
				return `rgba(${this.cTR(this.tColor)},0.1)`
			},
		},
		watch: {
			scroll(val) {
				this.gotoScroll(val)
			},
		},
		mounted() {
			this.getCartList()
		},
		methods: {
			goTopay() {
				this.$refs.ShoppingCart.otherGoTopay()
			},
			showDetails() {
				this.$refs.ShoppingCart.showDetails()
			},
			fetchGoods() {
				//增减商品
				const {
					id,
					cIndex,
					gIndex
				} = this.cacheGoods
				let goodsList = this.cartList.filter(g => g.goodsId === id)
				let num = 0
				goodsList.forEach(item => {
					num += (+item.num)
				})
				this.tempCategoryList[cIndex].goods[gIndex].carNum = num
				this.$set(this.tempCategoryList[cIndex].goods, gIndex, this.tempCategoryList[cIndex].goods[gIndex])
			},
			backupCategory(val) {
				//当前页面缓存商品数据 用来做本地的加减商品
				this.tempCategoryList = utils.deepCopy(val)
				this.pageSize = 30
				this.$nextTick(() => {
					this.scrollTop = -1
					this.getGoodsBoxDots()
					this.getCategoryDots()
				})
			},
			goToGoodsDetail(good, cIndex, gIndex) {
				// 储存进入詳情的商品信息 在页面返回时对商品进行修改
				this.cacheGoods = {
					id: good.id,
					cIndex,
					gIndex
				}
				if (this.shopData.businessState.state !== 1) return
				this.go('navigateTo', `/yb_o2ov2/home/<USER>
			},
			refresh() {
				this.$parent.refresh()
			},
			handleCancle() {
				this.$parent.handleCancle()
			},
			async getCartList() {
				console.log('获取购物车列表', this.storeId)
				let {
					data
				} = await this.util.request({
					url: this.api.getCartList,
					method: 'GET',
					data: {
						storeId: this.storeId,
						tableId: this.tableId,
						// item: '1', //1外卖 2店内 3快餐
						// together: this.together || 0
					}
				})
				this.cartInfo = data
				this.cartList = data.data
				this.setTogether(data)
				//this.together = data.together
				if (this.cartList.length === 0) {
					this.tempCategoryList.forEach(category => {
						category.goods.forEach(goods => {
							goods.carNum = 0
						})
					})
				}
				if (this.cacheGoods && this.cacheGoods.id) { //从商品詳情页 回来需要更新商品数量
					this.fetchGoods()
				}
			},
			setTogether(data) {
				if (this.together > data.together) {
					this.togetherUser = []
					this.selfMoney = 0
					this.$emit('update:showTogether', false) //修改页面拼单显示状态
					this.$emit('update:togetherUser', this.togetherUser)
					this.$emit('update:together', 0)
					this.$emit('cancelTimer')
				}
				if (data.together == 0) {
					this.$emit('cancelTimer')
				}
				if (data.together) {
					this.togetherUser = data.togetherUser
					this.selfMoney = data.selfMoney
					this.$emit('update:showTogether', true) //修改页面拼单显示状态
					this.$emit('update:togetherUser', this.togetherUser)
					this.$emit('update:together', data.together)
				}
				if (data.togetherStatus == 2) {
					this.$emit('cancelTimer')
					if (data.is_commander == 1) {
						setTimeout(() => {
							this.goTopay()
						}, 1500)
						return
					}
					this.util.modal('好友已提交了订单，您已无法继续选购商品', '提示').then(res => {
						this.goTopay()
					})
					return
				}
				if (data.togetherStatus == 1 && data.together) {
					this.$emit('bigenTimer')
				}
			},
			// 封装一个购物车点击 供子组件selectSpecs和shoppingcart使用
			/**
			 * 对购物车进行操作.
			 * @constructor
			 * @param {string} goodsId - 商品ID.
			 * @param {string} type - 操作类型 1加 2减.
			 * @param {string} id - 购物车ID 对在购物车内的商品增购的时候需要.
			 */
			async modifyCart(goodsId, type, id, spec) {
				let {
					count,
					data
				} = await this.util.request({
					url: this.api.saveShopCar,
					method: 'POST',
					contentType: 'application/json',
					data: {
						attribute: spec ? spec.attribute || null : null,
						groupId: spec ? spec.groupId || null : null,
						material: spec ? spec.material || null : null,
						storeId: this.storeId,
						tableId: this.tableId,
						goodsId: goodsId,
						type: type, // 1加 2减
						id: id || null, //购物车ID
						item: '1', //1外卖 2店内 3快餐
						together: this.together || 0
					}
				})
				if(data.code == 99 ){
					this.util.message(data.msg || data, 3, 2000)
					const pages = getCurrentPages();
					const currentPage = pages[pages.length - 1];
					const route = currentPage.route;
					const fullPath = route + '?' + Object.entries(currentPage.options || {}).map(([key, val]) => `${key}=${val}`).join('&');
					uni.setStorageSync('login_redirect', fullPath);
					uni.navigateTo({
						url: `/yb_o2ov2/my/login`
					})
				}
				
				if (data) {
					if (data.status == 0) {
						uni.showModal({
							title: '提示',
							content: data.msg,
							showCancel: false
						});
						this.fetchGoods()
						return 0
					}

					// 修改后会返回购物车信息  覆盖
					this.cartInfo = data
					this.cartList = data.data.reverse()
					this.setTogether(data)
					return 1
				} else {
					this.fetchGoods()
					return
				}
			},
			//获取商品规格
			async getSpecs(id, goodName, goodOriginPrice) {
				this.specsShow = true
				let {
					data
				} = await this.util.request({
					url: this.api.getGoodsSpecs,
					method: 'GET',
					data: {
						id: id
					}
				})
				this.specsGoodsId = id
				this.specsData = data
				this.goodsInfoSpecs = {
					goodName,
					goodOriginPrice
				}
			},
			// 动画初始化
			handleStartAni() {
				this.$refs.aniAdd.init()
			},
			handleGetDotStart(x, y) {
				this.startXY = {
					x: x,
					y: y
				}
			},
			handleGetDotEnd(x, y) {
				this.endXY = {
					x: x,
					y: y
				}
			},
			changeScroll(e) {
				let top = e
				// this.categoryTitleDotArr 计算可能出现小数 所以top+1
				var index = this.categoryTitleDotArr.findIndex(c => c > top + 1)
				if (index === 0) {
					index = 1
				}
				if (index === -1) {
					index = this.categoryTitleDotArr.length
				}
				if ((index - 1) !== this.active) {
					this.changeCategory(index - 1)
				}
			},
			gotoScroll(top) {
				this.changeScroll(top)
				// if(top>this.scrollTop){//上拉
				// 	this.isDown = true
				// }else{//下拉
				// 	this.isDown = false
				// }
				this.isDown = top > this.scrollTop
				this.scrollTop = JSON.stringify(top)
				if (this.isDown) {
					this.nextPage(top)
				} else {
					this.prevPage(top)
				}
				// console.log('min:max',this.min+':'+this.max)
			},
			nextPage(top) {
				let limit = (this.min + this.pageSize) * 320 / this.pxToRpxRate
				if (top >= limit) {
					this.min += this.pageSize
					this.max += this.pageSize
				}

			},
			prevPage(top) {
				let limit = (this.min + this.pageSize) * 320 / this.pxToRpxRate
				if (top <= limit) {
					if (this.min < this.pageSize) {
						this.min = 0
						this.max = 90
						return
					}
					this.min -= this.pageSize
					this.max -= this.pageSize
				}
			},
			selectCategory(index) {
				this.changeCategory(index)
				// 设置左边分类点击后 设置右边商品对应的滚动位置
				this.$emit('update:callbackScroll', this.categoryTitleDotArr[index])
				this.min = this.tempCategoryList[index].goods[0].index - this.pageSize
				this.max = this.tempCategoryList[index].goods[0].index + this.pageSize
			},
			changeCategory(index) {
				// 由右边滚动带动左边 不需要设置右边滚动轴位置
				this.active = index
				this.scrollCategoryTop = (index - 3) * this.categoryHeight
			},
			getCategoryDots() {
				let query = uni.createSelectorQuery().in(this)
				query.selectAll('.categoryTitle').boundingClientRect(res => {
					res.forEach((item, index) => {
						this.categoryTitleDotArr.push(item.top - this.scrollStickyTop)
					})
				}).exec()
			},
			getGoodsBoxDots() {
				let query = uni.createSelectorQuery().in(this)
				query.selectAll('.goods-box').boundingClientRect(res => {
					let index = 0
					this.tempCategoryList.forEach(category => {
						category.goods.forEach(goods => {
							goods.top = res[index].top
							goods.index = index
							index++
						})
					})
					// console.log(index)
				}).exec()
			},
			addCart: utils.throttle(async function(e, goodId, cIndex, gIndex, GetcartId = '', spec = '') {
				// 从购物车内部点击||多规格
				if (GetcartId || spec) {
					let count = await this.modifyCart(goodId, '1', GetcartId, spec)
					let length = this.tempCategoryList.length
					// 在分类中寻找商品 并将count作为商品已购数量赋值
					for (let i = 0; i < length; i++) {
						let result = this.tempCategoryList[i].goods.find(t => t.id === goodId)
						if (result) {
							this.tempCategoryList[i].goods.find(t => t.id === goodId).carNum++
							break
						}
					}
					return
				}
				// 商品列表点击
				// 本地list上 商品+1
				const data = await this.modifyCart(goodId, '1', cartId)
				if(!data){
					return
				}
				let tempGood = this.tempCategoryList[cIndex].goods[gIndex]
				let cartId = tempGood.carNum === 0 ? '' : this.getCartId(tempGood.id)
				if (++this.tempCategoryList[cIndex].goods[gIndex].carNum < tempGood.minNum) {
					this.tempCategoryList[cIndex].goods[gIndex].carNum = tempGood.minNum
				}

				this.handleStartAni()
				// 传入动画起始点 开始动画
				this.handleGetDotStart(e.target.x, e.target.y)
			}, 400),
			removeCart: utils.throttle(async function(goodId, cIndex, gIndex, GetcartId = '', spec = '') {
				// 从购物车内部点击
				if (GetcartId) {
					let count = await this.modifyCart(goodId, '2', GetcartId, spec)
					let length = this.tempCategoryList.length
					// 在分类中寻找商品并更新数量
					for (let i = 0; i < length; i++) {
						const categories = this.tempCategoryList[i]
						const goods = categories.goods.find(t => t.id === goodId)
						if (goods) {
							// 从购物车数据中获取最新的数量
							const cartItem = this.cartList.find(item => item.goodsId === goodId)
							if (cartItem) {
								goods.carNum = Number(cartItem.num) || 0
							} else {
								goods.carNum = 0
							}
							// 强制更新视图
							this.$set(categories.goods, categories.goods.indexOf(goods), goods)
							break
						}
					}
					return
				}
				//本地减一
				let tempGood = this.tempCategoryList[cIndex].goods[gIndex]
				let cartId = tempGood.carNum === 0 ? '' : this.getCartId(tempGood.id)
				if ((--this.tempCategoryList[cIndex].goods[gIndex].carNum) < tempGood.minNum) {
					this.tempCategoryList[cIndex].goods[gIndex].carNum = 0
				}
				this.modifyCart(goodId, '2', cartId)
			}, 400),
			getCartId(goodId) {
				if (!this.cartInfo?.data) return '';
				const cartItem = this.cartInfo.data.find(i => i.goodsId === goodId);
				return cartItem ? cartItem.id : '';
			}
		}
	}
</script>

<style scoped lang="scss">
	.category-label {
		right: 0;
		top: 10rpx;
		font-size: 20rpx;
		background: #FFEFEA;
		border-radius: 20rpx 0 0 20rpx;
		padding: 0 8rpx;
		color: #FF5F2F;
	}

	.icon-bg {
		height: 48rpx;
		width: 48rpx;
		text-align: center;
	}

	.listScroll {
		width: 100%;

		.category {
			padding: 20rpx 20rpx;
			height: 100%;
			display: flex;
			align-items: center;
		}

		.active {
			font-weight: bold;
			color: #000000;
			background: #ffffff;
		}

		.inactive {
			background: #f5f6f9;
		}

		.bs-rb {
			border-radius: 0 0 10rpx 0;
		}

		.bs-rt {
			border-radius: 0 10rpx 0 0;
		}
	}

	.rightList {
		width: 590rpx;
		padding: 30rpx 20rpx 0 20rpx;
		background: #ffffff;
		z-index: 1;
	}

	.categoryTitle {
		height: 60rpx;
		width: 100%;
		line-height: 60rpx;
		background: #ffffff;
		z-index: 3;
	}

	.select-spec {
		font-size: 24rpx;
		font-weight: bold;
		padding: 2rpx 12rpx;
		border-radius: 10rpx;

		.spec-dot {
			position: absolute;
			width: 30rpx;
			height: 30rpx;
			top: -10rpx;
			right: -2rpx;
			border-radius: 50%;
			background: #f10000;
			color: #fff;
			text-align: center;
			font-size: 20rpx;
		}
	}

	.goods-label {
		color: #000;
		background: #f5f6f9;
		padding: 0 6rpx;
		border-radius: 10rpx;
		font-size: 20rpx;
		margin-right: 10rpx;
		margin-top: 5rpx;
	}

	.single {
		margin: 0 10rpx;
		display: inline-block;
		border: 1rpx solid #999;
		border-radius: 10rpx;
		padding: 0 6rpx;
		font-size: 20rpx;
		color: #999;
	}

	.discount {
		display: inline-block;
		border: 1rpx solid #DD6161;
		border-radius: 10rpx;
		padding: 0 6rpx;
		font-size: 20rpx;
		color: #ff5454;

		.text {
			position: relative;
			margin-right: 20rpx;

			&::after {
				position: absolute;
				content: "";
				width: 1rpx;
				height: 20rpx;
				right: -10rpx;
				top: 50%;
				transform: translateY(-50%);
				background-color: #ff5454;
			}
		}
	}
</style>