<template>
	<view>
		<view v-if="goodsTotalNum===0 && isValidReduceArr" class="f-c f20" style="height: 60rpx;">
			<!-- 循环满减 -->
			<block v-if="reduceArr.data.type === '1'">
				<text>每满{{reduceArr.data.fullMoney}}减{{reduceArr.data.money}}</text>
			</block>
			<!-- 阶梯满减 -->
			<block v-else-if="reduceArr.data.type === '2'">
				<text v-for="(text,index) in reduceArr.data.moneyArr" :key='text.fullMoney'>
					<text>{{`满${text.fullMoney}减${text.money}`}}</text>
					<text v-if="index !== reduceArr.data.moneyArr.length-1">,</text>
				</text>
			</block>
		</view>
		<view v-else class="f-c f20" style="height: 60rpx;">
				<text>已减</text>
				<text style="color:#ff5454">{{discount.reduceMoney || 0}}元</text>
				<block v-if="!discount.reduceFullMoney">
					<text>再买</text>
					<text style="color:#ff5454">{{discount.money || 0}}元</text>
					<text>可再减</text>
					<text style="color:#ff5454">{{discount.againReduce || 0}}元</text>
					<text style="color:#ff5454">[去凑单]</text>
				</block>
			<!-- 无商品显示满减规则 -->
		</view>
	</view>
</template>

<script>
	export default {
		props:{
			reduceArr:{
				type:[Object, Boolean],
				default:()=>({
					data: {
						type: '',
						fullMoney: 0,
						money: 0,
						moneyArr: []
					}
				})
			},
			discount:{
				type:Object,
				default:()=>({
					reduceMoney: 0,
					money: 0,
					againReduce: 0,
					reduceFullMoney: false
				})
			},
		},
		data(){
			return{
				goodsTotalNum: 0
			}
		},
		computed: {
			isValidReduceArr() {
				return this.reduceArr && typeof this.reduceArr === 'object' && this.reduceArr.data;
			}
		},
		created() {
			// 计算商品总数
			if (this.$parent && this.$parent.cartList) {
				this.goodsTotalNum = this.$parent.cartList.reduce((total, item) => total + (Number(item.num) || 0), 0);
			}
		}
	}
</script>

<style>
</style>
