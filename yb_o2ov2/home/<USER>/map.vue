<template>
	<view class="page">
		<view class="page-navigation-bar">
			<view class="go-icon">
				<text class="iconfont iconback f40 " @click="go('back')"></text>
			</view>
		</view>
		<view class="map" id="map"></view>
		<view class="search-box">
			<input class="ipt" :placeholder="$t('address.address_tip')" id="input" v-model="address" type="text" @input="onInput" />
		</view>
		<view class="f-y-c mt20 p03">
			<text class="f24 c9">{{$t('location.nearby_address')}}</text>
		</view>
		<view class="p03">
			<view v-for="item in nearbyArr" :key="item.id" class="p20" style="border-bottom: 1px solid #fafafa;"
				@click="changePosition(item)">
				<view class="f30">{{item.title}}</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		Loader
	} from '@googlemaps/js-api-loader'
	import utils from '@/common/utils.js'
	import {
		mapState,
		mapMutations,
		mapActions
	} from 'vuex'
	export default {
		data() {
			return {
				map: null,
				marker: null,
				nearbyArr: [],
				address: '',
				backType: 'position',
				locationInfo: {
					maddress: '',
					latitude: 0,
					longitude: 0
				}
			}
		},
		onLoad(options) {
			if (options && options.type) {
				this.backType = options.type
			}
			// 从本地存储加载位置信息
			const savedLocation = uni.getStorageSync('locationInfo');
			if (savedLocation) {
				this.locationInfo = JSON.parse(savedLocation);
				console.log('使用本地存储的位置信息:', this.locationInfo);
			}
		},
		mounted() {
			// #ifdef H5
			this.initMap()
			// #endif
			this.getNearby()
		},
		computed: {
			...mapState('dndc', ['addInfo']),
		},
		methods: {
			...mapMutations('dndc', ['setLatLng', 'setAddInfo']),
			...mapActions('address', ['setAddressForm']),
			async initMap() {
				const position = {
					lat: Number(this.locationInfo.latitude) || 25.0330, // 默认台北坐标
					lng: Number(this.locationInfo.longitude) || 121.5654
				}
				
				// 获取当前语言设置
				const currentLanguage = sessionStorage.getItem('language') || 'zh-TW';
				
				const loader = new Loader({
					apiKey: 'AIzaSyBTUfOCvsKnvX2H9xr_bbYsho1PzJWQV6c',
					libraries: ['places', 'marker'],
					version: 'weekly',
					language: currentLanguage
				})
				const {
					Map
				} = await loader.importLibrary("maps");
				const {
					AdvancedMarkerElement
				} = await loader.importLibrary("marker");
				
				this.map = new Map(document.getElementById('map'), {
					zoom: 16,
					center: position,
					mapId: 'a6481eb566ad058c',
					language: currentLanguage,
					mapTypeControl: false,
					streetViewControl: false,
					fullscreenControl: false
				});
				this.marker = new AdvancedMarkerElement({
					map: this.map,
					position: position,
					title: "当前位置"
				});
			},
			onInput(e) {
				// #ifdef H5
				if (!e.detail) return
				const input = document.getElementById('input').querySelector('input')
				const autocomplete = new google.maps.places.Autocomplete(input, {
					fields: ['name', 'formatted_address', 'geometry', 'place_id'],
					type: ['geocode'],
					componentRestrictions: {
						country: 'tw'
					},
					location: new google.maps.LatLng(25.0330, 121.5654), // 台北市中心
					radius: 50000 // 50公里半径
				});
				
				autocomplete.addListener('place_changed', () => {
					const places = autocomplete.getPlace();
					if (places.place_id) {
						this.address = places.name || ''
						this.locationInfo = {
							...this.locationInfo,
							latitude: places.geometry.location.lat(),
							longitude: places.geometry.location.lng(),
							maddress: places.name
						};
						
						// 保存到本地存储
						try {
							uni.setStorageSync('locationInfo', JSON.stringify(this.locationInfo));
							console.log('位置信息已更新到本地存储:', this.locationInfo);
							this.setAddressForm({
								address: this.locationInfo.maddress,
								lat: this.locationInfo.latitude,
								lng: this.locationInfo.longitude
							})
							
							// 返回上一页并传递位置信息
							const pages = getCurrentPages();
							const prevPage = pages[pages.length - 2];
							if (prevPage) {
								// 更新上一页的数据
								prevPage.$vm.locationInfo = this.locationInfo;
								// 保存到本地存储，让上一页通过 onShow 监听更新
								uni.setStorageSync('locationInfo', JSON.stringify(this.locationInfo));
							}
							
							// 提示用户位置已更新
							this.util.message('位置已更新');
						} catch (e) {
							console.error('保存位置信息失败:', e);
							this.util.message('保存位置信息失败');
						}
						
						this.map.setCenter(places.geometry.location)
						this.updateMarker(places.geometry.location, places.formatted_address)
						this.getNearby()
					}
				})
				// #endif
			},
			updateMarker(position, title) {
				// 清除旧标记
				if (this.marker) this.marker.setMap(null);

				// 创建新标记
				this.marker = new google.maps.Marker({
					position: position,
					map: this.map,
					title: title
				});
			},
			async getNearby() {
				let {
					data
				} = await this.util.request({
					url: this.api.getNearby,
					method: 'GET',
					data: {
						lat: this.locationInfo.latitude,
						lng: this.locationInfo.longitude,
						pagesize: 10,
						page_index: 1
					},
					is_login: 0
				})
				this.nearbyArr = data.data
			},
			changePosition(item) {
				if (this.backType == 'position') {
					// 更新位置信息
					this.locationInfo = {
						...this.locationInfo,
						latitude: item.location.lat,
						longitude: item.location.lng,
						maddress: item.title
					};
					
					// 保存到本地存储
					try {
						uni.setStorageSync('locationInfo', JSON.stringify(this.locationInfo));
						console.log('位置信息已更新到本地存储:', this.locationInfo);
						// 提示用户位置已更新
						this.util.message('位置已更新');
						
						// 返回上一页并传递位置信息
						// const pages = getCurrentPages();
						// const prevPage = pages[pages.length - 2];
						// if (prevPage) {
						// 	// 更新上一页的数据
						// 	prevPage.$vm.locationInfo = this.locationInfo;
						// 	// 保存到本地存储，让上一页通过 onShow 监听更新
						// 	uni.setStorageSync('locationInfo', JSON.stringify(this.locationInfo));
						// }
						this.go('reLaunch', '/yb_o2ov2/index/index')
					} catch (e) {
						console.error('保存位置信息失败:', e);
						this.util.message('保存位置信息失败');
					}
				} else {
					this.setAddressForm({
						address: item.title,
						lat: item.location.lat,
						lng: item.location.lng
					})
				}
				this.go('back')
			}
		}
	}
</script>

<style lang="scss" scoped>
	.page {
		width: 100vw;

		.map {
			width: 100%;
			height: 600rpx;
		}

		.search-box {
			width: 100%;
			height: 80rpx;
			padding: 0 30rpx;
			box-sizing: border-box;
			margin-top: 20rpx;

			.ipt {
				width: 100%;
				height: 100%;
				background-color: #fff;
				border-radius: 10rpx;
				padding-left: 20rpx;
				box-sizing: border-box;
				font-size: 28rpx;
			}
		}
	}
</style>