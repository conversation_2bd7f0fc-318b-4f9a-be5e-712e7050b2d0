<template>
	<view class="w100 h100v">
		<!-- 头部 start -->
		<view class="bf p-f w100 f-y-c pl20" style="top: 0;z-index: 2;" :style="{height:`${statusNavBarHeight}px`,paddingTop:`${menuButtonTop}px`}" >
			<text class="iconfont iconback f40 c0" @click="go('back')"></text>
			<!-- 搜索 -->
			<view class="f-y-c p1 ml30"
			style="background: #f5f5f5;border-radius: 30rpx;width: 450rpx;"
			 @click="go('navigateTo','/yb_o2ov2/home/<USER>')">
				<text class="iconfont iconsearch f28 ml20 cd"></text>
				<text class="ml10 c6">请输入商店或商品名称</text>
			</view>
		</view>
		<!-- 头部 end -->
		<!-- 商品组 -->
		<view class="p-r flex listScroll" :style="{marginTop:`${statusNavBarHeight}px`}">
			<view class="f-row bf" :style="leftScrollStyle">
				<!-- leftList 分类 -->
				<scroll-view :scroll-y="true" :scroll-top="scrollLeftTop" class="f-col"
					style="width: 160rpx;">
					<view v-for="(item,index) in tempCategoryList" :key="item.id"
						:style="{height:categoryHeight*pxToRpxRate + 'rpx'}" class="bf5">
						<view class="category c9 f30"
							:class="[active === index?'active':'inactive',{'bs-rb':active === (index+1)},{'bs-rt':active === (index-1)}]"
							@tap.stop="selectCategory(index)">
							<text :style="active === index?`color:#ef8733`:''">{{item.name}}</text>
							<view class="t-c num-label"
							:style="active === index?`color:#ef8733`:'color:#999'">
								{{item.storeNum}}
							</view>
						</view>
					</view>
					<!-- 占位元素 -->
					<view :style="{height:categoryHeight*pxToRpxRate + 'rpx'}" class="bd"></view>
					<view :style="{height:categoryHeight*pxToRpxRate + 'rpx'}" class="bd"></view>
					<view :style="{height:categoryHeight*pxToRpxRate + 'rpx'}" class="bd"></view>
					<view :style="{height:categoryHeight*pxToRpxRate + 'rpx'}" class="bd"></view>
					<view :style="{height:categoryHeight*pxToRpxRate + 'rpx'}" class="bd"></view>
				</scroll-view>
			</view>
			<!-- rightList 商品-->
			<view :style="{height:`${scrollHeight}rpx`,background:'#f5f6f9'}">
				<scroll-view scroll-y class="rightList" @scroll="changeScroll" :scroll-top="scrollRightTop">
					<view  v-for="(item,categoryIndex) in tempCategoryList" :key="item.id" style="background: #f5f6f9;">
						<view class="categoryTitle f30" :style="rightCategoryTitleStyle">{{item.name}}</view>
						<view class="bs20 bf flex f-w">
							<view class="mt20 f-c-c mb30"
							style="width: 180rpx;"
							@click="go('navigateTo',`/yb_o2ov2/home/<USER>/category-store?pid=${item.id}&id=${item.id}`)">
								<view style="width: 80rpx;height: 80rpx;"><image class="wh" :src="item.icon" mode=""></image></view>
								<view class="mt30 c3 f24">全部</view>
								<view class="t-c num-label c9">
									{{item.storeNum}}
								</view>
							</view>
							<view v-for="(good,goodIndex) in item.child" :key="good.id" 
							class="mt20 f-c-c mb30"
							style="width: 180rpx;"
							@click="go('navigateTo',`/yb_o2ov2/home/<USER>/category-store?pid=${good.pid}&id=${good.id}`)">
								<view style="width: 80rpx;height: 80rpx;"><image class="wh" :src="good.icon" mode=""></image></view>
								<view class="mt30 c3 f24">{{good.name}}</view>
								<view class="t-c num-label c9">
									{{good.storeNum}}
								</view>
							</view>
						</view>
						
					</view>
					<view style="width: 100%;height: 20vh;"></view>
				</scroll-view>
			</view>
			
		</view>
	</view>
</template>

<script>
	import utils from '@/common/utils.js'
	export default {
		props:{
			categorySet:{
				type:Object,
				default:()=>{}
			},
			isOpen:{
				type:Boolean,
				default:false
			},
			callbackScroll:{
				type:[String,Number],
				default:0
			},
			categoryList:{ //商品分类
				type:Array,
				default:()=>[]
				// function(){
				// 	return ['推荐', '🍑季节水果茶', '👍麻薯系列', '🥥生打椰', '🍉四季水果茶', '🥛双皮奶', '🍰蛋糕系列', '🔥超人气奶茶', '🍶换厚乳奶茶',
				// 	'❄️冷萃茶', '🍭️加點区', '❤️茶粉小提示'
				// 	]
				// }
			},
		},
		data(){
			return {
				active: 0, //当前激活分类的索引
				scrollLeftTop: 0, //左侧分类scroll的滚动位置
				scrollRightTop: 0,
				categoryHeight: 75, //左侧分类一个元素的高度
				categoryTitleDotArr:[],//右侧每个categoryTitle的top位置信息arr
				tempCategoryList:[],
			}
		},
		computed: {
			scrollHeight() {
				return (this.wHeight - this.statusNavBarHeight) * this.pxToRpxRate 
			},
			leftScrollStyle() {
				return `position:sticky;top:${this.scrollStickyTop}px;height:${this.scrollHeight}rpx;z-index: 1;width:160rpx;`
			},
			rightCategoryTitleStyle() {
				return `position:sticky;top:0px;`
			},
			scrollStickyTop() {
				//部分机型rpx转化px丢失精度过大 所以-1px
				return Math.floor(this.statusNavBarHeight - 1)
			},
		},
		onLoad() {
			this.fetchData()
		},
		methods:{
			async fetchData(){
				let {data} =  await this.util.request({
					url: this.api.CategoryListAll,
					method: 'GET'
				})
				this.tempCategoryList = data
				this.$nextTick(()=>{
					this.getCategoryDots()
				})
			},
			changeScroll(e) {
				let top = e.detail.scrollTop
				// this.categoryTitleDotArr 计算可能出现小数 所以top+1
				var index = this.categoryTitleDotArr.findIndex(c=>c>top+1)
				if(index===0){
					index = 1
				}
				if(index===-1){
					index = this.categoryTitleDotArr.length
				}
				if((index-1) !== this.active){
					this.changeCategory(index-1)
				}
			},
			selectCategory(index) {
				this.changeCategory(index)
				// 设置左边分类点击后 设置右边商品对应的滚动位置
				this.scrollRightTop = this.categoryTitleDotArr[index]
				console.log(this.scrollRightTop )
			},
			changeCategory(index){
				// 由右边滚动带动左边 不需要设置右边滚动轴位置
				this.active = index
				this.scrollLeftTop = (index - 3) * this.categoryHeight
			},
			getCategoryDots(){
				let query = uni.createSelectorQuery().in(this)
				query.selectAll('.categoryTitle').boundingClientRect(res=>{
					res.forEach(item=>{
						this.categoryTitleDotArr.push(item.top-this.scrollStickyTop)
					})
					console.log(this.categoryTitleDotArr)
				}).exec()
			}
		}
	}
</script>

<style scoped lang="scss">
	.listScroll {
		.category {
			display: flex;
			align-items: center;
			flex-direction: column;
			height: 100%;
			padding: 20rpx;
		}
		.active {
			font-weight: bold;
			background: #f5f6f9;
			.num-label{
				font-size: 24rpx;
				margin-top: 10rpx;
				border-radius:20rpx;
				padding: 1rpx 12rpx;
				min-width: 70rpx;
				background:#FFFFFF;
			}
		}
	
		.inactive {
			background: #ffffff;
			color: #000000;
			.num-label{
				font-size: 24rpx;
				margin-top: 10rpx;
				border-radius:20rpx;
				padding: 1rpx 12rpx;
				min-width: 70rpx;
				background:#f5f5f5;
			}
		}
	
		.bs-rb {
			border-radius: 0 0 20rpx 0;
		}
	
		.bs-rt {
			border-radius: 0 20rpx 0 0;
		}
	}
	.rightList{
		display: flex;
		flex-direction: column;
		height: 100%;
		width: 590rpx;
		margin: 0 30rpx 0 20rpx ;
		background: #ffffff;
		z-index: 1;
		.num-label{
			font-size: 24rpx;
			margin-top: 10rpx;
			border-radius:20rpx;
			padding: 1rpx 12rpx;
			min-width: 70rpx;
			background:#f5f5f5;
		}
	}
	.categoryTitle{
		height:90rpx;
		width:100%;
		line-height:90rpx;
		background:#f5f6f9;
		z-index: 3;
	}
</style>
