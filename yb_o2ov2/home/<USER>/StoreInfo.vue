<template>
	<view :style="{minHeight:viewHeight+'px'}">
		<view class="mt20 bf p2">
			<view class="card" v-if="shopData.id && plugin.length">
				<!-- <view class="f-bt">
					<view class="title">我的应用</view>
					<view class="c9 f26 f-c"  @click="goComment(0)">全部应用 <text class="iconfont iconinto f24 c9 ml5"></text></view>
				</view> -->
				<view class="f-x-c-sa">
					<view class="f-c-c mt20" @click="go('navigateTo',`/yb_o2ov2/shop/reserve/index?storeId=${shopData.id}`)" v-if="plugin.includes('reserve')">
						<view class="f-y-c f30">
							<view class="" style="width: 40rpx;height: 40rpx;">
								<image class="wh" :src="config.reserve.icon?config.reserve.icon:'/static/img_my/3.png'"  mode=""></image>
							</view>
						</view>
						<view class="mt10">
							<text class="">{{config.reserve.curName || '餐桌预定'}}</text>
						</view>
					</view>
					<view  class="f-c-c mt20"  @click="go('navigateTo',`/yb_o2ov2/shop/lineup/pdqh?storeId=${shopData.id}`)" v-if="plugin.includes('queuing')">
						<view class="f-y-c f30">
							<view class="" style="width: 40rpx;height: 40rpx;">
								<image class="wh" :src="config.queuing.icon?config.queuing.icon:'/static/img_my/4.png'" mode=""></image>
							</view>
						</view>
						<view class="mt10">
							<text class="">{{config.queuing.curName || '排队取号'}}</text>
						</view>
					</view>
<!-- 					<view class="f-c-c mt20" @click="goComment(1)">
						<view class="f-y-c f30">
							<view class="" style="width: 40rpx;height: 40rpx;">
								<image class="wh" :src="styles.store.img.search('/static/img/') === -1?styles.store.img:'/static/img_my/1.png'" mode=""></image>
							</view>
						</view>
						<view class="mt10">
							<text class="">集点活动</text>
						</view>
					</view> -->
					<view  class="f-c-c mt20" @click="go('navigateTo',`/yb_o2ov2/shop/storage/index?storeId=${shopData.id}`)" v-if="plugin.includes('storage')">
						<view class="f-y-c f30">
							<view class="" style="width: 40rpx;height: 40rpx;">
								<image class="wh"  :src="config.storage.icon2?config.storage.icon2:'/static/img_my/2.png'" mode=""></image>
							</view>
						</view>
						<view class="mt10">
							<text class="">{{config.storage.curName || '酒水寄存'}}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="mt20 bf">
			<view class="f-y-c f30 p2 c3">
				<view>
					<text class="c9 mr10 iconfont iconpositionfill f30"></text>
				</view>
				<view class="f-g-1">
					<text class="c3">{{shopData.address}}</text>
				</view>
				<!-- <view class="t-c" style="width: 100rpx;" @click.stop="call">
					<text class="c9 iconfont iconphone f30"></text>
				</view> -->
			</view>
			<scroll-view scroll-x="true" style="width: 100%;" class="f-x-bt ws-n p02">
				<view v-for="(item,index) in shopData.environment" :key="item.name" class="dis-in mr20 bs10" style="width: 190rpx;height: 110rpx;"
				@click="checkImg(item,index)">
					<image class="wh" :src="item" mode="aspectFill" ></image>
				</view>
			</scroll-view>
			<!-- <u-cell-item  :title-style="{fontSize:'30rpx',color:'#333'}" :title="$t('good.view_food_safety_records')"  @click="navigation2">
				<text slot="icon" class="c9 mr10 iconfont icontaketalk f30"></text>
			</u-cell-item> -->
		</view>

		<view class="mt20 bf">
			<u-cell-item :arrow="false" :title-style="{fontSize:'30rpx',color:'#333'}" :title="title.deliveryMode">
				<text slot="icon" class="c9 mr10 iconfont icontakeout f30"></text>
			</u-cell-item>
			<u-cell-item :arrow="false" :title-style="{fontSize:'30rpx',color:'#333'}" :title="title.timeArr">
				<text slot="icon" class="c9 mr10 iconfont icontime f30"></text>
			</u-cell-item>
		</view>

		<view class="mt20 bf">
			<u-cell-item :arrow="false" title-width="600"
				:title-style="{fontSize:'30rpx',color:'#333',overflow: 'hidden',textOverflow:'ellipsis',whiteSpace: 'nowrap'}"
				:title="moreSet.notice">
				<text slot="icon" class="c9 mr10 iconfont iconnotice f30"></text>
			</u-cell-item>
			<u-cell-item :arrow="false">
				<text slot="icon" class="c9 mr10 iconfont icontaketalk f30"></text>
				<text slot="title" class="f30 c3 f-y-c">
					 {{$t('good.merchant_services')}}：
					 <block>
						 <text class="bs10 f24 wei m02" style="border: 1rpx solid #9dd7fb;color:#9dd7fb;padding: 0 6rpx;margin-right: 5rpx;">跨</text>
						  {{$t('good.cross_day_booking')}}
					 </block>
					 <block v-if="moreSet.distributionSupport.includes('2')">
						 <text class="bs10 f24 wei m02" style="border: 1rpx solid #9dd7fb;color:#9dd7fb;padding: 0 6rpx;margin-right: 5rpx;">自</text>
						  {{$t('good.pick_up_at_the_storez')}}
					 </block>
				 </text>
			</u-cell-item>
		</view>
	</view>

</template>

<script>
	export default {
		props: {
			tabNavHeight: { //tabNav高度单位rpx
				type: [String, Number],
				default: 80
			},
			shopData:{
				type:Object,
				default:()=>{}
			},
			moreSet:{
				type:Object,
				default:()=>{}
			},
		},
		created() {
			// this.payPlugin()
		},
		data() {
			return {
				title:{},
				plugin:[],
				config:{},
			}
		},
		computed: {
			viewHeight() {
				return this.wHeight - this.statusNavBarHeight - this.tabNavHeight / this.pxToRpxRate
			},
		},
		watch:{
			shopData(){
				let storeIsOpen = false
				let result = ''
				try{
					if(this.shopData.timeType === 1){
						storeIsOpen = true
						result += this.$t('good.alltoday')
					}else{
						let date = new Date()
						let hours = date.getHours()
						let min = date.getMinutes()
						console.log('时间',hours,min)
						this.shopData.timeArr.forEach(item=>{
							let sTime = item.startTime.split(":")
							let eTime = item.endTime.split(":")
							//判断营业时间
							if(!item.ciri){//同一天
								if((hours>=sTime[0]&&min>=sTime[1])&&(hours<=eTime[0]&&min<=eTime[1])){
									storeIsOpen = true
								}else{
									storeIsOpen = false
								}
							}else{//次日
								
							}
							result += `${item.startTime}-${item.ciri?'次日':''}${item.endTime}  `
						})
					}
					this.payPlugin()
				}catch(e){
					console.log(e)
					result = ''
					//TODO handle the exception
				}
				this.title = {
					deliveryMode: this.$t('location.service', { mode: this.shopData.deliveryMode }),
					timeArr: this.$t('location.hours', { time: result }),
				}
			}
		},
		methods:{
			async payPlugin(){
				let {data} =  await this.util.request({
					url: this.api.payPlugin,
					method: 'GET',
					data:{
						storeId: this.shopData.id,
					}
				})
				this.plugin = data.plugin?data.plugin:[]
				this.config = data.config?data.config:{}
			},
			checkImg(item,index){
				console.log(item,index,this.shopData)
				uni.previewImage({
					urls: this.shopData.environment,
					current: index,
					longPressActions: {
						itemList: ['发送给朋友', '保存图片', '收藏'],
						success: function(data) {
							
						},
						fail: function(err) {
							
						}
					}
				});
			},
			call(){
				uni.makePhoneCall({
				    phoneNumber: this.shopData.storeTel //仅为示例
				});
			},
			navigation(){
				let info = {
					lat:this.shopData.lat,
					lng:this.shopData.lng,
					name:this.shopData.name,
					address:this.shopData.address
				}
				this.util.ckWz(info)
			},
			navigation2(){
				this.go('navigateTo','/yb_o2ov2/home/<USER>' + encodeURIComponent(this.shopData.index))
			},
		}
	}
</script>

<style>
</style>
