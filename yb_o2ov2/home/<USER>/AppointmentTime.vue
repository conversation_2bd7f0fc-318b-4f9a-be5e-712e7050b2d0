<template>
	<view>
		<!-- 弹出按钮 -->
		<view @click="showPopup = true" class="select-time-button">
			{{selectedDateTime || $t('good.selecte_time')}}
		</view>

		<!-- 弹窗 -->
		<u-popup v-model="showPopup" mode="bottom" border-radius="20">
			<view class="popup-content">
				<!-- 标题 -->
				<view class="popup-header">
					<text @click="showPopup = false" class="cancel-text">{{ $t('common.cancel') }}</text>
					<text class="title-text">{{ $t('good.selecte_time') }}</text>
					<text @click="confirmSelection" class="confirm-text">{{ $t('common.ok') }}</text>
				</view>

				<!-- 日期和时间选择器 -->
				<view class="picker-container">
					<scroll-view scroll-y class="date-picker">
						<view 
							v-for="(date, index) in appointmentTime" 
							:key="index"
							class="date-item"
							:class="{ active: selectedIndex.date === index }"
							@click="selectDate(index)"
						>
							<view class="date-day">{{ date.day }}</view>
							<view class="date-week">{{ date.weekName }}</view>
						</view>
					</scroll-view>

					<scroll-view scroll-y class="time-picker">
						<view 
							v-for="(time, index) in selectedTimes" 
							:key="index"
							class="time-item"
							:class="{ active: selectedIndex.time === index }"
							@click="selectTime(index)"
						>
							{{ time }}
						</view>
					</scroll-view>
				</view>
			</view>
		</u-popup>

	</view>
</template>

<script>
export default {
	props: {
		appointmentTime: {
			type: Array,
			default: () => []
		}
	},
	data() {
		return {
			showPopup: false,
			selectedIndex: {
				date: 0,
				time: -1
			},
			selectedDateTime: ''
		};
	},
	computed: {
		selectedTimes() {
			if (this.selectedIndex.date === -1 || !this.appointmentTime[this.selectedIndex.date]) return [];
			return this.appointmentTime[this.selectedIndex.date].timesArr;
		}
	},
	methods: {
		selectDate(index) {
			this.selectedIndex.date = index;
			this.selectedIndex.time = -1; // 重置时间选择
		},
		selectTime(index) {
			this.selectedIndex.time = index;
		},
		confirmSelection() {
			if (this.selectedIndex.date === -1 || this.selectedIndex.time === -1) {
				uni.showToast({ title: this.$t('good.selecte_time'), icon: 'none' });
				return;
			}

			const selectedDate = this.appointmentTime[this.selectedIndex.date];
			const selectedTime = selectedDate.timesArr[this.selectedIndex.time];
			
			this.selectedDateTime = `${selectedDate.day} ${selectedTime}`;
			this.showPopup = false;
			
			// 触发事件，传递选择结果
			this.$emit('time-selected', {
				date: selectedDate,
				time: selectedTime,
				fullTime: `${selectedDate.time} ${selectedTime}`
			});
		}
	}
};
</script>

<style scoped>
.select-time-button {
	/* padding: 20rpx; */
	/* background-color: #f5f5f5; */
	/* text-align: center; */
	/* border-radius: 8rpx; */
	/* margin: 40rpx; */
	font-size: 24rpx;
	color: #333;
}

.popup-content {
	height: 600rpx;
	background-color: #fff;
}

.popup-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	font-size: 32rpx;
	font-weight: bold;
	border-bottom: 1px solid #eee;
}

.cancel-text, .confirm-text {
	color: #666;
	font-size: 28rpx;
	font-weight: normal;
}

.confirm-text {
	color: #007AFF;
}

.picker-container {
	display: flex;
	height: 500rpx;
	overflow: hidden;
}

.date-picker, .time-picker {
	flex: 1;
	padding: 20rpx;
	overflow-y: auto;
}

.date-item, .time-item {
	padding: 20rpx;
	margin-bottom: 20rpx;
	border-radius: 8rpx;
	text-align: center;
}

.date-item.active, .time-item.active {
	background-color: #e8f4ff;
	color: #007AFF;
}

.date-day {
	font-size: 32rpx;
	font-weight: bold;
	margin-bottom: 10rpx;
}

.date-week {
	font-size: 24rpx;
	color: #999;
}

.time-item {
	font-size: 28rpx;
}

.selected-time {
	padding: 20rpx;
	margin: 40rpx;
	background-color: #f8f8f8;
	border-radius: 8rpx;
	font-size: 28rpx;
	color: #333;
}
</style>