<template>
	<mescroll-uni ref="mescroll" :height="`${wHeight}px`" :down="{auto:false}" :up="{onScroll:true,auto:false}"
	 @scroll="listeningScroll" @down="refresh" @up="onreachBottom" class="p-r">
		<!-- 头部 start -->
		<view class="p-f" style="z-index: 3;left: 20rpx;width: 60rpx;height:60rpx;" :style="{top:`${menuButtonTop}px`}" @click="go('back')">
			<text class="iconfont iconback f40 cf" :style="{color:cfontColor}"></text>
		</view>
		<!-- 头部渐变背景 -->
		<view class="p-f w100" style="z-index: 2;left: 0;top:0"
			:style="{height:`${statusNavBarHeight}px`,background:mainColor,opacity:opacity}"></view>
		<!-- 头部 end -->
		<view class="t-c w100"
			:style="{height:`${statusNavBarHeight}px`,paddingTop:`${menuButtonTop}px`,background:mainColor}">
			<view class="mr20">
				<text class="category-name cf f36 wei" :style="{color:cfontColor}">{{categoryName}}</text>
			</view>
		</view>
		<!-- main start-->
		<view :style="{background:mainColor}">
			<view class="bg-card">
				<view class="p025  posi-s" style="z-index: 2;" :style="{top: `${menuButtonTop}px`,}" >
					<view class="bs30-nh" :style="[searchStyle]">
						<!-- 搜索 -->
						<view class="f-y-c p1" @click="go('navigateTo','/yb_o2ov2/home/<USER>')">
							<text class="iconfont iconsearch f28 ml20 cd"></text>
							<text class="ml10 c6">请输入商店或商品名称</text>
						</view>
					</view>
				</view>
				<!-- 关键字 -->
		<!-- 		<view class="f-y-c p025 mt10">
					<text class="recommend" v-for="word in keywords" :key="word">{{word}}</text>
				</view> -->
				<!-- 幻灯片 -->
				<view class="mt20 p025">
					<StoreSwiper ref="storeswiper" type="2" :pid="categoryQuery.pid" :styles="swiperStyles"></StoreSwiper>
				</view>
				<!-- 细化分类 -->
				<block v-if="categoryArr && categoryArr.child.length>0">
				<scroll-view scroll-x="true" class="w100 m20 ws-n f-y-c posi-s categoryStickyDot"
					:scroll-into-view="categoryIndex"
					:style="{top:`${statusNavBarHeight-80/pxToRpxRate}px`,background:categorySticky?'#fff':''}"
					>
					<view class="dis-in ml20" style="height: 150rpx;" id="category-1"
					 @click="clickCategory(-1)">
						<view class="f-c-c">
							<view class="bsf bf" style="width: 80rpx;height: 80rpx;">
								<image class="wh" :src="categoryArr.icon" mode=""></image>
							</view>
							<view class="p-r mt20">
								<view :style="selectCategory===-1?`font-weight:bold;color:${tColor}`:'color:#666'">全部</view>
								<view class="p-a p-a-xc" style="width: 30rpx;height: 8rpx;top: 44rpx;" :style="(categorySticky&&selectCategory===index)?`background:${mainColor}`:''"></view>
							</view>
						</view>
					</view>
					<view class="dis-in ml30" v-for="(item,index) in categoryArr.child" :key="item" style="height: 150rpx;"
					:id="`category${index}`"
					 @click="clickCategory(index)">
						<view class="f-c-c">
							<view class="bsf bf" style="width: 80rpx;height: 80rpx;">
								<image class="wh" :src="item.icon" mode=""></image>
							</view>
							<view class="p-r mt20">
								<view :style="selectCategory===index?`font-weight:bold;color:${tColor}`:'color:#666'">{{item.name}}</view>
								<view class="p-a p-a-xc" style="width: 30rpx;height: 8rpx;top: 44rpx;" :style="(categorySticky&&selectCategory===index)?`background:${mainColor}`:''"></view>
							</view>
						</view>
					</view>
					<!-- 占位元素 -->
					<view class="dis-in" style="width: 20rpx;height: 1rpx;"></view>
				</scroll-view>
				</block>
				<!-- rpx转化px会舍弃小数位 -1 修正误差 -->
				<view style="height: 140rpx;" class="posi-s pb10" :style="{top:`${statusNavBarHeight + 70/pxToRpxRate -1}px`,background:categorySticky?'#fff':''}">
					<dropdown :isFixed="categorySticky"></dropdown>
				</view>
				<StoreList ref="storelist"></StoreList>
			</view>
		</view>
		<!-- 1、商店优惠券2、平台优惠券3、新人专享弹窗 -->
		<tcyhq type="1" @close='sjhbshow=false' :co='sjhbInfo' v-model="sjhbshow" :color='tColor'></tcyhq>
		<tcyhq type="2" @close='pthbshow=false' :co='pthbInfo' v-model="pthbshow" :color='tColor'></tcyhq>
		<tcyhq type="3" @close='xrzxshow=false' :co='xrzxInfo' v-model="xrzxshow" :color='tColor'></tcyhq>
		<!-- main end -->
	</mescroll-uni>
</template>

<script>
	import {
		mapState,
	} from 'vuex'
	import StoreList from '@/components/StoreList.vue'
	import StoreSwiper from "@/yb_o2ov2/index/component/drag/StoreSwiper.vue"
	import Dropdown from '@/components/Dropdown.vue'
	import tcyhq from '@/components/template/tcyhq.vue'
	export default {
		components: {
			StoreList,StoreSwiper,Dropdown,tcyhq,
		},
		data() {
			return {
				categoryName:'',//标题标签样式
				mainColor:'',
				cfontColor:'',
				swiperStyles:{
					marginTop:0,
					marginLR:0,
					marginBottom:0,
					circleBtn:15
				},
				// keywords:[],
				subtitleIndex:'',
				subtitles:['百亿补贴','吃货联盟','30分钟达','满减优惠','商店名称','商店名称'],
				value1:1,
				options1: [{label: '综合排序',value: 1,},{label: '距离优先',value: 2,},{label: '销量优先',value: 3,},{label: '评分优先',value: 4,}],
				scrollTop: 0,
				searchStyle: {
					width:'700rpx',
					marginLeft:'0px',
					background: '#ffffff'
				},
				opacity: 0,
				categoryStickyDot:'',
				categorySticky:false, // 吸顶修改背景色
				selectCategory:0,
				categoryQuery:{},
				categoryArr:[],
				categoryIndex:'',
				sjhbInfo: '',
				sjhbshow: false,
				pthbInfo: '',
				pthbshow: false,
				xrzxInfo: '',
				xrzxshow: false,
			}
		},
		computed:{
			...mapState({
				layout:state=>state.layout.index.body
			}),
			selectStyle(){
				return  boolean=>{
					let style = {}
					if(this.categorySticky){
						style.background="#f5f6f9"
					}
					if(boolean){
						style.background=this.tColor
						style.fontWeight = 'bold'
					}
					return style
				}
			}
		},
		watch: {
			scrollTop(val) {
				let limit = 30
				if (val < 0) {
					this.opacity = 0
					this.searchStyle.width = `700rpx`
					this.searchStyle.marginLeft = `0px`
					return
				}
				if (val < limit) {
					this.searchStyle = {
						width: `${(700/this.pxToRpxRate)-this.scrollTop*3.7}px`,
						marginLeft: `${this.scrollTop}px`
					}
					this.opacity = val / limit
				} else {
					this.searchStyle.width = `480rpx`
					this.searchStyle.marginLeft = `${limit}px`
					this.searchStyle.background = '#f5f5f5'
					this.opacity = 1
				}
				if(val > this.categoryStickyDot){
					this.categorySticky = true
				}else{
					this.categorySticky = false
				}

			}
		},
		methods:{
			onreachBottom(e){
				this.$refs.storelist.nextPage()
				e.endBySize()
			},
			async refresh(e){
				await this.filterData()
				this.$nextTick(()=>{//改了id需要在下次循环才会生效
				 this.$refs.storeswiper.fetchData()
				})
				setTimeout(()=>{
					e.endBySize()
				},500)
			},
			init(){
				// try{
				// 	this.keywords = (this.layout.list.find(i=>i.title === '地址定位').styles.fixedSearch.keyword).split(',') || false
				// }catch(e){
				// 	this.keywords = false
				// 	//TODO handle the exception
				// }
				this.getCategories()
				this.$nextTick(()=>{//改了id需要在下次循环才会生效
					this.$refs.storeswiper.fetchData()
				})
			},
			filterData(){
				// 传参 请求数据
				try{
					this.$refs.storelist.setApi(this.api.storeList,{lat:this.latLng.latitude,lng:this.latLng.longitude,catId:this.categoryQuery.id})
					this.$refs.storelist.refresh()
					// this.$refs.goodslist[0].setApi(this.api.dplb)
					// this.$refs.goodslist[0].fetchData()
				}catch(e){
					//TODO handle the exception
					this.util.message('网络异常', 3)
				}
			},
			async getCategories(){
				let {data} =  await this.util.request({
					url: this.api.CategoryListSec,
					method: 'GET',
					data:{id:this.categoryQuery.pid}
				})
				this.categoryArr=data
				this.mainColor = data.color
				this.cfontColor = data.configData && data.configData.fontColor ? data.configData.fontColor:this.fontColor
				this.categoryName = data.name
				if( +this.categoryQuery.id === +data.id){
					this.selectCategory = -1
				}else{
					data.child.map((item,index)=>{
						if(+this.categoryQuery.id === +item.id){
							this.selectCategory = index
						}
					})
				}
				this.clickCategory(this.selectCategory)
			},
			clickCategory(index){
				this.selectCategory=index
				if(index <2){
					this.categoryIndex = `category-1`
				}else{
					this.categoryIndex = `category${index-3}`
				}
				if(this.selectCategory === -1){
					this.categoryQuery.id = this.categoryQuery.pid
				}else{
					this.categoryQuery.id = this.categoryArr.child[index].id
				}
				this.filterData()
			},
			getCategoryStickyDot(){
				// 以下页面跳转指的是v-show
				// 当前ScrollTop超过这个点(ScrollDot) 跳转页面的历史scroll位置最低也得是ScrollDot
				// 小于ScrollDot 跳转页面的scroll位置等于当前页面的ScrollTop
				let query = uni.createSelectorQuery().in(this);
				query.select('.categoryStickyDot').boundingClientRect((res) => {
					this.categoryStickyDot = res.top - 50/this.pxToRpxRate
					// console.log('res',res)
				}).exec()
			},
			listeningScroll(e){
				this.scrollTop = e.scrollTop
			},
			async getXrzx() {
				let res = await this.util.request({
					'url': this.api.sytchj,
					'data':{
						couponType:2,
						categoryId:this.categoryQuery.id
					}
				})
				if (res.data.newCoupon) {
					this.xrzxInfo = res.data.newCoupon.data
					setTimeout(() => {
						this.xrzxshow = true
					}, 1000)
				}
				if (res.data.windowCoupons) {
					if(res.data.windowCoupons.couponType=='1'){
						this.pthbInfo = res.data.windowCoupons
						setTimeout(() => {
							this.pthbshow = true
						}, 2000)
					}else if(res.data.windowCoupons.couponType=='2'){
						this.sjhbInfo = res.data.windowCoupons
						setTimeout(() => {
							this.sjhbshow = true
						}, 2000)
					}
				}
			},
		},
		onLoad(option) {
			this.categoryQuery={
				id:option.id, //查询商店列表用到
				pid:option.pid //查询分类用到
			}
			this.init()
			this.getXrzx()
		},
		onReady() {
			this.getCategoryStickyDot()
		},
		// onPageScroll(e) {
		// 	this.scrollTop = e.scrollTop
		// },
		// onReachBottom() {
		// 	this.$refs.storelist.nextPage()
		// }
	}
</script>

<style scoped lang="scss">
	.recommend {
		background: #fff;
		padding: 5rpx 14rpx;
		border-radius: 20rpx;
		font-size: 22rpx;
		margin-right: 10rpx;
	}

	.bg-card {
		background: #f5f5f5;
		padding-top: 20rpx;
		border-radius: 30rpx 30rpx 0 0;
	}

	.red-label {
		font-size: 20rpx;
		padding: 2rpx 10rpx;
		margin-right: 10rpx;
		border-radius: 5rpx;
		background: #E85D54;
		color: #fff;
	}
	.element{
		font-size: 24rpx;
		border-radius: 10rpx;
		background: #FFF;
		padding: 10rpx 36rpx;
		margin-right: 10rpx;
		text-align: center;
	}
	.dropdown-item-speed{
		width: 160rpx;
		text-align: center;
		margin-top: 20rpx;
		.dropdown-item-speed-active{
			display: block;
			padding: 10rpx 8rpx;
			border-radius: 10rpx;
			font-weight: bold;
			color: #ff964d;
			background: #fefbf2;
			border: 1px solid #ff964d;
		}
		.dropdown-item-speed-inactive{
			display: block;
			padding: 10rpx 8rpx;
			border-radius: 10rpx;
			background: #f5f5f5;
			border: 1px solid #f5f5f5;
		}
	}
	.dropdown-item-speed-btn{
		width: 300rpx;
		height: 80rpx;
		line-height: 80rpx;
		border-radius: 10rpx;
	}
</style>
