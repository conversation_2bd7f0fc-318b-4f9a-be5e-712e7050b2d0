<template>
	<view class="container pb130">
		<form @submit="formSubmit">
			<view class="">
				<view class="cf p-r" :style="{background:tColor,color:fontColor}">
					<view class="rwimg">
						<image class="wh" :src="payConfig.icon && payConfig.icon" mode="aspectFit"></image>
					</view>
					<view class="p-a dftxt f32 pt105">{{payConfig && payConfig.help}}</view>
				</view>
				<view class="zlbd p-r o-h">
					<view class="bf p3 f24 c9 bs10 f-bt">
						<view>代付金额</view>
						<view v-if="info">收货人：{{info.receivedName}}</view>
					</view>
					<view class="bf p3 f24 c9 f-bt">
						<view class="paymoeny wei f50 cf0"><text class="">{{sl}}</text><text
								class="cf0">{{pmoney}}</text></view>
						<view v-if="info" class="f26 djsc">剩余付款时间：<text class="ml10"
								v-if="stime">{{stime[2]}}:{{stime[3]}}</text></view>
					</view>
				</view>
				<view v-if="info" class="p2 mb30 bs20 mt10 bf">
					<!-- 商品 -->
					<view v-for="goods in info.goodsArr " :key="goods.id" class="f-y-c">
						<view class="bs10 mr20" style="width: 104rpx;height: 104rpx;">
							<image class="wh" :src="goods.icon" mode=""></image>
						</view>
						<view class="f-g-1">
							<view class="l-h1">{{goods.name}}</view>
						</view>
						<view class="t-r">
							<view class="f-g-0 f32">${{itemTotal(goods)}}</view>
							<view class="ml10 f24 c9">{{goods.money}} X {{goods.num}}</view>
						</view>
					</view>
					<view class="w100" style="height: 20rpx;"></view>
					<!-- 包装费 -->
					<view class="f-x-bt mt20" v-if="info.boxMoney>0">
						<text>包装费</text>
						<text class="f32 c3">${{info.boxMoney}}</text>
					</view>
					<!-- 外送费 -->
					<view v-if="info.deliveryMode!=='10'" class="f-x-bt mt20">
						<text>外送费</text>
						<text class="f32 c3" v-if='info.deliveryPreferential>0'>${{info.deliveryMoney}}</text>
						<text class="f32" :class="info.deliveryPreferential>0?'t-d-l c9':'c3'">${{info.deliveryMoney}}</text>
					</view>
					<view class="t-r c3 mt20">
						<text class="f24">共{{info.num}}件商品</text>
						<text class="m02">小计</text>
						<text class="f34 c3">${{info.money}}</text>
					</view>
				</view>
				<view class="mt20 p20 bf">
					<view class="f24 c9 p253">付款说明:</view>
					<view class="p03 f22 c9">1、付款前务必和好友再次确认，避免是诈骗行为</view>
					<view class="p03 f22 c9">2、如果发生退款，钱将退还到你的微信账户里</view>
				</view>
				<view class="foot-btnc">
					<button form-type="submit" :disabled="loading" :loading="loading" class="foot-btn b-s-2 f30 wei"
						:style="{background:tColor,color:fontColor}">立即付款</button>
				</view>
			</view>
		</form>
	</view>
</template>

<script>
	import {
		mapActions,
		mapState
	} from 'vuex'
	import utils from '@/common/utils.js'
	import {
		utilMixins
	} from "@/common/util-mixins.js"
	export default {
		name: 'df',
		components: {

		},
		data() {
			return {
				stime: '',
				info: null, //订单詳情
				loading: false,
			}
		},
		async onLoad(options) {
			this.getSystem()
			this.util.setNT('求付款')
			this.getLoginInfo().then(async () => {
				await this.getConfig({
					name: 'payConfig',
					api: this.api.config,
					data: { ident: 'payConfig' }
				})
				await this.getConfig({
					name: 'orderSet',
					api: this.api.orderSet,
				})
				if (options.orderId) {
					let {
						data
					} = await this.util.request({
						'url': this.api.orderRep,
						data: {
							orderId: options.orderId,
						}
					})
					if (data) {
						if (data.state == 1) {
							this.info = data
							this.djs(data.createdAt)
						} else {
							uni.showModal({
								title: '提示',
								content: '订单已付款或异常',
								showCancel: false
							})
							setTimeout(() => {
								this.go('reLaunch', '/yb_o2ov2/index/index')
							}, 1000)
						}
					}

				}
			})
		},
		mixins: [utilMixins],
		computed: {
			...mapState({
				payConfig: state => state.config.payConfig,
				orderset: state => state.config.orderSet,
			}),
			pmoney() {
				return Number(this.info && this.info.money || 0).toFixed(2)
			},
		},
		methods: {
			...mapActions(['getConfig']),
			djs(time) {
				let now = this.dateToTime(),
					time2 = +time + this.orderset.closeTime * 60
				if (time2 > now) {
					this.stime = utils.countDownTime(time2 - now)
					this.dsq = setInterval(async () => {
						time2 -= 1
						if (time2 == now) {
							clearInterval(this.dsq)
							this.go({
								t: 4
							})
						}
						this.stime = utils.countDownTime(time2 - now)
					}, 1000)
				}
			},
			async formSubmit(e) {
				this.loading = true
				let payres = await this.util.request({
					'url': this.api.pay,
					method: 'POST',
					mask: 1,
					data: {
						orderId: this.info.id,
						orderType: 1,
						isFriends:1
					}
				})
				console.log('payres',payres)
				if (payres.code !== 2) {
					// #ifndef  H5
					uni.requestPayment({
						provider: this.provider,
						// #ifdef MP-WEIXIN
						timeStamp: payres.data.timeStamp,
						nonceStr: payres.data.nonceStr,
						package: payres.data.package,
						signType: payres.data.signType,
						paySign: payres.data.paySign,
						// #endif
						// #ifdef MP-WEIXIN
						success: (res) => {
							this.util.message('付款成功', 3)
							this.go('redirectTo', '/yb_o2ov2/index/index')
							// let params = `?orderId=${this.info.id}&orderType=1`
							// this.go('redirectTo', '/yb_o2ov2/order/order-info'+params)
						},
						fail: (err) => {
							console.log('fail:' + JSON.stringify(err))
							if (err.errMsg == 'requestPayment:fail cancel') {
								this.util.message('取消付款', 2)
							} else {
								uni.showModal({
									title: '提示',
									content: err.errMsg + err.err_desc,
									showCancel: false
								})
								this.loading = false;
							}
						},
						// #endif
						complete: (e) => {
							console.log("paymentcomplete", e)
						}
					});
					// #endif
				} else {
					// this.util.message('请检查付款配置', 3, 1500)
					this.loading = false;
				}
				console.log('formSubmit payres', payres)
			},
			itemTotal(v) {
				let itemTotal = +(v.vipMoney > 0 && +v.vipMoney < +v.money && v.vipMoney || v.money)
				return +(itemTotal * v.num).toFixed(2)
			},
		},
		onShareAppMessage() {
			this.go('reLaunch', '/yb_o2ov2/index/index')
			let p = `/yb_o2ov2/home/<USER>/payment-for-friend?orderId=${this.info.id}`
			return this.util.mpShare({
				t: this.payConfig.help,
				i: this.payConfig.icon && this.getSingleImg(this.payConfig.icon),
				p
			})
		},
	}
</script>
<style scoped lang="scss">
	.rwimg {
		width: 100%;
		height: 300rpx;
		bottom: 10rpx;
	}

	.dftxt {
		top: 80rpx;
		left: 75rpx;
		right: 75rpx;
	}

	.zlbd {
		border-radius: 10rpx 10rpx 0 0;
		margin-top: -30rpx;

		.paymoeny {
			display: flex;
			align-items: baseline;
		}

		.cf0 {
			color: #F41C0D;
		}
	}

	.djsc {
		line-height: 70rpx;
	}
</style>
