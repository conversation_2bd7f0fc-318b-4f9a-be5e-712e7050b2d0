<template>
	<view style="padding-bottom: 80rpx;border-top: 1px solid #F5f6f9;" :style="{background: bgColor}">
		<!-- 总评分 -->
		<view class="f-x-c-sa p2">
			<view class="f50 wei" style="color: #ff5454;">{{shopData.score}}</view>
			<view class="f-c-xc">
				<text class="f24 c9">{{$t('good.merchant_rating')}}</text>
				<u-rate :current="shopData.score" active-color="#ff5454"></u-rate>
			</view>
			<view class="f-c-xc">
				<text class="f24 c9">{{$t('good.taste')}}</text>
				<text class="wei">{{shopData.taste}}</text>
			</view>
			<view class="f-c-xc">
				<text class="f24 c9">{{$t('good.packages')}}</text>
				<text class="wei">{{shopData.pack}}</text>
			</view>
			<view class="f-c-xc">
				<text class="f24 c9">{{$t('good.Delivery_satisfaction')}}</text>
				<text class="wei">{{shopData.satisfaction}}%</text>
			</view>
		</view>
		<!-- 评价 -->
		<view class="mt20 pt20" :style="{background: bgColor}">
			<!-- 	<view class="f-c mla t-c f28 evaluate" style="width: 700rpx;">
				<view class="w50 p10 wei" :class="type==='take'?'takeActive':'takeInactive'" 
					@click="type='take'">外卖评价</view>
				<view class="w50 p10 wei" :class="type==='inStore'?'inStoreActive':'inStoreInactive'"
					@click="type='inStore'">到店评价</view>
			</view> -->
			<!-- 评价分类 -->
			<view class="categorys">
				<view v-for="(item,index) in category" :key="item.name" class="category"
					:style="chooseCategory === index?`color:${fontColor};background:${tColor}`:'color:#000;background:#f5f6f9'"
					@click="changeCategory(index)">
					<text>{{item.name}} {{item.num}}</text>
				</view>
			</view>
			<!-- 吸顶评价分类 -->
			<scroll-view scroll-x="true" style="transition:.1s ease;width: 100%;height: 95rpx;position: fixed;"
				:style="categoryFixed?fiexStyle:'top:-75rpx'" class="f-x-bt ws-n p02 pb20">
				<view v-for="(item,index) in category" :key="item.name" class="category dis-in"
					:style="chooseCategory === index?`color:${fontColor};background:${tColor}`:'color:#000;background:#f5f6f9'"
					@click="changeCategory(index)">
					<text>{{item.name}} {{item.num}}</text>
				</view>
			</scroll-view>
			<!-- 用户评价内容 -->
			<view class="p02">
				<view v-for="(user,index) in list" :key="index" class="pt40  mt40"
					style="border-top: 2rpx solid #f5f6f9;">
					<!-- 用户信息 -->
					<view class="f-x-bt">
						<view class="flex">
							<view class="bsf mr20" style="width: 80rpx;height: 80rpx;">
								<image class="wh" :src="user.member.portrait" mode=""></image>
							</view>
							<view class="f-col">
								<text class="f24 wei">{{user.member.nickname}}</text>
								<text class="f24 c9">{{user.createdAt}}</text>
							</view>
						</view>
						<view><text class="iconfont iconother f28 mr20 cd" style="margin-left: auto;"></text></view>
					</view>
					<!-- 打分 -->
					<view>
						<u-rate size="24" :current="user.star" active-color="#ff5454" disabled></u-rate>
						<text class="c9 f24">{{getStar(user.star)}}</text>
					</view>
					<!-- 内容 -->
					<view>
						<view class="f28">{{user.body}}</view>
						<!-- 用户发布图片 -->
						<view class="f-y-c f-w">
							<view v-for="image in user.media" :key="image" class="bs20 mr20 m20"
								style="width: 150rpx;height: 150rpx;" @click="checkImg(index)">
								<image class="wh mr10" :src="image" mode="aspectFill"></image>
							</view>
						</view>
						<!-- <view>
							<view v-if="user.media.length === 1">
								<image class="bs10" :src="user.media[0]" mode="aspectFill" style="width: 450rpx;height: 450rpx;"></image>
							</view>
							<view v-else-if="user.media.length === 2">
								<image class="mr10 bs10" :src="user.media[0]" mode="aspectFill" style="width: 350rpx;height: 350rpx;"></image>
								<image class="bs10" :src="user.media[1]" mode="aspectFill" style="width: 350rpx;height: 350rpx;"></image>
							</view>
							<view v-else-if="user.media.length > 2" class="flex">
								<image class="mr10 bs10" :src="user.media[0]" mode="aspectFill" style="width: 450rpx;height: 450rpx;"></image>
								<view class="f-col">
									<image class="mb10 bs10" :src="user.media[1]" mode="aspectFill" style="width: 220rpx;height: 220rpx;"></image>
									<view class="p-r">
										<image class="bs10" :src="user.media[2]" mode="aspectFill" style="width: 220rpx;height: 220rpx;"></image>
										<text v-if="user.media.length >= 4" @click="checkImg(index)" style="position: absolute;right: 0;bottom: 0;">更多</text>
									</view>
								</view>
							</view>
						</view> -->
					</view>
					<!-- 商店回复 -->
					<view v-if="user.reply" style="background: #f5f6f9;" class="bs10 f24 p1">
						<view class="c6">
							<text class="wei">商店回复:</text>
							<text>{{user.reply}}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		<!-- 空布局 -->
		<mescroll-empty v-if="list.length===0" :option="{icon:'/static/empty/7.png',tip:'~ 暂无评价 ~'}"></mescroll-empty>
		<u-loadmore v-else @loadmore="nextPage" :status="status" />
	</view>
</template>

<script>
	export default {
		props: {
			shopData: {
				type: Object,
				default: () => {}
			},
			storeId: {
				type: [String, Number],
				default: ''
			},
			scroll: {
				type: [String, Number],
				default: 0
			},
			tabNavHeight: { //tabNav高度单位rpx
				type: [String, Number],
				default: 80
			},
		},
		data() {
			return {
				status: 'loading',
				params: {
					page: 1,
					storeId: '',
					star: '' // 2:有图 3:好评 4:差评
				},
				list: [],
				init: false,
				bgColor: '#FFFFFF',
				type: 'take',
				chooseCategory: 0,
				category: [{
						name: this.$t('good.all'),
						num: '',
						star: ''
					},
					// {
					// 	name: this.$t('good.pictures'),
					// 	num: '',
					// 	star: '2'
					// },
					{
						name: this.$t('good.favorable_comment'),
						num: '',
						star: '1'
					}, {
						name: this.$t('good.bad'),
						num: '',
						star: '3'
					}
				],
				imgArr: [1, 2, 3, 4],
				categoryFixed: false,
			}
		},
		computed: {
			categoryShowTop() {
				return Math.floor(this.statusNavBarHeight + this.tabNavHeight / this.pxToRpxRate)
			},
			fiexStyle() {
				return `top:${this.categoryShowTop-1}px;z-index:2;background:${this.bgColor}`
			}
		},
		watch: {
			scroll(val) {
				this.changeScroll(val)
			},
			storeId: {
				handler(val) {
					this.params.storeId = val
				},
				immediate: true // 组件创建时立即执行一次
			}
		},
		methods: {
			changeCategory(index) {
				this.chooseCategory = index
				this.params.star = this.category[index].star
				this.refresh()
			},
			getStar(val) {
				switch (val) {
					case '1':
						return '非常差'
					case '2':
						return '差'
					case '3':
						return '一般'
					case '4':
						return '满意'
					case '5':
						return '非常满意'
				}
			},
			refresh() {
				this.params.page = 1
				this.list = []
				this.fetchData()
			},
			async fetchData() {
				let {
					data
				} = await this.util.request({
					url: this.api.storeEvaluate,
					method: 'GET',
					data: this.params
				})
				this.category.find(i => i.star === '').num = data.Store_evaluate.count || 0
				// this.category.find(i => i.star === '2').num = data.img || 0
				this.category.find(i => i.star === '1').num = data.evaluate.favorable || 0
				this.category.find(i => i.star === '3').num = data.evaluate.bad || 0
				this.list = this.list.concat(data.Store_evaluate.list)
				this.status = 'loadmore'
				if (data.Store_evaluate.list.length === 0) {
					this.status = 'nomore'
					return
				}
			},
			nextPage() {
				if (this.status === 'loading') {
					return
				}
				this.params.page++
				this.fetchData('nextPage')
			},
			changeScroll(e) {
				var top = e
				if (top >= this.FixedDot) {
					this.categoryFixed = true
				} else {
					this.categoryFixed = false
				}
			},
			getFixedDot() {
				if (this.init) return
				this.init = true
				let query = uni.createSelectorQuery().in(this)
				query.select('.categorys').boundingClientRect(res => {
					this.FixedDot = res.bottom - this.categoryShowTop
				}).exec()
			},
			checkImg(index) {
				console.log(this.list[index].media)
				uni.previewImage({
					urls: this.list[index].media,
					indicator: 'number'
				})
			},
		}
	}
</script>

<style scoped lang="scss">
	.evaluate {

		.takeActive {
			border-radius: 10rpx 0 0 10rpx;
			background: #F7CE62;
			border-left: 1px solid #F7CE62;
			border-top: 1px solid #F7CE62;
			border-bottom: 1px solid #F7CE62;
		}

		.takeInactive {
			border-radius: 10rpx 0 0 10rpx;
			background: #FFF;
			border-left: 1px solid #eee;
			border-top: 1px solid #eee;
			border-bottom: 1px solid #eee;
		}

		.inStoreActive {
			border-radius: 0 10rpx 10rpx 0;
			background: #F7CE62;
			border-right: 1px solid #F7CE62;
			border-top: 1px solid #F7CE62;
			border-bottom: 1px solid #F7CE62;
		}

		.inStoreInactive {
			border-radius: 0 10rpx 10rpx 0;
			background: #FFF;
			border-right: 1px solid #eee;
			border-top: 1px solid #eee;
			border-bottom: 1px solid #eee;
		}
	}

	.categorys {
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
		padding: 0 20rpx;
	}

	.category {
		font-size: 24rpx;
		margin-right: 10rpx;
		margin-top: 20rpx;
		padding: 10rpx 20rpx;
		border-radius: 10rpx;
	}
</style>