<template>
	<view v-show="isShow" class="p-f" style="top: 0;left: 0;z-index: 99999;">
		<view class="p-f" :style="{left:`${start.x}px`}" :animation="animationX">
			<view :animation="animationY" class="p-f bsf bf5f" :style="{top:`${cStart.y}px`}" style="width: 30rpx;height: 30rpx;"></view>
		</view>
	</view>
</template>

<script>
	var duration = 400;
	export default {
		props:{
			start:{
				type:Object,
				default:function(){
					return {
						x:0,
						y:0
					}
				}
			},
			end:{
				type:Object,
				default:function(){
					return {
						x:0,
						y:0
					}
				}
			},
			scroll: {
				type: [String, Number],
				default: 0
			},
		},
		data(){
			return{
				animationX:'',
				animationY:'',
				isShow:false
			}
		},
		computed: {
			cStart() {
				//因为scroll-view改为页面滚动所以元素定位点Y发生改变 需要额外处理
				var y = this.start.y>this.wHeight?this.start.y-this.scroll:this.start.y
				return {
					x:this.start.x,
					y:y
				}
			}
		},
		methods:{
			init(){
				let animationX = this.moveX(0,0,0)
				let animationY = this.moveY(0,0,0)
				this.animationX = animationX.export()
				this.animationY = animationY.export()
				this.isShow = true
				setTimeout(()=>{this.createdAnimation()},10)
				// 在起始位准备就绪后显示
				
			},
			createdAnimation(){
				let animationX = this.moveX(this.end.x,this.start.x)
				let animationY = this.moveY(this.end.y,this.cStart.y)
				this.animationX = animationX.export()
				this.animationY = animationY.export()
				// 执行完里面隐藏
				setTimeout(()=>{this.isShow = false},420)
			},
			moveX(e,s,d){
				var animation = uni.createAnimation({
					duration:d||duration,
					timingFunction:'linear'
				})
				animation.translateX(e-s).step()
				return animation
			},
			moveY(e,s,d){
				var animation = uni.createAnimation({
					duration:d||duration,
					timingFunction:'ease-in',
				})
				animation.translateY(e-s).step()
				return animation
			},
		}
	}
</script>

<style scoped>
</style>
