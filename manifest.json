{
    "name" : "Union",
    "appid" : "__UNI__C5001B7",
    "description" : "Union",
    "versionName" : "1.0.0",
    "versionCode" : "100",
    "transformPx" : false,
    "uniStatistics" : {
        "enable" : false //全局关闭  
    },
    "sassImplementationName" : "node-sass",
    /* 5+App特有相关 */
    "app-plus" : {
        "usingComponents" : true,
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        /* 模块配置 */
        "modules" : {},
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ]
            },
            /* ios打包配置 */
            "ios" : {},
            /* SDK配置 */
            "sdkConfigs" : {},
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            }
        }
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        "appid" : "wxde0606b085d14100",
        "setting" : {
            "urlCheck" : false,
            "es6" : true,
            "minified" : true,
            "postcss" : true
        },
        "optimization" : {
            "subPackages" : true
        },
        "usingComponents" : true,
        // "navigateToMiniProgramAppIdList" : [ "wx0d1797247cbbed6d", "wxeb490c6f9b154ef9" ],
        // "plugins" : {
        //     "live-player-plugin" : {
        //         "version" : "1.3.2",
        //         "provider" : "wx2b03c6e691cd7370"
        //     }
        // },
        // "tencentvideo" : {
        //     "version" : "1.3.16",
        //     "provider" : "wxa75efa648b60994b"
        // },
        // },
        // "live-player-plugin" : {
        //     "version" : "1.1.8",
        //     "provider" : "wx2b03c6e691cd7370"
        "permission" : {
            "scope.userLocation" : {
                "desc" : "你的位置信息将用于小程序位置接口的效果展示"
            }
        }
    },
    "mp-alipay" : {
        "usingComponents" : true
    },
    "mp-baidu" : {
        "usingComponents" : true,
        "appid" : "16668966",
        "setting" : {
            "urlCheck" : false
        }
    },
    "mp-toutiao" : {
        "usingComponents" : true,
        "setting" : {
            "es6" : true,
            "postcss" : false,
            "minified" : false,
            "urlCheck" : false
        },
        "appid" : "tt7f6609dbe04c80ae"
    },
    "h5" : {
        "router" : {
            "mode" : "hash",
            "base" : "/"
        },
        "title" : "Union",
        "favicon" : "/favicon.ico",
        "sdkConfigs" : {
            "maps" : {
                "qqmap" : {
                    "key" : "JITBZ-4Z4CF-RJGJM-NGQHT-KGFK5-UTFDE"
                },
                "google" : {
                    "key" : "AIzaSyBTUfOCvsKnvX2H9xr_bbYsho1PzJWQV6c"
                }
            }
        },
        "devServer" : {
            "https" : true,
            "disableHostCheck" : true,
            "host" : "127.0.0.1",
            "port" : 80
        },
        "optimization" : {
            "treeShaking" : {
                "enable" : true
            }
        },
        "unipush" : {
            "enable" : false
        },
        "uniStatistics" : {
            "enable" : false
        },
        "template" : "index.html"
    },
    "fallbackLocale" : "en",
    "locale" : "zh-Hant"
}
