<template>
	<view class="p2 bf5">
		<u-waterfall v-model="flowList" ref="uWaterfall">
			<template v-slot:left="{leftList}">
				<view v-for="(item, index) in leftList" :key="item.id" class="mt20">
					<!-- 这里编写您的内容，item为您传递给v-model的数组元素 -->
					<view class="bs20 bf" style="width: 340rpx;" @click="goToGoodsDetail(item)">
						<view class="w100" style="height: 360rpx;"><image class="wh" :src="item.icon" mode="aspectFill"></image></view>
						<view class="p02 pt20 pb10">
							<view class="wei">{{item.name}}</view>
							<view class="f24 c9 t-o-e"><text class="mr20">销量{{item.outSales}}</text> 配送费{{item.distribution.money}}
								<text class="t-o-e c9 f24 ml20">起送:${{item.distribution.startMoney}}</text>
							</view>
							<view style="border-bottom: 1px solid #f5f6f9;color: #FA3534;">
								<text class="f20">$</text>
								<text class="f32 ">{{item.price}}</text>
								<!-- <text class="t-d-l f20 cd ml10">$26.99</text> -->
								<!-- <text class="f20 ml10" style="border: 1px solid;border-radius: 10rpx;padding: 0 4rpx;">6.29折</text> -->
								</view>
							<view class="f-x-bt f24 mt10">
								<view class="t-o-e c9 f-y-c" style="width: 240rpx;">
									<view style="width: 45rpx;height: 45rpx;" class="bsf f-c mr10">
										<image class="wh" :src="item.storeIcon" mode="aspectFill"></image>
									</view>
									<view class="t-o-e">{{item.storeName}}</view>
								</view>
								<!-- <view style="color: #ff6560;">{{item.store.score}}分</view> -->
							</view>
						</view>
					</view>
				</view>
			</template>
			<template v-slot:right="{rightList}">
				<view v-for="(item, index) in rightList" :key="item.id" class="mt20">
					<!-- 这里编写您的内容，item为您传递给v-model的数组元素 -->
					<view class="bs20 bf" style="width: 340rpx;" @click="goToGoodsDetail(item)">
						<view class="w100" style="height: 360rpx;"><image class="wh" :src="item.icon" mode="aspectFill"></image></view>
						<view class="p02 pt20 pb10">
							<view class="wei">{{item.name}}</view>
							<view class="f24 c9 t-o-e"><text class="mr20">销量{{item.outSales}}</text> 
								<text v-if="item.distribution.money && item.distribution.money>0">配送费</text>
								<text v-if="item.distribution.money && item.distribution.money>0">{{item.distribution.money}}</text>
								<text v-else>免配送费</text>
								<text class="t-o-e c9 f24 ml20">起送:${{item.distribution.startMoney}}</text>
							</view>
							<view style="border-bottom: 1px solid #f5f6f9;color: #FA3534;">
								<text class="f20">$</text>
								<text class="f32">{{item.price}}</text>
								<!-- <text class="t-d-l f20 cd ml10">$26.99</text> -->
								<!-- <text class="f20 ml10" style="border: 1px solid;border-radius: 10rpx;padding: 0 4rpx;">6.29折</text> -->
								</view>
							<view class="f-x-bt f24 mt10">
								<view class="t-o-e c9 f-y-c" style="width: 240rpx;">
									<view style="width: 45rpx;height: 45rpx;" class="bsf f-c mr10">
										<image class="wh" :src="item.storeIcon" mode="aspectFill"></image>
									</view>
									<view class="t-o-e">{{item.storeName}}</view>
								</view>
								<!-- <view style="color: #ff6560;">{{item.store.score}}分</view> -->
								<!-- <view  class="t-o-e c9">起送:${{item.distribution.startMoney}}</view> -->
							</view>
						</view>
					</view>
				</view>
			</template>
		</u-waterfall>
		<u-loadmore @loadmore="nextPage" :status="status" />
	</view>
</template>

<script>
	export default {
		data(){
			return {
				params:{
					lat:'',
					lng:'',
					page:1,
					size:10,
				},
				status:'loading',
				flowList:[]
			}
		},
		created() {
		},
		methods:{
			async goToGoodsDetail(good){
				let params = {
					storeId:good.storeId,
					lat:this.latLng.latitude,
					lng:this.latLng.longitude
				}
				this.go('navigateTo',`/yb_o2ov2/home/<USER>
			},
			setApi(api,initParams){
				this.ApiUrl = api
				if(initParams){
					Object.assign(this.params,initParams)
				}
			},
			refresh(params){
				if(params !== undefined){
					Object.assign(this.params,params)
				}
				this.$refs.uWaterfall.clear();
				this.params.page = 1
				this.fetchData()
			},
			async fetchData(type,method='GET'){
				this.status='loading'
				let {data} = await this.util.request({
						'url': this.ApiUrl,
						method: method||'GET',
						data: this.params
					})
					if(type !=='nextPage' || this.params.page === 1){
						this.flowList = data
					}else{
						if(data.length === 0){
							this.status = 'nomore'
							return
						}
						this.flowList = this.flowList.concat(data)
					}
				this.status='loadmore'
			},
			nextPage(){
				if(this.status === 'loading'){return}
				this.params.page++
				this.fetchData('nextPage')
			},
		}
	}
</script>

<style>
</style>
