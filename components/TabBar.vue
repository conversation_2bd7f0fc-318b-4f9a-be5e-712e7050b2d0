<template>
	<view class="tab-bar-container">
		<!-- 主内容 -->
		<view class="tab-bar">
			<view class="tab-item" v-for="item in tabList" :key="item.value" @click="handleTabChange(item)">
				<view :class="['icon-container', { 'active': currentTab === item.value }]">
					<image class="tab-icon" :src="currentTab === item.value ? item.icon_active : item.icon"
						mode="aspectFit" />
				</view>
				<text class="tab-label" :class="{ 'active': currentTab === item.value }">
					{{ item.label }}
				</text>
			</view>
		</view>

		<!-- 安全区域占位 -->
		<view class="safe-area"></view>

		<!-- 整体占位 -->
		<view class="tab-placeholder"></view>
	</view>
</template>

<script>
	export default {
		name: 'CustomTabBar',
		props: {
			// 允许从外部传入当前选中项
			current: {
				type: Number,
				default: 0
			}
		},
		data() {
			return {
				currentTab: this.current,
				tabList: [{
						url: '/yb_o2ov2/index/index',
						icon: require('@/static/tab_list/home.png'),
						icon_active: require('@/static/tab_list/home-active.png'),
						label: this.$t('common.home'),
						value: 0
					},
					{
						url: '/yb_o2ov2/order/index',
						icon: require('@/static/tab_list/order.png'),
						icon_active: require('@/static/tab_list/order-active.png'),
						label: this.$t('common.order'),
						value: 1
					},
					{
						url: '/yb_o2ov2/my/index',
						icon: require('@/static/tab_list/my.png'),
						icon_active: require('@/static/tab_list/my-active.png'),
						label: this.$t('common.my'),
						value: 2
					}
				]
			}
		},
		watch: {
			// 监听外部传入的current变化
			current(newVal) {
				this.currentTab = newVal
			}
		},
		methods: {
			handleTabChange(item) {
				if (this.currentTab === item.value) return
				this.currentTab = item.value
				uni.redirectTo({
					url: item.url,
					fail: (err) => {
						console.error('切换tab失败:', err)
					}
				})
				// 向父组件通知变化
				this.$emit('change', item.value)
			}
		}
	}
</script>

<style lang="scss" scoped>
	$tab-height: 140rpx;
	$icon-size: 48rpx;
	$active-color: #FFC400;
	$animation-duration: 0.3s;

	.tab-bar-container {
		position: relative;
		z-index: 999;
	}

	.tab-bar {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		height: $tab-height;
		display: flex;
		justify-content: space-around;
		align-items: center;
		background-color: #FFFFFF;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
		padding-bottom: 20rpx;
	}

	.tab-item {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		height: 100%;
		position: relative;
	}

	.icon-container {
		width: $icon-size;
		height: $icon-size;
		margin-bottom: 8rpx;
		transition: all $animation-duration ease;

		&.active {
			animation: bounce $animation-duration ease;
		}
	}

	@keyframes bounce {
		0% {
			transform: translateY(0) scale(1);
		}

		50% {
			transform: translateY(-20rpx) scale(1.2);
		}

		100% {
			transform: translateY(-10rpx) scale(1.1);
		}
	}

	.tab-icon {
		width: 100%;
		height: 100%;
	}

	.tab-label {
		font-size: 22rpx;
		color: #333333;
		transition: color $animation-duration ease;

		&.active {
			color: $active-color;
			font-weight: 500;
		}
	}

	.safe-area {
		width: 100%;
		height: constant(safe-area-inset-bottom);
		height: env(safe-area-inset-bottom);
		background-color: #FFFFFF;
	}

	.tab-placeholder {
		width: 100%;
		height: calc(#{$tab-height} + constant(safe-area-inset-bottom));
		height: calc(#{$tab-height} + env(safe-area-inset-bottom));
	}
</style>