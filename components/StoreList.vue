<!-- 通用商店列表 不带分类 -->
<template>
	<view class="bf pt30">
		<view v-for="(item,index) in list" :key="item.id" class="card" @click="showIndex(item)">
			<view class="mr20 bs10 p-r" style="width: 130rpx;height: 130rpx;flex-shrink: 0;">
				<image class="wh" :src="item.icon"></image>
				<!-- 标签 -->
				<view v-if="item.labelFormat" class="p-a o-h"
					style="top:0;right: 0;height: 32rpx;border-radius: 0 0 0 10rpx;">
					<view class="h100 f20 wei" style="padding: 0 2rpx;margin-top: -2rpx;" :style="{
						 color:item.labelFormat.data.fontColor,
						 //背景色
						 background:getLabelBg(item)
						 }">
						{{item.labelFormat.name}}
					</view>
				</view>
			</view>
			<view class="card-right w100">
				<view class="f-y-c" style="width: 420rpx;">
					<!-- <text v-if="item.isNew === 1" class="f-s-0 label-title">新店</text> -->
					<text class="t-o-e dis-b f30 wei l-h1 h34">{{item.name}}</text>
					<!-- <text class="iconfont iconother f28 mr20 cd" style="margin-left: auto;transform: rotate(90deg);"></text> -->
				</view>
				<!-- 闭店标签 -->
				<block v-if="item.businessState.state!==1">
					<block v-if="item.businessState.state === 2">
						<view class="f-y-c f24 store-closed mt10">
							<view class="left">休息中</view>
							<view class="right">{{item.businessState.msg}}</view>
						</view>

					</block>
					<block v-else-if="item.businessState.state === 0">
						<view class="cf bs10 f24 t-c mt10"
							style="border: 1rpx solid #b1b1b1;background:#b1b1b1;width: 110rpx;">非營業時間
						</view>
					</block>
					<view class="f-y-c f24 store-closed2 mt10" v-if="item.businessState.state === 3">
						<view class="left">接受预定</view>
						<view class="right">{{item.businessState.msg}}</view>
					</view>
				</block>
				<view class="f24 f-y-c mt10">
					<view class="mr20 colorxx"><text class="f26 wei">{{item.score}}</text>分</view>
					<view class="colorxl mr20">销量{{item.outSales}}</view>
					<!-- <view class="c9">人均${{item.perCapita}}</view> -->
					<!-- 外送方 -->
					<!-- <block v-if="item.businessState.state ===1">
						<view class="bs10 brff f24" :style="{borderColor:tColor,color:tColor}"
							style="margin-left: auto;padding: 0 4rpx;">
							{{item.deliveryMode}}
						</view>
					</block> -->
					<view class="t-r colorxl" style="margin-left: auto;"><text class="mr10"
							v-if="item.deliveryTime && item.deliveryTime>=0"></text>{{item.distance}}</view>

				</view>
				<!-- 	<view class="f24 f-y-c colorxl f-bt">
					<view>
						<text class="mr20">起送${{item.distribution.startMoney}}</text>
						<text class="">{{`外送$${item.distribution.money}` || '免费外送'}}</text>
					</view> -->
				<!-- 外送方 -->
				<!-- 					<block v-if="item.businessState.state ===1 && item.deliveryMode!='商店外送'">
						<view class="t-r" v-if="styles.switchDelivery=='1'">
							<view class="bs10 brff f24 label-wh" :style="{borderColor:tColor,color:tColor}"
								style="margin-left: auto;">
								{{item.deliveryMode}}
							</view>
						</view>
					</block>
				</view> -->
				<view class="f24 mt10">
					<text class="tag" v-if="item.recommendation.length !== 0"
						v-for="(tip,tipIndex) in item.recommendation" :key="tipIndex">
						{{tip}}
					</text>
				</view>
				<view class="p-r f24 mt10 flex f-y-c f-w o-h pr40">
					<view class="p-a t-c" style="right: 0;top: 0;width: 40rpx;height: 40rpx;"
						@click.stop="expand=(expand===index?'':index)">
						<!-- <text class="iconfont icontriangle f30"
							style="color: #c0c4cc;"
							:style="expand===index?'transform: rotate(180deg);':'transform: rotate(0deg)'"></text> -->
					</view>
					<!-- 平台新客红包 -->
					<!-- <view class="label-coupon f-y-c" style="background-color: #FA3534;color: #fff;border: none;" v-if="item.discount.platformReduction && item.discount.platformReduction.name && item.discount.platformReduction.data && item.discount.platformReduction.data.money">
						<text>{{item.discount.platformReduction.name}}{{item.discount.platformReduction.data.money}} 元</text>
					</view> -->
					<view v-if="item.discount.reduce.length !== 0 || coupons(item).length!==0"
						class="p-r f24 f-y-c f-w o-h" :style="expand===index?'height:auto':''">
						<block v-if="item.discount.reduce && item.discount.reduce.data.type === '1'">
							<!-- 循环满减 -->
							<view class="label-coupon f-y-c" style="color: #FA3534;border: 1rpx solid #FA3534;">
								<text>每满{{item.discount.reduce.data.fullMoney}}减{{item.discount.reduce.data.money}}</text>
							</view>
						</block>
						<block v-else-if="item.discount.reduce && item.discount.reduce.data.type === '2'">
							<!-- 阶梯满减 -->
							<view class="label-coupon f-y-c" style="color: #FA3534;border: 1rpx solid #FA3534;">
								<block v-if="ids<3" v-for="(text,ids) in item.discount.reduce.data.moneyArr" :key='ids'>
									<text>{{`满${text.fullMoney}减${text.money}`}}</text>
									<text v-if="ids !== item.discount.reduce.data.moneyArr.length - 1 && ids < 2"
										class="m01 f18" style="color: #d28f50;">|</text>
								</block>
							</view>
						</block>
					</view>
					<view class="dis-in mr10 f20"
						v-if="item.discount.platformNewReduction && item.discount.platformNewReduction.money && item.discount.platformNewReduction.money>0">
						<!-- 平台新客立减 -->
						<view class="label-coupon f-y-c"
							style="color: #FA3534;border: 1rpx solid #FA3534;height: 32rpx;">
							<text>{{ $t('location.discountNew') }}{{item.discount.platformNewReduction.money}}</text>
						</view>
					</view>
					<view v-if="item.discount.newReduction && item.discount.newReduction>0">
						<!-- 新客 -->
						<view class="label-coupon f-y-c" style="color: #FA3534;border: 1rpx solid #FA3534;">
							<text>{{ $t('location.storeCustomer') }}{{item.discount.newReduction}}</text>
						</view>
					</view>
					<view v-if="item.discount.delivery && item.discount.delivery.length  !== 0"
						class="p-r f24 f-y-c f-w o-h" :style="expand===index?'height:auto':''">
						<!-- 外送费满减 -->
						<block v-if="item.discount.delivery && item.discount.delivery.type === '1'">
							<!-- 循环满减 -->
							<view class="label-coupon f-y-c" style="color: #FA3534;border: 1rpx solid #FA3534;">
								<text>{{ $t('location.deliveryFee') }}{{item.discount.delivery.money}}</text>
							</view>
						</block>
						<block v-else-if="item.discount.delivery && item.discount.delivery.type === '2'">
							<!-- 阶梯满减 -->
							<view class="label-coupon f-y-c" style="color: #FA3534;border: 1rpx solid #FA3534;">
								{{ $t('good.delivery_fee') }}
								<block v-if="ids<3" v-for="(text,ids) in item.discount.delivery.moneyArr" :key='ids'>
									<text>{{`满${text.fullMoney}减${text.money}`}}</text>
									<text v-if="ids !== item.discount.delivery.moneyArr.length - 1 && ids < 2"
										class="m01 f18" style="color: #d28f50;">|</text>
								</block>
							</view>
						</block>
					</view>
					<!-- 其他活动标签 -->
					<block>
						<view v-for="coupon in coupons(item)" :key="coupon.type" class="label-coupon f-y-c"
							:style="coupon.style">{{coupon.name}}
						</view>
					</block>
				</view>
				<view class="flex mt20">
					<view v-for="(citem, index) in item.recommendGoods" :key="citem.id" class="flex">
						<view class="bf" :class="index<2?'mr10':''" style="width: 165rpx;"
							@click="goToGoodsDetail(citem)">
							<view class="w100" style="height: 110rpx;">
								<image class="wh" style="border-radius: 10rpx;" :src="citem.icon" mode="aspectFill">
								</image>
							</view>
							<view class="pt10">
								<view class="f24 t-o-e">{{citem.name}}</view>
								<view style="color: #FA3534;">
									<text class="f20">$</text>
									<text class="f28 wei">{{citem.price}}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<mescroll-empty v-if="list.length === 0" imageWH="280"
			:option="{icon:'/static/empty/2.png', tip:$t('location.localText')}"></mescroll-empty>
		<u-loadmore v-else @loadmore="nextPage" :status="status" />
	</view>
</template>

<script>
	import {
		mapState
	} from 'vuex'
	export default {
		data() {
			return {
				status: 'loading',
				params: {
					keyList: [],
					lat: '',
					lng: '',
					page: 1,
					size: 10,
					distance: '',
					typePid: '',
					typeId: '',
					sort: '1'
				},
				list: [],
				expand: '', //激活的查看优惠券 index  
				ApiUrl: '',
			}
		},
		computed: {
			coupons() {
				return item => {
					let arr = []
					if (item.isCarry === 1) {
						arr.push({
							name: '支持自取',
							style: 'color: #b1b1b1;border: 1rpx solid #b1b1b1;',
							type: 'isCarry'
						})
					}
					return arr
				}
			},
			...mapState('dndc', ['regionId']),
			...mapState({
				storeSet: state => state.config.storeSet
			}),
		},
		created() {
			// this.params.lat = this.latLng.latitude
			// this.params.lng = this.latLng.longitude
			// this.fetchData()
		},
		methods: {
			getLabelBg(item) {
				// if(!item.labelFormat.data) return ''
				return item.labelFormat.data.model === 2 ?
					`linear-gradient(${item.labelFormat.data.gradientDeg}deg, ${item.labelFormat.data.color1}, ${item.labelFormat.data.color2})` :
					`${item.labelFormat.data.color1}`
			},
			setApi(api, initParams) {
				this.ApiUrl = api
				if (initParams) {
					Object.assign(this.params, initParams)
				}
			},
			refresh(params) {
				if (params !== undefined) {
					Object.assign(this.params, params)
				}
				this.params.page = 1
				this.fetchData()
			},
			async fetchData(type, method = 'GET') {
				this.status = 'loading'
				if (this.storeSet && this.storeSet.showType == 1) {
					this.params.regionId = this.regionId.id
				}
				let {
					data
				} = await this.util.request({
					'url': this.ApiUrl,
					method: method || 'GET',
					data: this.params
				})
				this.status = 'loadmore'
				if (data.length < this.params.size) {
					this.status = 'nomore'
				}
				if (type !== 'nextPage' || this.params.page === 1) {
					this.list = data
				} else if (data.length) {
					this.list = this.list.concat(data)
				}
			},
			nextPage() {
				if (this.status === 'loading' || this.status === 'nomore') {
					return
				}
				this.params.page++
				this.fetchData('nextPage')
			},
			showIndex(item) {
				if (item.showIndex == 1) {
					this.go('navigateTo', `/yb_o2ov2/home/<USER>
				} else {
					this.go('navigateTo', `/yb_o2ov2/home/<USER>
				}

			}
		},
	}
</script>

<style scoped lang="scss">
	.card {
		display: flex;
		width: 700rpx;
		background: #FFF;
		border-radius: 20rpx;
		margin: 0 auto;
		margin-bottom: 20rpx;
		padding: 20rpx 20rpx 10rpx 20rpx;
	}
	
	.label-title {
		font-size: 22rpx;
		font-weight: bold;
		color: #fff;
		border-radius: 10rpx;
		margin-right: 10rpx;
		padding: 2rpx 8rpx;
		background: #ea5a3b;
	}

	.tag {
		margin-right: 10rpx;
		padding: 2rpx 10rpx;
		border-radius: 10rpx;
		// color: #d28f50;
		// background: #fdf7e1;
		color: #F18932;
		background-color: #FEF8E0;
	}

	.label-coupon {
		font-size: 20rpx;
		height: 36rpx;
		// line-height: 38rpx;
		color: #c57771;
		border: 1rpx solid #f0dbdb;
		border-radius: 8rpx;
		padding: 0rpx 8rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}

	.label-wh {
		font-size: 20rpx;
		height: 36rpx;
		padding: 0rpx 8rpx;
	}

	.store-closed {
		view {
			padding: 0rpx 6rpx;
			border: 1rpx solid #b1b1b1;
		}

		.left {
			border-radius: 10rpx 0 0 10rpx;
			background: #b1b1b1;
			color: #fff;
		}

		.right {
			border-radius: 0 10rpx 10rpx 0;
			color: #b1b1b1;
		}
	}

	.store-closed2 {
		view {
			padding: 0rpx 6rpx;
			border: 1rpx solid #63CE9A;
		}

		.left {
			border-radius: 10rpx 0 0 10rpx;
			background: #63CE9A;
			color: #fff;
		}

		.right {
			border-radius: 0 10rpx 10rpx 0;
			color: #63CE9A;
		}
	}

	.brff {
		border: 1rpx solid #fff
	}

	.colorxx {
		color: #FF7F21;
	}

	.colorxl {
		color: #535353
	}

	.colorc9Font {
		color: #5B5B5B;
	}

	.h34 {
		height: 34rpx;
	}
</style>