<template>
	<u-parse  :html="nodes" />
</template>

<script>
	// import Parse from '@/components/uParse/src/wxParse.vue'
	export default {
		components: {
			// Parse,
		},
		props:{
			text:{
				type: String,
				default: ''
			}
		},
		data() {
			return {
			
			}
		},
		computed:{
			nodes() {
				return this.text.replace(/style=[\'\"]?([^\'\"]*)[\'\"]?/gi,'style="width:100%;height:auto;"')
			},
		},
		methods: {
		}
	}
</script>

<style>
</style>
