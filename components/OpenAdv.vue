<template>
	<view v-if="open&&styles.status==1" class="open" @touchmove.stop.prevent  @click="handlerGoUrl(styles)">
		<block v-if="styles.type==1">
			<view @click="open = false" class='posi-a f-c countC cf'>{{time}} 跳过</view>
			<view v-if="list.length>1">
				<u-swiper :height="wHeight*pxToRpxRate" :list="list" mode="round" borderRadius="0"></u-swiper>
			</view>
			<view class="wh" v-else>
				<image class="wh" :src="list[0].image" mode="aspectFill"></image>
			</view>
		</block>
		<block v-else>
			<u-mask :show="show">
				<view class="p-a p-a-c w100 f-c-c">
					<view class="tcgg">
						<u-swiper height="800" :list="list" mode="round" borderRadius="30" bgColor="#ffffff00" imgMode="aspectFill"></u-swiper>
						<view @click="open = false" class="mt60 f-c"><text class="iconfont iconcancelorder f50 cf"></text></view>
					</view>
				</view>
			</u-mask>
		</block>
	</view>
</template>

<script>
import {
	mapActions,
	mapState
} from 'vuex';
export default {
	name: 'open',
	props: {
		styles: {
			type: Object,
			default:()=>{}
		},
	},
	data() {
		return {
			open:false,
			show: false,
			time:''
		}
	},
	computed:{
		list(){
			return this.styles.imgUrl.map(item=>{
				return {
					image: item.img,
					title: item.url
				}
			})
		},
		length(){
			return this.list.length
		}
	},
	watch:{
		open(val){
			if(val&&this.styles.type==1){
				this.time = this.styles.countTime
				this.countTime()
			}
		}
	},
	methods: {
		countTime(){
			setTimeout(()=>{
				if(this.time-- === 1 ){
					this.open = false
				}else{
					this.countTime()
				}
			},1000)
		},
		handlerGoUrl(i){
			this.goUrl(i.imgUrl[0].url)
		}
	},
	created() {
		this.show = true
	}
}
</script>

<style lang="scss" scoped>
.open{
	width: 100vw;
	height: 100vh;
	position: fixed;
	top: 0;
	z-index: 110;
}
.countC {
	width: 140rpx;
	height: 55rpx;
	top: 80rpx;
	left: 30rpx;
	border-radius: 55rpx;
	background-color: rgba(147, 147, 147, 0.6);
}
.tcgg {
	width: 70%;
	}
</style>
