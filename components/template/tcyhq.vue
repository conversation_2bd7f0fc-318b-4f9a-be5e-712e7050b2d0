<template>
	<mg-modal :ismr="true" v-model="show" :width="type==1?'632rpx':type==2?'632rpx':'562rpx'" :z-index="3000">
		<block v-if="type==1 && co.couponList">
			<view class="tctop posi-r">
				<view class="tcimg3">
					<mg-img :src="co.icon||`/static/yhq/hbbg1.png`"></mg-img>
					<!-- <mg-img :src="co.icon||`${onImgurl}img/xrzxbg.png`"></mg-img> -->
				</view>
				<view class="posi-a tctopbd3 f-col">
					<scroll-view scroll-y class="f-1 tctopbd3b o-h">
						<block>
							<view class="jlic">
								<!-- <mg-coupon ptype="1" :color='color' cname='mb20' v-for="(item, index) in 3" :key="index"></mg-coupon> -->
								<block v-for="(v,i) in co.couponList" :key='i'>
									<mg-coupon ptype="1" :color='color' cname='mb20' :co='v'></mg-coupon>
								</block>
							</view>
						</block>
					</scroll-view>
				<!-- 	<button :disabled="loading" :style='{background:tColor}' class="lqbtn bs10 cf f28 f-c zfls4 wei bt"
					 @click="dllq">登录领取</button> -->
				</view>
			</view>
			<view @click="$emit('input', false)" class="tcbtm3 f-c"><text class="iconfont iconcancelorder cf f60"></text></view>
		</block>
		<block v-else-if="type==2 && co.couponList">
			<view class="tctop posi-r">
				<view class="tcimg3">
					<mg-img :src="co.icon||`/static/yhq/hbbg1.png`"></mg-img>
					<!-- <mg-img :src="co.icon||`${onImgurl}img/xrzxbg.png`"></mg-img> -->
				</view>
				<view class="posi-a tctopbd3 f-col">
					<scroll-view scroll-y class="f-1 tctopbd3b o-h">
						<block>
							<view class="jlic">
								<!-- <mg-coupon ptype="2" :color='color' cname='mb20' v-for="(item, index) in 3" :key="index"></mg-coupon> -->
								<block v-for="(v,i) in co.couponList" :key='i'>
									<mg-coupon ptype="2" :color='color' cname='mb20' :co='v'></mg-coupon>
								</block>
							</view>
						</block>
					</scroll-view>
				<!-- 	<button :disabled="loading" :style='{background:tColor}' class="lqbtn bs10 cf f28 f-c zfls4 wei bt"
					 @click="dllq">登录领取</button> -->
				</view>
			</view>
			<view @click="$emit('input', false)" class="tcbtm3 f-c"><text class="iconfont iconcancelorder cf f60"></text></view>
		</block>
		<block v-if="type==3">
			<view class="tctop posi-r" @click="dllq">
				<view class="tcimg3">
					<mg-img src="/static/yhq/hbbg2.png"></mg-img>
					<!-- <mg-img :src="co.icon||`${onImgurl}img/xrzxbg.png`"></mg-img> -->
				</view>
				<view class="posi-a tctopbd2 f-col">
					<view class="f-g-0 tctopbdt t-c">
						<view class="f70 wei f-c" v-if="co.money">
							<view class="ctype3c">{{co.money}}</view>
						</view>
					</view>
					<view class="type3font f-c f50 cf">新人红包 限时领取</view>
				</view>
<!-- 				<button :disabled="loading" :style='{background:tColor}' class="dlbtn bs10 cf f28 f-c zfls4 wei bt"
					 @click="dllq">登录领取</button> -->
			</view>
			<view @click="$emit('input', false)" class="tcbtm3 f-c"><text class="iconfont iconcancelorder cf f60"></text></view>
		</block>
	</mg-modal>
</template>

<script>
	import {
		mapState,
		mapActions
	} from 'vuex'
	import mgModal from '@/components/common/modal.vue'
	import mgImg from '@/components/common/mg-img.vue'
	import mgCoupon from '@/components/common/mg-coupon.vue'
	import utils from '@/common/utils.js'
	export default {
		name: 'searchBox',
		components: {
			mgModal,
			mgImg,
			mgCoupon,
		},
		props: {
			co: {
				type: Object,
				default: function() {
					return {}
				}
			},
			value: {
				type: Boolean,
				default: false
			},
			color: {
				type: String,
				default: ''
			},
			type: { //1商店优惠券2平台优惠券3新客专享弹窗
				type: String,
				default: '1'
			},
		},
		data() {
			return {
				list: [{
					name: '优惠券名称',
					type: 1,
					money: '8',
					fullMoney: '20',
					useExplain: '有效期至2020-11-85 12:56'
				}, {
					name: '优惠券名称',
					type: 2,
					discount: '8.9',
					fullMoney: '20',
					useExplain: '有效期至2020-11-85 12:56'
				}, {
					name: '优惠券名称',
					type: 1,
					money: '18',
					fullMoney: '',
					useExplain: '有效期至2020-11-85 12:56'
				}, ],
				yhqbg: '/static/yhq/xyhq.png',
				loading: false,
			}
		},
		computed: {
			show: {
				get() {
					return this.value;
				},
				set(newVal) {
					this.$emit("input", newVal)
				}
			},
		},
		methods: {
			async ljlq() {
				if (!await this.checkLogin()) return
				this.loading = true
				let res = await this.util.request({
					'url': this.api[this.type == 1 ? 'lqtcyhq' : 'lqfqb'],
					method: 'POST',
					mask: 1,
					data: {
						id: this.co.id,
					},
				})
				if (res) {
					this.util.message('领取成功', 1)
					utils.stfn(() => {
						this.loading = this.show = false
					}, 500)
				} else {
					this.loading = false
				}
			},
			async dllq() {
				if(!this.isLogin){
					this.go('navigateTo',`/yb_o2ov2/my/login`)
				}
				if (!await this.checkLogin()) 
				this.$emit('close')
				return
			},
		},
		async created() {}
	}
</script>

<style scoped lang="scss">
	.zfls4 {
		letter-spacing: 4rpx;
	}
	
	.ctype3c{
		font-size: 140rpx;
		color: #FF4948;
	}
	.type3font{
		margin-top: 80rpx;
	}

	.tctop {}

	.tcimg {
		width: 100%;
		height: 845rpx;
	}

	.tctopbd {
		width: 572rpx;
		height: 540rpx;
		top: 168rpx;
		left: 50%;
		transform: translateX(-50%);
		// background: #fff;
	}

	.tctopft {
		height: 83rpx;
		bottom: 36rpx;
		color: #fff;

		.lqbtn {
			height: 100%;
			width: 360rpx;
		}
	}

	.tctopbdt {
		padding: 20rpx 30rpx;

		.hx {
			margin: 0 15rpx;
			width: 24rpx;
			height: 1.5px;
			background: #fff;
		}
	}

	.tctopbdb {
		padding: 0rpx 20rpx;
		// overflow-y: scroll;
	}

	.tcbtm {
		margin-top: 48rpx;
	}

	/*2*/
	.tcimg2 {
		width: 100%;
		height: 845rpx;
	}

	.tctopbd2 {
		width: 572rpx;
		height: 540rpx;
		top: 58rpx;
		left: 50%;
		transform: translateX(-50%);
	}

	.tctopbd2b {
		padding: 0rpx 20rpx;
	}

	.tcbtm2 {
		margin-top: 30rpx;
	}

	/**/
	.tcimg3 {
		height: 738rpx;
	}

	.tctopbd3 {
		width: 100%;
		height: 492rpx;
		top: 100rpx;
		left: 50%;
		transform: translateX(-50%);
		padding-bottom: 30rpx;
	}

	.tctopbd3b {
		padding: 30rpx 40rpx 20rpx;
	}

	.tcbtm3 {
		margin-top: 30rpx;
	}

	.jlbt {
		color: #FCE82C;

		.xhx {
			width: 100%;
			margin: 0 20rpx;
			height: 0;
			border-top: 2px dashed #FCE82C;
		}
	}

	.jlic {
		margin: 10rpx 0 20rpx;

		.bgimg {
			height: 140rpx;
		}

		.coubd {
			top: 0;
			left: 0;
		}

		.coubdl {
			width: 205rpx;
		}
	}
	.dlbtn{
		
	}
</style>
