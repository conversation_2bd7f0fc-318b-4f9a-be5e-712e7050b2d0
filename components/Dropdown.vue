<template>
	<view>
		<u-dropdown ref="uDropdown" hideIndex="-1" @open="open" @close="close" :active-color="defaultStyles.activeColor"
			:inactive-color="defaultStyles.inactiveColor" :active-icon-color="defaultStyles.activeIconColor"
			:inactive-icon-color="defaultStyles.inactiveIconColor">
			<u-dropdown-item :title="sorting" :options="options" @change="sortzh"></u-dropdown-item>
			<u-dropdown-item :title="cName || '分类'">
				<view class="navbd f-row bf">
					<scroll-view scroll-y class="f-g-1" style="background:#f6f6f6">
						<view class="navbdi" v-for="(v,i) in typeArr" :key='i'
							:style="{background:i==paIdx?'#fff':'',color:i==paIdx?tColor:''}" @click="paIdxClick(i)">
							<text>{{v.name}}</text>
							<text v-if="v.num" class="ml20 f24"
								:style="{color:i==paIdx?tColor:'#999'}">({{v.num}})</text>
						</view>
					</scroll-view>
					<scroll-view scroll-y v-if="paIdx>-1&&typeArr[paIdx].child&&typeArr[paIdx].child.length>0"
						class="f-g-0 navbdr">
						<view class="navbdi" v-for="(v,i) in typeArr[paIdx].child" :key='i'
							:style="{color:i==saIdx?tColor:''}" @click="saIdxClick(i)">{{v.name}}</view>
					</scroll-view>
				</view>
			</u-dropdown-item>
			<u-dropdown-item :title="distancing">
				<view class="bf pb20" style="border-radius: 0 0 20rpx 20rpx;">
					<view class="f-y-c pl22" v-for="(filter,findex) in filters" :key="findex">
						<view v-for="(item,index) in filter.query" :key="item.id" class="dropdown-item-speed"
							@click="speedSelect(filter.multiple,findex,index,item)">
							<text
								:class="item.active?'dropdown-item-speed-active':'dropdown-item-speed-inactive'">{{item.title}}</text>
						</view>
					</view>
					<view class="mt20 f-x-c-sa t-c">
						<view class="dropdown-item-speed-btn c3 bf5" @click="clearQuery">重置</view>
						<view class="dropdown-item-speed-btn c0 f30" :style="{background:tColor,color:fontColor}"
							@click="distanceChage">完成
						</view>
					</view>
				</view>
			</u-dropdown-item>
			<u-dropdown-item title="全部筛选">
				<scroll-view scroll-y class="bf o-h p-r" :style="{height:`${wHeight * pxToRpxRate - 600}rpx`}"
					style="border-radius: 0 0 20rpx 20rpx;">
					<view v-for="(filters,index1) in all" :key="index1">
						<view class="f-x-bt mt20" style="padding: 0 22rpx;">{{filters.title}}</view>
						<view class="f-y-c f-w pl22 dropdown-item">
							<view v-for="(item,index2) in filters.query" :key="item.id" class="dropdown-item-speed"
								@click="allSelect(filters.multiple,index1,index2,item)">
								<text
									:class="item.active?'dropdown-item-speed-active':'dropdown-item-speed-inactive'">{{item.title}}</text>
							</view>
						</view>
					</view>
					<!-- <view style="height: 120rpx;"></view> -->
					<view class="mt20 f-x-c-sa t-c p-f bf pb20 pt10 w100" style="border-radius: 0 0 20rpx 20rpx;">
						<view class="dropdown-item-speed-btn c3 bf5" @click="clearQuery">重置</view>
						<view class="dropdown-item-speed-btn c0 f30" :style="{background:tColor,color:fontColor}"
							@click="confirm">完成
						</view>
					</view>
				</scroll-view>
			</u-dropdown-item>
		</u-dropdown>
		<!-- <scroll-view scroll-x="true" class="ws-n f-bt p03">
			<view v-for="(item,index) in subtitles" :key="index" class=" dis-in"
				:class="isFixed?'element-fixed':'element'"
				:style="subtitleIndex === index?`font-weight:blod;background:${tColor}`:`background:${styles.colorLabelBg}`"
				@click="selectSubtitleIndex(index)">
				<view class="f-c-c wh">
					<text :style="subtitleIndex === index?`color:${fontColor}`:`color:${styles.colorLabel}`">{{item.title}}</text>
				</view>
			</view>
		</scroll-view> -->
	</view>
</template>

<script>
	import utils from '@/common/utils.js'
	export default {
		props: {
			styles: {
				type: Object,
				default: () => {}
			},
			isFixed: {
				type: Boolean,
				default: false
			},
		},
		computed: {
			defaultStyles() {
				if (this.styles) {
					return { //首页装修样式
						activeColor: this.styles.colorWord !== '#343434' ? this.styles.colorWord : '#000',
						inactiveColor: this.styles.colorWord !== '#343434' ? this.styles.colorWord : '#606266',
						activeIconColor: this.styles.colorMarker !== '#CDCDCD' ? this.styles.colorMarker : '#000',
						inactiveIconColor: this.styles.colorMarker !== '#CDCDCD' ? this.styles.colorMarker : '#c0c4cc'
					}
				} else {
					return { //商店分类默认样式
						activeColor: '#000',
						inactiveColor: '#606266',
						activeIconColor: '#000',
						inactiveIconColor: '#c0c4cc'
					}
				}

			}
		},
		data() {
			return {
				distancing: "距离",
				distanceNum: null,
				sorting: "综合排序",
				subtitleIndex: '-1',
				subtitles: [
					// { title: '下单返红包', value: 'redbag' },
					// { title: '新客立减', value: 'newguest' },
					// { title: '满减', value: 'reduce' },
					// { title: ' 配送费满减', value: 'selfMention' },
				],
				options: [{
						label: '综合排序',
						value: 1,
					}, {
						label: '距离优先',
						value: 2,
					}, {
						label: '销量优先',
						value: 3,
					},
					{
						label: '评分优先',
						value: 4,
					}
				],
				query: [],
				filters: [
					// {
					// 	title: '速度',
					// 	multiple: false,
					// 	query: [
					// 		{ title: '30分钟内', id: 3, active: false }, { title: '40分钟内', id: 4, active: false },
					// 		{ title: '50分钟内', id: 5, active: false }, { title: '60分钟内', id: 6, active: false },
					// 	]
					// },
					{
						title: '距离',
						multiple: false,
						query: [{
								title: '1km内',
								id: 7,
								active: false,
								distance: 1000
							}, {
								title: '2km内',
								id: 8,
								active: false,
								distance: 2000
							},
							{
								title: '3km内',
								id: 9,
								active: false,
								distance: 3000
							}, {
								title: '5km内',
								id: 10,
								active: false,
								distance: 5000
							}
						]
					},
				],
				all: [{
						title: '自取',
						multiple: false,
						query: [{
							title: '到店自取',
							id: 2,
							active: false,
							ps: 'isCarry'
						}]
					},
					// {
					// 	title: '速度',
					// 	multiple: false,
					// 	query: [
					// 		{ title: '30分钟内', id: 3, active: false }, { title: '40分钟内', id: 4, active: false },
					// 		{ title: '50分钟内', id: 5, active: false }, { title: '60分钟内', id: 6, active: false },
					// 		{ title: '1km内', id: 7, active: false }, { title: '2km内', id: 8, active: false },
					// 		{ title: '3km内', id: 9, active: false }, { title: '5km内', id: 10, active: false }
					// 	]
					// },
					// {
					// 	title: '商店特色',
					// 	multiple: true,
					// 	query: [
					// 		{ title: '新商店', id: 11, active: false }, 
					// 		{ title: '0元起送', id: 12, active: false },
					// 		{ title: '跨天预订', id: 13, active: false }, 
					// 		// { title: '开发票', id: 14, active: false },
					// 		// { title: '优选商店', id: 15, active: false }, 
					// 		// { title: '赠准时宝', id: 16, active: false },
					// 		// { title: '放心吃', id: 17, active: false }, 
					// 		// { title: '极速退款', id: 18, active: false },
					// 		// { title: '公益商店', id: 19, active: false }
					// 	]
					// },
					{
						title: '品质',
						multiple: false,
						query: [{
								title: '5星好评',
								id: 20,
								active: false,
								score: 5
							}, {
								title: '4星以上',
								id: 21,
								active: false,
								score: 4
							},
							{
								title: '3星以上',
								id: 22,
								active: false,
								score: 3
							},
							// { title: '品牌商店', id: 23, active: false },
						]
					},
					// {
					// 	title: '优惠活动',
					// 	multiple: true,
					// 	query: [
					// 		// { title: '神券膨胀', id: 24, active: false }, 
					// 		// { title: '津贴优惠', id: 25, active: false },
					// 		// { title: '会员商店', id: 26, active: false }, 
					// 		// { title: '优惠商店', id: 27, active: false },
					// 		{ title: '满减优惠', id: 28, active: false ,yhhd:'reduce'}, 
					// 		{ title: '进店领券', id: 29, active: false ,yhhd:'inStoreCoupon'},
					// 		// { title: '折扣商品', id: 30, active: false }, 
					// 		// { title: '首单立减', id: 31, active: false },
					// 		// { title: '满赠活动', id: 32, active: false }, 
					// 		// { title: '第二份半价', id: 33, active: false },
					// 		// { title: '满返代金券', id: 34, active: false },
					// 		{ title: '减配送费', id: 35, active: false ,yhhd:'selfMention'},
					// 		// { title: '买赠活动', id: 36, active: false },
					// 		// { title: '提前下单优惠', id: 37, active: false },
					// 		// { title: '天天神券', id: 38, active: false }, 
					// 		{ title: '门店新客立减', id: 39, active: false,yhhd:'newguest'}
					// 	]
					// }
				],
				sort: 1,
				distance: 1,
				typeArr: [],
				snavs: [],
				aIdx: -1,
				navIdx: 0,
				paIdx: 0,
				saIdx: 0,
				cId: '',
				cName: '',
				pId: '',
				pName: '',
				yhhd: [],
				ps: [],
				score: '',
			}
		},
		mounted() {
			this.$refs.uDropdown.highlight(0);
			this.TypeList()
		},
		methods: {
			parentRefresh(params) {
				let storelist
				if (this.$parent.$refs.storelist) storelist = this.$parent.$refs.storelist[0] //首页
				if (this.$parent.$parent.$refs.storelist) storelist = this.$parent.$parent.$refs.storelist //銷售類別
				storelist.refresh(params)
			},
			selectSubtitleIndex: utils.debounceImmediate(function(index) {
				this.subtitleIndex === index ? this.subtitleIndex = '' : this.subtitleIndex = index
				let value = this.subtitles[this.subtitleIndex] ? this.subtitles[this.subtitleIndex].value : ''
				this.parentRefresh({
					keyList: [value]
				})
			}, 300),
			// 
			speedSelect(multiple, index1, index2, item) {
				//console.log(11,multiple ,item.distance)
				if (item.distance) {
					this.distance = item.distance
				}
				if (multiple) { //多选
					this.filters[index1].query[index2].active = !this.filters[index1].query[index2].active
				} else {
					if (this.filters[index1].query[index2].active) {
						this.filters[index1].query[index2].active = false
						return
					}
					this.filters[index1].query.forEach(item => {
						item.active = false
					})
					this.filters[index1].query[index2].active = true
				}
				this.distanceNum = index2
			},
			allSelect(multiple, index1, index2, item) {
				// this.subtitleIndex === index ? this.subtitleIndex = '' : this.subtitleIndex = index
				// let value = this.subtitles[this.subtitleIndex] ? this.subtitles[this.subtitleIndex].value : ''
				// this.parentRefresh({ keyList: [value] })
				// console.log(123,multiple,item)
				this.score = ''
				if (item.score) {
					this.score = item.score
				}
				//console.log('ps',item.ps)
				if (item.ps) {
					this.ps = []
					if (!this.ps.includes(item.ps)) {
						this.ps.push(item.ps);
					}
				}
				if (multiple) { //多选
					this.all[index1].query[index2].active = !this.all[index1].query[index2].active
					this.yhhd = []
					if (this.all[index1].query && item.yhhd) {
						for (let i = 0; i < this.all[index1].query.length; i++) {
							if (this.all[index1].query[i].active == true) {
								if (!this.yhhd.includes(this.all[index1].query[i].yhhd)) { //includes 检测数组是否有某个值
									this.yhhd.push(this.all[index1].query[i].yhhd);
								}
								console.log(11, this.yhhd)
							}
						}
					}
				} else {
					if (this.all[index1].query[index2].active) {
						this.all[index1].query[index2].active = false
						return
					}
					this.all[index1].query.forEach(item => {
						item.active = false
					})
					this.all[index1].query[index2].active = true
				}
			},
			open(index) {
				// 展开某个下来菜单时，先关闭原来的其他菜单的高亮
				// 同时内部会自动给当前展开项进行高亮
				try {
					this.$refs.uDropdown.highlight();
				} catch (e) {
					console.log("页面初始化中")
				}
			},
			close(index) {
				// 关闭的时候，给当前项加上高亮
				// 当然，您也可以通过监听dropdown-item的@change事件进行处理
				try {
					this.$refs.uDropdown.highlight(index);
				} catch (e) {
					console.log("页面初始化中")
				}
			},
			sortzh(value) {
				// sort 1综合 2距离 3销量 4评分
				try {
					this.parentRefresh({
						sort: value
					})
				} catch (e) {
					this.util.message('网络异常', 3)
				}
				// 点击相应的展现相应的排序名称，sorting默认为
				this.options.forEach(item => item.value == value ? this.sorting = item.label : "")
			},
			distanceChage() {
				this.parentRefresh({
					distance: this.distance
				})
				this.$refs.uDropdown.close()
				this.filters[0].query.forEach((item, index) => {
					if (this.distanceNum == index) {
						this.distancing = item.title
					} else if (this.distanceNum == null) {
						this.distancing = "距离"
					}
				})
			},
			confirm() {
				if (this.ps) {
					this.yhhd = this.yhhd.concat(this.ps)
				}
				this.parentRefresh({
					sort: this.sort,
					keyList: this.yhhd,
					score: this.score
				})
				this.$refs.uDropdown.close()
			},
			clearQuery() {
				this.all.forEach(item => {
					item.query.forEach(item => {
						item.active = false
					})
				})

				this.filters.forEach(item => {
					item.query.forEach(item => {
						item.active = false
					})
				})
				this.distance = ''
				this.score = ''
				this.yhhd = []
				this.ps = []
				this.distanceNum = null
			},
			async TypeList() {
				let arr = [{
					name: '全部分类',
					child: [],
				}]
				let {
					data
				} = await this.util.request({
					'url': this.api.carlist,
					data: {
						// item: 1,
					},
				})
				this.typeArr = arr.concat(data)
				for (let i = 0; i < this.typeArr.length; i++) {
					this.typeArr[i].child.unshift({
						name: '全部分类',
						pid: "",
					})
				}
			},
			paIdxClick(i) {
				let pitem = this.typeArr[i]
				this.paIdx = i
				this.saIdx = -1
				this.cId = pitem.id || ''
				this.cName = pitem.name
				this.parentRefresh({
					catId: this.cId
				})
				// console.log(222,pitem,this.cId,this.cName)
			},
			saIdxClick(i) {
				let sitem = this.typeArr[this.paIdx].child[i]
				this.saIdx = i
				this.pId = sitem.id
				if (sitem.id) {
					this.cName = sitem.name
				}
				if (!this.pId) {
					this.pId = this.cId
				}
				// console.log(333,sitem,this.pId,this.pName)
				this.parentRefresh({
					catId: this.pId
				})
				this.$refs.uDropdown.close()
			},
		}
	}
</script>

<style scoped lang="scss">
	.pl22 {
		padding-left: 22rpx;
	}

	.element {
		background: #FFF;
		font-size: 24rpx;
		border-radius: 10rpx;
		padding: 10rpx 36rpx;
		margin-right: 10rpx;
		text-align: center;
	}

	.element-fixed {
		background: #F5f5f5;
		font-size: 24rpx;
		border-radius: 10rpx;
		padding: 10rpx 36rpx;
		margin-right: 10rpx;
		text-align: center;
	}

	.dropdown-item {
		max-height: 160rpx;
		overflow: hidden;
	}

	.dropdown-item-speed {
		display: flex;
		flex-wrap: wrap;
		width: 160rpx;
		font-size: 22rpx;
		text-align: center;
		margin-top: 20rpx;
		margin-right: 22rpx;

		.dropdown-item-speed-active {
			display: block;
			width: 100%;
			padding: 10rpx 8rpx;
			border-radius: 10rpx;
			font-weight: bold;
			color: #ff964d;
			background: #fefbf2;
			border: 1px solid #ff964d;
		}

		.dropdown-item-speed-inactive {
			display: block;
			width: 100%;
			padding: 10rpx 8rpx;
			border-radius: 10rpx;
			background: #f5f5f5;
			border: 1px solid #f5f5f5;
		}
	}

	.dropdown-item-speed-btn {
		width: 300rpx;
		height: 80rpx;
		line-height: 80rpx;
		border-radius: 10rpx;
	}

	.navbd {
		max-height: 800rpx;
		border-radius: 0 0 25rpx 25rpx;
		overflow: hidden;
	}

	.navbdi {
		padding: 20rpx 32rpx;
	}

	.navbdr {
		width: 69%;
	}

	.navbd2 {
		max-height: 885rpx;
		border-radius: 0 0 25rpx 25rpx;
		overflow: hidden;
	}
</style>
