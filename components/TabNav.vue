<template>
	<view class="u-tabs"  :style="{
			background: bgColor
		}">
		<scroll-view scroll-x class="u-scroll-view" :scroll-left="scrollLeft" scroll-with-animation>
			<view class="u-tabs-scroll-box" :class="{'u-tabs-scorll-flex': !isScroll}">
				<view class="u-tabs-item"  :style="[tabItemStyle(index)]" @tap="emit(index)"
				 v-for="(item, index) in getTabs" :key="index" :class="[preId + index]">
					<!-- <u-badge :count="item[count] || item['count'] || 0" :offset="offset" size="mini"></u-badge> -->
					<view>{{ item[name] || item['name']}}</view>
					<view v-show="index === current" class="u-scroll-bar" :style="{background:activeColor}"></view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	const preId = 'UEl_';
	const { windowWidth } = uni.getSystemInfoSync();
	export default {
		props:{
			// 读取传入的数组对象的属性(tab名称)
			name: {
				type: String,
				default: 'name'
			},
			// 导航菜单是否需要滚动，如只有2或者3个的时候，就不需要滚动了，此时使用flex平分tab的宽度
			isScroll: {
				type: Boolean,
				default: true
			},
			// 选中项的文字颜色
			activeTextColor: {
				type: String,
				default: '#000000'
			},
			// 未选中项的文字颜色
			inactiveTextColor: {
				type: String,
				default: '#000000'
			},
			// 选中项的主题颜色
			activeColor: {
				type: String,
				default: '#2979ff'
			},
			// 未选中项的颜色
			inactiveColor: {
				type: String,
				default: '#303133'
			},
			//需循环的标签列表
			list: {
				type: Array,
				default () {
					return [];
				}
			},
			// 当前活动tab的索引
			currentIndex: {
				type: [Number, String],
				default: 0
			},
			// 导航栏的高度和行高，单位rpx
			height: {
				type: [Number, String],
				default: 80
			},
			// 字体大小，单位rpx
			fontSize: {
				type: [Number, String],
				default: 32
			},
			// 活动tab字体是否加粗
			bold: {
				type: Boolean,
				default: true
			},
			// 单个tab的左或右内边距（各占一半），单位rpx
			gutter: {
				type: [Number, String],
				default: 40
			},
			// 导航栏的背景颜色
			bgColor: {
				type: String,
				default: '#ffffff'
			},
			// 当前活动tab item的样式
			activeItemStyle: {
				type: Object,
				default() {
					return {}
				}
			},
			// 是否显示底部的滑块
			showBar: {
				type: Boolean,
				default: true
			},
		},
		data(){
			return {
				tabsInfo:[],
				preId,
				windowWidth:0,
				scrollLeft:0,
				current:0,
			}
		},
		watch: {
			currentIndex:{
				handler(n){
					this.current = n
				},
				immediate:true
			},
			current(n, o) {
				this.change(n);
			},
			list() {
				this.$nextTick(() => {
					this.init();
				})
			}
		},
		computed:{
			// 获取当前活跃的current值
			getCurrent() {
				const current = Number(this.current);
				// 判断是否超出边界
				if (current > this.getTabs.length - 1) {
					return this.getTabs.length - 1;
				}
				if (current < 0) return 0;
				return current;
			},
			getTabs() {
				return this.list;
			},
			// tab的样式
			tabItemStyle() {
				return (index) => {
					let style = {
						height: this.height + 'rpx',
						lineHeight: this.height + 'rpx',
						padding: `0 ${this.gutter / 2}rpx`,
						color: this.getCurrent === index ? this.activeTextColor : this.inactiveTextColor,
						fontSize: this.fontSize + 'rpx',
						fontWeight: (index == this.getCurrent && this.bold) ? 'bold' : 'normal'
					};
					if(index == this.getCurrent) {
						// 给选中的tab item添加外部自定义的样式
						style = Object.assign(style, this.activeItemStyle);
					}
					return style;
				}
			},
		},
		mounted() {
			this.init();
		},
		methods:{
			emit(index) {
				this.current = index
				this.$emit('change', index);
			},
			change() {
				this.setScrollViewToCenter();
			},
			async init() {
				await this.getTabsInfo();
				this.getQuery(() => {
					this.setScrollViewToCenter();
				});
			},
			getTabsInfo() {
				return new Promise((resolve, reject) => {
					let view = uni.createSelectorQuery().in(this);
					for (let i = 0; i < this.list.length; i++) {
						view.select('.' + preId + i).boundingClientRect();
					}
					view.exec(res => {
						const arr = [];
						for (let i = 0; i < res.length; i++) {
							// 给每个tab添加其文字颜色属性
							res[i].color = this.inactiveColor;
							// 当前tab直接赋予activeColor
							if (i == this.getCurrent) res[i].color = this.activeColor;
							arr.push(res[i]);
						}
						this.tabsInfo = arr;
						resolve();
					});
				})
			},
			// 把活动tab移动到屏幕中心点
			setScrollViewToCenter() {
				let tab;
				tab = this.tabsInfo[this.current];
				if (tab) {
					let tabCenter = tab.left + tab.width / 2;
					let fatherWidth;
					// 活动tab移动到中心时，以屏幕还是tab组件为宽度为基准
					if (this.autoCenterMode === 'window') {
						fatherWidth = windowWidth;
					} else {
						fatherWidth = this.componentsWidth;
					}
					this.scrollLeft = tabCenter - fatherWidth / 2;
				}
			},
			getQuery(cb) {
				try {
					let view = uni.createSelectorQuery().in(this).select('.u-tabs');
					view.fields({
							size: true
						},
						data => {
							if (data) {
								this.componentsWidth = data.width;
								if (cb && typeof cb === 'function') cb(data);
							} else {
								this.getQuery(cb);
							}
						}
					).exec();
				} catch (e) {
					this.componentsWidth = windowWidth;
				}
			},
		}
	}
</script>

<style scoped lang="scss">
	.u-tabs {
		width: 100%;
		transition-property: background-color, color;
	}
	.u-scroll-view {
		width: 100%;
		white-space: nowrap;
		position: relative;
		z-index: 2;
	}
	.u-tabs-scroll-box {
		// position: relative;
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		z-index: 3;
	}
	.u-tabs-scorll-flex {
		display: flex;
		flex-direction: row;
		justify-content: center;
	}
	
	.u-tabs-scorll-flex .u-tabs-item {
		flex-grow: 1;
		text-align: center;
		position: relative;
		z-index: 4;
	}
	.u-scroll-bar {
		position: relative;
		width: 40rpx;
		height: 8rpx;
		bottom: 8rpx;
		left: 50%;
		transform: translateX(-50%);
		border-radius: 100px;
		z-index: 99;
	}
</style>
