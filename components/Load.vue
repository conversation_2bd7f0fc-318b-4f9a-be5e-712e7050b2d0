<template>
	<view v-if="show" class="h100v w100 p-f bf" style="top: 0;z-index: 9999999;">
		<view class="p-a p-c">
			<view class="animation_opactiy" style="width: 150rpx;height: 150rpx;">
				<image class="wh" :src="system.loadIcon" mode=""></image>
			</view>
			<view class="w100 t-c mt20 c6 f24">努力加载中...</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
	} from 'vuex'
	export default {
		props:{
			show:{
				type:Boolean,
				default:true
			}
		}
	}
</script>

<style scoped lang="scss">
	.animation_opactiy {
			animation: backOpacity 1s ease-in-out infinite;
	}
	@keyframes backOpacity {
		0% {
			opacity: 0
		}

		25% {
			opacity: .2
		}

		50% {
			opacity: 0.5
		}

		75% {
			opacity: .2
		}

		100% {
			opacity: 0
		}
	}
</style>
