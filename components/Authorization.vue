<template>
	<view >
		<u-popup v-model="showPopup" height="1100" border-radius="30" mode="center" :closeable="false" :mask-close-able="false">
			<view class="p-r p02 h100" style="width: 650rpx;">
				<view class="posi-s t-c wei f28 bf pt20" style="top: 0;">溫馨</view>
				<scroll-view scroll-y="true" style="height: 850rpx;" >
					<view style="white-space: pre-wrap;padding: 30rpx;">
					<view class="wei">亲，感谢您对盒马一直以来的信任!</view>
					<view class="wei m30">我们依据最新的监管要求更新了<text style="color: #00BFFF;">《盒马隐私权政策》</text>(点击了解更新后的详细内容)，特向您说明如下:</view>
1、为向您提供交易相关基本功能，我们会收集、使用必要的信息;
2、为保障您的账号与使用安全，您需要授权我们读取本机识别码，您有权拒绝或取消授权，取消后不会影响您使用我们提供的其他服务;
3、为在盒马首页向您展示所在位置附近门店内容，您需要授权我们获取您的位置权限，您有权拒绝或取消授权，取消后不会影响您使用我们提供的其他服务;
4我们会采取业界先进的安全措施保护您的信息安全;
5、未经您同意，我们不会从第三方处获取、共享或向其提供您的信息;
6、您可以查询、更正、删除您的个人信息，我们也提供账户注销的渠道。
1、为向您提供交易相关基本功能，我们会收集、使用必要的信息;
2、为保障您的账号与使用安全，您需要授权我们读取本机识别码，您有权拒绝或取消授权，取消后不会影响您使用我们提供的其他服务;
3、为在盒马首页向您展示所在位置附近门店内容，您需要授权我们获取您的位置权限，您有权拒绝或取消授权，取消后不会影响您使用我们提供的其他服务;
4我们会采取业界先进的安全措施保护您的信息安全;
5、未经您同意，我们不会从第三方处获取、共享或向其提供您的信息;
6、您可以查询、更正、删除您的个人信息，我们也提供账户注销的渠道。
1、为向您提供交易相关基本功能，我们会收集、使用必要的信息;
2、为保障您的账号与使用安全，您需要授权我们读取本机识别码，您有权拒绝或取消授权，取消后不会影响您使用我们提供的其他服务;
3、为在盒马首页向您展示所在位置附近门店内容，您需要授权我们获取您的位置权限，您有权拒绝或取消授权，取消后不会影响您使用我们提供的其他服务;
4我们会采取业界先进的安全措施保护您的信息安全;
5、未经您同意，我们不会从第三方处获取、共享或向其提供您的信息;
6、您可以查询、更正、删除您的个人信息，我们也提供账户注销的渠道。
1、为向您提供交易相关基本功能，我们会收集、使用必要的信息;
2、为保障您的账号与使用安全，您需要授权我们读取本机识别码，您有权拒绝或取消授权，取消后不会影响您使用我们提供的其他服务;
3、为在盒马首页向您展示所在位置附近门店内容，您需要授权我们获取您的位置权限，您有权拒绝或取消授权，取消后不会影响您使用我们提供的其他服务;
4我们会采取业界先进的安全措施保护您的信息安全;
5、未经您同意，我们不会从第三方处获取、共享或向其提供您的信息;
6、您可以查询、更正、删除您的个人信息，我们也提供账户注销的渠道。
1、为向您提供交易相关基本功能，我们会收集、使用必要的信息;
2、为保障您的账号与使用安全，您需要授权我们读取本机识别码，您有权拒绝或取消授权，取消后不会影响您使用我们提供的其他服务;
3、为在盒马首页向您展示所在位置附近门店内容，您需要授权我们获取您的位置权限，您有权拒绝或取消授权，取消后不会影响您使用我们提供的其他服务;
4我们会采取业界先进的安全措施保护您的信息安全;
5、未经您同意，我们不会从第三方处获取、共享或向其提供您的信息;
6、您可以查询、更正、删除您的个人信息，我们也提供账户注销的渠道。
					</view>
				</scroll-view>
				<view class="p-a bf w100 p02" style="bottom: 20rpx;left: 0;">
					<view @click="UserAuthorization" class="t-c cf w100 btn" style="background: #007AFF;">同意</view>
					<view @click="reject" class="t-c mt20 w100 btn" style="background: #ddd;">不同意</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	export default {
		props:{
			show: {
				type: Boolean,
				default: false
			},
		},
		data(){
			return {
				showPopup:this.show
			}
		},
		watch:{
			show(val){
				this.showPopup = val
			}
		},
		methods:{
			UserAuthorization(){
				uni.setStorageSync('UserAuthorization','已同意')
				this.showPopup = false
			},
			reject(){
				this.showPopup = false
			},
		}
	}
</script>

<style scoped lang="scss">
	.btn{
		height: 70rpx;
		line-height: 70rpx;
		font-size: 28rpx;
		border-radius: 35rpx;
	}
</style>
