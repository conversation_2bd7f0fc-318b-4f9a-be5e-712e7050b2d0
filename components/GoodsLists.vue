<!-- 通用商品列表 不带分类 -->
<template>
	<view class="p02" style="padding-bottom: 220rpx;">
		<view v-for="(good,index) in list" :key="good.id" class="card"
		@click="go('navigateTo',`/yb_o2ov2/home/<USER>">
			<!-- 商品左边图片 -->
			<view class="p-r mr20">
				<view class="bs10" style="width: 140rpx;height: 140rpx;">
					<image :src="good.icon" class="wh" mode="aspectFit"></image>
				</view>
				<!-- 招牌 -->
				<view v-if="good.hotsaleType === '1'" class="p-a bs10 f20 wei"
					style="top: -10rpx;right: -10rpx;padding: 0 6rpx;" :style="{background:tColor,color:fontColor}">招牌
				</view>
			</view>
			<!-- 右侧介绍 -->
			<view class="f-g-1">
				<view style="min-height: 150rpx;">
					<view class="wei f30">{{good.name}}</view>
					<view v-if="false"><text class="f22 bs10"
							style="padding: 2rpx 8rpx;background: #FEF8E3;color: #EF8D38;">网友点名推荐</text></view>
					<!-- 商品简介 -->
					<view v-if="good.body" class="f24 c9" style="width: 320rpx;">{{good.body.length>25?good.body.substring(0,25)+'...':good.body}}</view>
					<view class="f24 c9 mt10">月售 {{good.salesNum||0}} 库存 {{good.stock}}</view>
					<view class="f30">
						<text class="crb">${{good.money}}</text>
						<text class="c9 f26 ml10 t-d-l">{{good.price}}</text>
					</view>
				</view>
			
			</view>
		</view>
		<mescroll-empty v-if="list.length === 0"
			:option="{icon:'/static/empty/4.png',tip:'~ 暂无特价商品哦 ~'}"></mescroll-empty>
		<u-loadmore  v-if="list.length!==0" @loadmore="nextPage" :status="status" />
	</view>
</template>

<script>
	export default {
		props:{
			list:{
				type:Array,
				default:()=>[]
			},
			storeId:{
				type:[Number,String],
				default:''
			}
		},
		data() {
			return{
				params:{
					page: 1,
					size: 10,
				},
				status:'loading',
				// list:[],
				current:0,
				selectIndex:'',
			}
		},
		watch:{
			list(val){console.log(val)}
		},
		created() {
		
		},
		methods:{
			tabsChange(index){
				this.params.state = index + 1
				this.refresh()
			},
			refresh(){
				this.params.page = 1
				this.fetchData()
			},
			async fetchData(type){
				this.params.lat=this.latLng.latitude
				this.params.lng=this.latLng.longitude
				let {data} =  await this.util.request({
					url: this.api.wdyhq,
					method: 'GET',
					data:this.params
				})
				if(type !=='nextPage'){
						this.list = data
					}else{
						if(data.length === 0){
							this.status = 'nomore'
							return
						}
						if(this.params.page === 1){
							this.list = data
						}else{
							this.list = this.list.concat(data)
						}
					}
				this.status='loadmore'
			},
			nextPage(){
				if(this.status === 'loading'){return}
				this.params.page++
				this.fetchData('nextPage')
			},
		}
	}
</script>

<style scoped lang="scss">
	.card {
		display: flex;
		width: 700rpx;
		background: #FFF;
		border-radius: 20rpx;
		margin: 0 auto;
		margin-bottom: 20rpx;
		padding: 20rpx;
	}

	.label-title {
		font-size: 22rpx;
		font-weight: bold;
		color: #fff;
		border-radius: 10rpx;
		margin-right: 10rpx;
		padding: 2rpx 8rpx;
		background: #ea5a3b;
	}

	.tag {
		margin-right: 10rpx;
		padding: 2rpx 4rpx;
		border-radius: 10rpx;
		color: #d28f50;
		background: #fdf7e1;
	}

	.label-coupon {
		height: 40rpx;
		line-height: 38rpx;
		color: #c57771;
		border: 1rpx solid #f0dbdb;
		border-radius: 8rpx;
		padding: 0rpx 8rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}

	.red {
		color: #ea5a3b;
		border-color: #f0dbdb;
	}

	.brown {
		color: #bba278;
		border-color: #d0c8b3;
	}
</style>
