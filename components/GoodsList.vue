<!-- 通用商品列表 不带分类 -->
<template>
	<view class="pt20">
		<view v-for="(good,index) in list" :key="good.id" class="card"
		@click="go('navigateTo',`/yb_o2ov2/home/<USER>">
			<!-- 商品左边图片 -->
			<view class="p-r mr20">
				<view class="bs10" style="width: 140rpx;height: 140rpx;">
					<image :src="good.icon" class="wh" mode="aspectFit"></image>
				</view>
				<!-- 招牌 -->
				<view v-if="good.hotsaleType === '1'" class="p-a bs10 f20 wei"
					style="top: -10rpx;right: -10rpx;padding: 0 6rpx;" :style="{background:tColor,color:fontColor}">招牌
				</view>
			</view>
			<!-- 右侧介绍 -->
			<view class="f-g-1">
				<view style="min-height: 150rpx;">
					<view class="wei f30">{{good.name}}</view>
					<view v-if="false"><text class="f22 bs10"
							style="padding: 2rpx 8rpx;background: #FEF8E3;color: #EF8D38;">网友点名推荐</text></view>
					<!-- 	<view v-else class="f-y-c f-w">
						<text class="goods-label">1人份</text>
						<text class="goods-label">绿茶</text>
						<text class="goods-label">花茶</text>
					</view> -->
					<!-- 商品简介 -->
					<view v-if="good.body" class="f24 c9" style="width: 320rpx;">{{good.body.length>25?good.body.substring(0,25)+'...':good.body}}</view>
					<view class="f24 c9 mt10">月售 {{good.salesNum||0}} 库存 {{good.stock}}</view>
					<!-- 特殊要求 -->
					<!-- 	<view class="discount">
						<text class="text">7折</text>
						<text>限一份</text>
					</view> -->
					<!-- 单点不送 -->
					<view class="single" v-if="good.aloneType === '1'">
						<text class="text">单点不送</text>
					</view>
				</view>
			
			</view>
		</view>
		<mescroll-empty v-if="list.length === 0" imageWH="280"
			:option="{icon:'/static/empty/4.png',tip:'~ 该商店没有相关的商品哦 ~'}"></mescroll-empty>
		<u-loadmore v-else @loadmore="nextPage" :status="status" />
	</view>
</template>

<script>
	export default {
		props:{
			list:{
				type:Array,
				default:()=>[]
			},
			storeId:{
				type:[Number,String],
				default:''
			}
		},
		data() {
			return {
			
			}
		},
		watch:{
			list(val){console.log(val)}
		},
		created() {
		
		},
		methods: {
			
		},
	}
</script>

<style scoped lang="scss">
	.card {
		display: flex;
		width: 700rpx;
		background: #FFF;
		border-radius: 20rpx;
		margin: 0 auto;
		margin-bottom: 20rpx;
		padding: 20rpx;
	}

	.label-title {
		font-size: 22rpx;
		font-weight: bold;
		color: #fff;
		border-radius: 10rpx;
		margin-right: 10rpx;
		padding: 2rpx 8rpx;
		background: #ea5a3b;
	}

	.tag {
		margin-right: 10rpx;
		padding: 2rpx 4rpx;
		border-radius: 10rpx;
		color: #d28f50;
		background: #fdf7e1;
	}

	.label-coupon {
		height: 40rpx;
		line-height: 38rpx;
		color: #c57771;
		border: 1rpx solid #f0dbdb;
		border-radius: 8rpx;
		padding: 0rpx 8rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}

	.red {
		color: #ea5a3b;
		border-color: #f0dbdb;
	}

	.brown {
		color: #bba278;
		border-color: #d0c8b3;
	}
</style>
