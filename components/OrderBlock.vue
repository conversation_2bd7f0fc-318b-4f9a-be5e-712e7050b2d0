<template>
	<view class="order-card">
		<view class="order-card-tags-type">{{showOrderType}}</view>
		<view class="flex" @click="handleStoreClick">
			<view class="mr20" style="margin-top: 8rpx;">
				<image class="bs10" :src="orderInfo.storeIcon" mode="" style="width: 74rpx;height: 74rpx;"></image>
			</view>
			<view class="f-g-1">
				<view class="f-x-bt">
					<view class="f-y-c">
						<view class="f28 wei  t-o-e" style="max-width: 240rpx;" @click.stop="handleStoreClick">{{orderInfo.storeName}}</view>
						<view class="iconfont iconinto f24 c9"></view>
					</view>
					<view class="">
						<text class="c9" :style="{color:orderInfo.state === '1'?tColor:''}"
							@click.stop="handleStoreClick">{{getState(orderInfo)}}{{stime}}</text>
					</view>

				</view>
				<view class="f24 c6" style="margin-top: 7rpx;">
					<text>{{orderTime(orderInfo.createdAt)}}
						<text v-if="orderInfo.takeNo!==null">({{$t('order.order_number')}}:#{{orderInfo.takeNo}})</text>
					</text>
				</view>
			</view>
		</view>
		<view @click="handleOrderInfoClick">
			<block v-if="orderInfo.goodsArr.length ===1">
				<view class="f-y-c mt10">
					<view class="mr20 bs10" style="width: 160rpx;height: 120rpx;">
						<image class="wh" :src="orderInfo.goodsArr[0].icon" mode="aspectFill"></image>
					</view>
					<text class="f-g-1 f30 c6">{{orderInfo.goodsArr[0].name}}</text>
					<view class="t-c mb20">
						<view class="wei f28">${{orderInfo.money}}</view>
						<view class="c6 f24">{{$t('order.order_total', {num: 1})}}</view>
					</view>
				</view>
			</block>
			<block v-else>
				<view class="f-x-bt mt10 p-r">
					<view class="flex o-x-s f26">
						<view v-for="element in orderInfo.goodsArr" :key="element.id" class="f-c-c mr20">
							<view class="bs10" style="width: 180rpx; height: 120rpx;">
								<image class="wh" :src="element.icon" mode="aspectFill"></image>
							</view>
							<view class="mt10 c6 t-c t-o-e" style="width: 180rpx;">{{element.name}}</view>
						</view>
						<!-- 占位元素 -->
						<view style="width: 120rpx;height: 1px;flex-shrink: 0;"></view>
					</view>
					<view class="t-c p-a h100 f-c-xc pl20" style="right: 0;background: rgba(255,255,255,.9);">
						<view class="wei f28">${{orderInfo.money}}</view>
						<view class="c6 f24">{{$t('order.order_total', {num: orderInfo.num})}}</view>
					</view>
				</view>
			</block>
		</view>
		<view v-if="orderInfo.deliveryMode === '10'&&orderInfo.state === '4'" class="t-r mb20">
			<text>{{$t('order.order_pickup_code')}}：</text>
			<text :style="{color:tColor}">{{orderInfo.selfCode}}</text>
		</view>
		<view class="f-x-e f22 wei mt40">
			<!-- <block v-if="['1','2'].includes(orderInfo.state)||(orderInfo.deliveryMode === '10'&&orderInfo.state === '4')"> -->
			<!-- 取消订单 -->
			<!-- <view @click="$emit('operation',{data:orderInfo,type:'cancelOrder'})" class="bs10 mr20 p02" style="border: 1px solid #666;height: 60rpx;line-height: 60rpx;">取消订单</view> -->
			<!-- 			</block> -->
			<block v-if="['5','6','7'].includes(orderInfo.state)">
				<!-- 再来一单 -->
				<view @click="handleAgainOrder" class="bs10 p02"
					style="border: 1px solid #666;height: 60rpx;line-height: 60rpx;">{{$t('order.order_again')}}</view>
				<block v-if="orderInfo.state === '5'&&currency&&currency.orderEvaluate === '1'">
					<!-- 评价 -->
					<view @click="handleEvaluate" class="bs10 p02 ml20"
						style="height: 60rpx;line-height: 60rpx;" :style="{background:tColor,color:fontColor}">
						<text v-if="currency.commentIntegralOpen!=1">{{$t('order.order_evaluate')}}</text>
						<text v-else>{{$t('order.order_evaluate_reward', {num: currency.commentType.commentIntegral})}}</text>
					</view>
				</block>
			</block>
			
			<block v-else-if="orderInfo.state === '1'">
				<!-- 立即付款 -->
				<view @click="handlePayTo" class="bs10 p02"
					style="height: 60rpx;line-height: 60rpx;" :style="{background:tColor,color:fontColor}">{{$t('order.order_pay_now')}}</view>
			</block>
			<block v-else-if="orderInfo.state === '4'&&orderInfo.deliveryMode === '10'">
				<!-- 確認取貨 -->
				<view @click="handleConfirmReceipt" class="bs10 p02"
					style="height: 60rpx;line-height: 60rpx;" :style="{background:tColor,color:fontColor}">{{$t('order.order_confirm_receipt')}}</view>
			</block>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
	} from 'vuex'
	import utils from '@/common/utils.js'
	export default {
		props: {
			orderInfo: {
				type: Object,
				default: () => {}
			},
			orderType: {
				type: String,
				default: 'store'
			}
		},
		data() {
			return {
				//1尚未付款,2已付款,3已接单,4外送中/待取货,5已收货,6已评价,7已取消,8.拒单，9申请退款,10已退款，11退款拒绝
				// stateArr: ['', '等待付款', '已付款', '已接单', '外送中', '已完成', '已评价', '已取消', '取消订单', '待商店处理', '退款成功',
				// '退款失敗'], //外卖state对应
				// ztstateArr: ['', '等待付款', '已付款', '已接单', '待取货', '已完成', '已评价', '已取消', '取消订单', '待商店处理', '退款成功',
				// '退款失敗'], //自取state对应
				stateArr:this.$t('order.state'),
				ztstateArr:this.$t('order.ztState'),
				stime: ''
			}
		},
		computed: {
			...mapState({
				orderSet: state => state.config.orderSet,
				currency: state => state.config.currency,
			}),
			showOrderType (){
				if(this.orderInfo.deliveryMode === '10' && this.orderInfo.isOut === '2' && this.orderInfo.tableId === '0'){
					return this.$t('order.self_pickup')
				}else if(this.orderInfo.deliveryMode === '1' && this.orderInfo.tableId === '0'){
					return this.$t('order.delivery')
				}else if(this.orderInfo){
					return this.$t('order.internal_use')
				}
			}
		},
		watch: {
			orderInfo(val) {
				if (val.state != 1 || !this.orderSet || this.orderSet.autoClose != 1) {
					this.clearCountDown();
				}
			},
		},
		created() {
			this.countDownTime(this.orderInfo.createdAt)
		},
		beforeDestroy() {
			this.clearCountDown();
		},
		methods: {
			clearCountDown() {
				if (this.setIntervalTime) {
					clearInterval(this.setIntervalTime);
					this.setIntervalTime = null;
					this.stime = '';
				}
			},
			handleStoreClick() {
				if(this.orderType === 'supplier'){
					return this.go('navigateTo', `/yb_o2ov2/my/supplier/store?storeId=${this.orderInfo.storeId}`)
				}else{
					return this.go('navigateTo', `/yb_o2ov2/home/<USER>
				}
			},
			handleOrderInfoClick() {
				if (this.orderType === 'supplier') {
					this.go('navigateTo', `/yb_o2ov2/my/supplier/order-info?orderId=${this.orderInfo.id}`);
					return;
				}
				this.go('navigateTo', `/yb_o2ov2/order/order-info?orderId=${this.orderInfo.id}`);
			},
			handleAgainOrder() {
				if(this.orderType === 'supplier'){
					return this.go('navigateTo', `/yb_o2ov2/my/supplier/store?storeId=${this.orderInfo.storeId}`)
				}else{
					return this.go('navigateTo', `/yb_o2ov2/home/<USER>
				}
			},
			handleEvaluate() {
				this.$emit('operation', {data: this.orderInfo, type: 'evaluate'});
			},
			handlePayTo() {
				this.$emit('operation', {data: this.orderInfo, type: 'payTo'});
			},
			handleConfirmReceipt() {
				this.$emit('operation', {data: this.orderInfo, type: 'confirmReceipt'});
			},
			getState(orderInfo) {
				if (orderInfo.deliveryMode != 10) {
					return this.stateArr[+orderInfo.state]
				} else {
					return this.ztstateArr[+orderInfo.state]
				}
			},
			orderTime(time) {
				let result = utils.timeToDate(time)
				return result
			},
			countDownTime(time) {
				//，剩余 13:55
				if (this.orderInfo.state != 1 || !this.orderSet || this.orderSet.autoClose != 1) return
				let now = utils.dateToTime(),
					time2 = +time + this.orderSet.closeTime * 60
				if (time2 > now) {
					let a = utils.countDownTime(time2 - now)
					this.stime = `，${this.$t('order.remaining')} ${a[2]}:${a[3]}`
					this.clearCountDown();
					this.setIntervalTime = setInterval(async () => {
						time2 -= 1
						if (time2 == now) {
							this.clearCountDown();
							return;
						}
						let arr = utils.countDownTime(time2 - now)
						this.stime = `，${this.$t('order.remaining')} ${arr[2]}:${arr[3]}`
					}, 1000)
				}
			},

		}
	}
</script>

<style lang="scss" scoped>
	.order-card {
		.order-card-tags-type{ 
			position: absolute;
			top: 4px;
			left: 0;
			font-size: 12px;
			// background: linear-gradient(90deg, #FDF8E2 0%, #FEE8B7 100%);
			background: #FEE8B7;
			color: #fff;
			padding-right: 10px;
			padding-left: 4px;
			border-radius: 0 10px 10px 0;
			z-index: 1;
		}
		width: 700rpx;
		margin: 0 auto;
		padding: 20rpx 20rpx 40rpx 20rpx;
		border-radius: 24rpx;
		background: #FFF;
		margin-bottom: 20rpx;
		position: relative;
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
		overflow: hidden;
	}
</style>
