<template>
	<view class="bfooter f-c-c c9 f26 t-c" v-if="system && system.support">
		<view @click="false&&util.makeTel(system.tel)" class="f-c">
			<view v-if="system.support.support" class="">{{system.support.support}}</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapActions
	} from 'vuex'
	export default {
		name: 'footc',
		props: {
			bottom: {
				type: String,
				default: ''
			},
		},
		data() {
			return {

			}
		},
	}
</script>

<style scoped lang='scss'>
	.bfooter {
		padding: 20rpx 20rpx 20rpx;
	}

	.footimg {
		width: 40rpx;
		height: 40rpx;
	}
</style>
