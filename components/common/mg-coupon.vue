<template>
	<view v-if="ptype==1" class="cou5 p025 p-r bf bs20 f24 f-y-c mb20" :class="[cname]">
		<view class="cou5l mr20 p-r bs20 " v-if="co.store">
			<mg-img :src='co.store.icon'></mg-img>
		</view>
		<view class="f-1 f-c-xc mr10">
			<view class="f30 c0 t-o-e wei">{{co.name}}</view>
			<view class="c6">{{fullName}}</view>
			<view class="c6 t-o-e">{{co.useExplain}}</view>
		</view>
		<view class="f-c-c cou5r">
			<view class="wh f-c-c c3 lh1 mb10">
				<view class="c5lq wei">
					<block>
						<text>$</text><text class="f56 ml5">{{Number(co.money)}}</text>
					</block>
				</view>
			</view>
			<button @click.stop="go('navigateTo',`/yb_o2ov2/home/<USER>" class="cf cou5lq bs30 f-c clearbtn" :disabled="disabled">去使用</button>
			<!-- <image v-else class="p-a cou5ylq" src="/static/yhq/lyq.png"></image> -->
		</view>
	</view>
	<view v-else-if="ptype==2" class="posi-r cou1 o-h bf c6 f22 cou2" :class="[cname]">
		<view class="bf cout f-row posi-f">
			<view class="f-g-0 coutl f-c-c mr30 brd">
				<view class="wei cf5">
					<block>
						<text>$</text><text class="f50 ml5">{{Number(co.money)}}</text>
					</block>
				</view>
				<view class="t-o-e cf5">{{fullName}}</view>
			</view>
			<view class="f-g-1 f-y-ad">
				<view class="wei f30 t-o-e c3">{{co.name}}</view>
				<view class="t-o-e c9">
					<text v-if="co.storeType==1">所有商户通用</text>
					<text v-else-if="co.storeType==2">指定商户可用</text>
					<text v-else-if="co.storeType==3 && co.storeTypeArr">
						<text v-for="item in co.storeTypeArr">仅{{item.name}}可用,</text>
					</text>
				</view>
				<view class="f-x-bt c9">
					<view class="t-o-e">{{co.useExplain}}</view>
				</view>
			</view>
			<view class="f24 t-c posi-a r0 b0" style="width: 100rpx;height:100rpx;">
				<image class="wh" src="/static/img_home/receive-after.png" mode=""></image>
			</view>
		</view>
	</view>
	

</template>

<script>
	import {
		mapState,
		mapActions
	} from 'vuex'
	import {
		utilMixins
	} from "@/common/util-mixins.js"
	import mgImg from '@/components/common/mg-img.vue'
	export default {
		name: 'searchBox',
		components: {
			mgImg,
		},
		props: {
			co: {
				type: Object,
				default: function() {
					return {}
				}
			},
			gttype: { 
				type: String,
				default: ''
			},
			ptype: { //样式类型 1商店优惠券,2平台优惠券
				type: String,
				default: '1'
			},
			ttype: { //文字类型 1领券中心2帳戶券
				type: String,
				default: '1'
			},
			cname: {
				type: String,
				default: ''
			},
			u: {
				type: String,
				default: 'px'
			},
			color: {
				type: String,
				default: ''
			}
		},
		data() {
			return {
				active: false,
				disabled: false,
			}
		},
		mixins: [utilMixins],
		computed: {
			typeName() {
				let t, c = ''
				switch (this.ptype) {
					case '1':
						if (this.ttype == 1) {
							t = '领 取'
							c = 'linear-gradient(90deg, #ff3a48, #ff3a48)'
						} else {
							t = this.co.useState == 1 ? '去使用' : this.co.useState == 2 ? '已使用' : '已失效'
							c = this.co.useState == 1 ? 'linear-gradient(90deg, #ff3a48, #ff3a48)' : '' //让btnClick方法判断不可点击
						}
						break;
					default:
						break;
				}
				return {
					t,
					c
				}
			},
			fullName() {
				return this.co.fullMoney > 0 ? `满${Number(this.co.fullMoney)}元可用` : '无门槛'
			},
			qbbl() {
				return this.co.saleNum / this.co.totalNum >= 1 ? 1 : this.co.saleNum / this.co.totalNum
			},
			qlx() {
				if(this.co.type == 1){
					return '代'
				}else if(this.co.type == 2){
					return '折'
				}else if(this.co.type == 3){
					return '兑'
				}
				// return this.co.type == 1 ? this.co.type == 2 ? '代' : '折' : '兑'
			},
			iswdq() {
				return this.ttype == 2 && this.co.receiveType == 13
			},
		},
		methods: {
			useTypeName(t) {
				let n
				switch (+t) {
					case 1:
						n = '全门店通用'
						break;
					case 2:
						n = '指定商户可用'
						break;
					case 6:
						n = '指定商户可用'
						break;
					case 7:
						n = '指定商品可用'
						break;
					case 8:
						n = '指定分类可用'
						break;
					default:
						break;
				}
				return n
			},
			// goTo() {
			// 	let url = '/yb_o2ov2/my/coupon/coupon-dl?id=' + (this.co.couponId ? this.co.couponId : this.co.id)
			// 	if (this.ptype == 1 && this.ttype == 2 || this.ptype == 4) {
			// 		url = '/yb_o2ov2/my/coupon/coupon-dl?islq=1&receiveId=' + this.co.id
			// 	} else if (this.ptype == 8) {
			// 		url = '/yb_o2ov2/order/coupon-bag/qbxq?id=' + this.co.id
			// 	}
			// 	this.go({
			// 		t: 1,
			// 		url,
			// 	})
			// },
			// btnClick() {
			// 	if (this.typeName.c || this.ptype == 5) {
			// 		if (this.ptype == 1 && this.ttype == 1 || this.ptype == 5) {
			// 			// console.log(this.co)
			// 			this.disabled = true
			// 			setTimeout(() => {
			// 				this.disabled = false
			// 			}, 1000)
			// 		}
			// 		this.$emit('btntap')
			// 	}
			// },
		}
	}
</script>

<style scoped lang="scss">
	.bf8 {
		background: #ff3a48;
	}

	.cf5 {
		color: #ff3a48;
	}

	.cou1 {
		border-radius: 20rpx;
	}

	.cout {
		position: relative;
		padding: 30rpx 20rpx;
	}

	.coutl {
		min-width: 150rpx;
		// height: 150rpx;
	}
	.brd{
		border-right: 1px dashed #ddd;
	}

	.coub {
		position: relative;
		background: #fff;

		&::after {
			content: '';
			position: absolute;
			border-top: 1px dashed #EBEBED;
			top: 0;
			right: 25rpx;
			left: 25rpx;
		}
	}

	.coubtn {
		min-width: 100rpx;
		height: 48rpx;
		color: #fff;
		background: #DDD;
		border-radius: 15rpx;
	}

	.coubt,
	.coubjt {
		transition: all 0.3s ease-in-out;
	}

	.active {
		opacity: 0.4;
	}

	.jtactive {
		transform: rotate(-180deg);
	}

	.coubb {
		height: 0;
		transition: all 0.3s ease-in-out;

		// 如果不给固定高度值没有过渡效果
		&.cactive {
			height: auto;
		}
	}

	.coubbv {
		opacity: 0;
		transform: translateY(-50%);
		transition: 0.3s ease-in-out;

		&.show {
			opacity: 1;
			-webkit-transform: translateY(0);
			transform: translateY(0);
		}
	}

	.ylq {
		width: 125rpx;
		height: 125rpx;
		top: 90rpx;
		right: 10rpx;
	}

	.couqlx {
		top: 0;
		left: -50rpx;
		width: 100rpx;
		height: 45rpx;
		font-size: 18rpx;
		line-height: 52rpx;
		background: linear-gradient(#ff3a48, #ff3a48);
		transform: rotate(-45deg);
		transform-origin: 50% 0%;
	}

	.counew {
		top: 0;
		left: -50rpx;
		width: 100rpx;
		height: 45rpx;
		font-size: 18rpx;
		line-height: 52rpx;
		background: linear-gradient(#96E666, #08C37D);
		transform: rotate(-45deg);
		transform-origin: 50% 0%;
	}

	/*cou2*/
	.cou2 {
		// border-radius: 10rpx;
	}

	.dot1,
	.dot2 {
		position: absolute;
		right: -16rpx;
		width: 30rpx;
		height: 15rpx;
		background: #EFF3F6;
	}

	.dot1 {
		top: -1.5rpx;
		border-radius: 0 0 30px 30px;
		border-top: 0;
	}

	.dot2 {
		bottom: -1.5rpx;
		border-radius: 30px 30px 0 0;
		border-bottom: 0;
	}

	.cou2l {
		width: 180rpx;
		border-right: 1px dashed #ddd;
	}

	/*cou3*/
	.cou3 {
		background: #F6F7F9;
		border-radius: 15rpx;

		.cou2l {
			width: 160rpx;
		}
	}

	.cou3br {
		width: 42rpx;
		padding: 5rpx 12rpx;
		background: #DDD;
	}

	/*cou5*/
	.cou5 {

		.cou5l {
			width: 115rpx;
			height: 108rpx;
		}

		.cou5r {
			padding-left: 10rpx;
		}
		.c5lq{
			color: #ff5436;
		}

		height: 174rpx;

		.cou5lq {
			background: #fe1e14;
			min-width: 106rpx;
			height: 54rpx;
		}

		.cou5ylq {
			width: 125rpx;
			height: 125rpx;
			bottom: -20rpx;
			right: 0;
		}
	}

	.be5 {
		background: #e5e5e5;
	}

	.cou6 {
		padding: 10rpx 15rpx;
	}

	.f66 {
		font-size: 66rpx;
	}

	.lh1 {
		line-height: 1;
	}

	.gfbg:after {
		background: #FFAFC0;
	}

	.gfbg:before {
		background: #FFAFC0;
	}

	.gfbtn {
		width: 112rpx;
		height: 48rpx;
		color: #fff;
		border-radius: 4rpx;
	}

	.gfbg {
		border-top: 1px dashed #eee;
	}

	.cef {
		color: #EF371F;
	}

	/*cou8*/
	.cou8 {
		.bgimg {
			width: 100%;
			height: 140rpx
		}

		.cqb {
			color: #7A4A1A;
		}

		.cou8m {
			border-right: 1px dashed #e2e2e2;
			padding-right: 20rpx;
		}

		.cou8zs {
			background: #F2D287;
			color: #7B4B1B;
			padding: 2rpx 10rpx;
		}

		.cou5lq {
			background: #F2D287;
			color: #7B4B1B;
		}

		.qbzkc {
			border: 0.5px solid #F7EAC6;
			color: #7B4B1B;
			padding: 2rpx 6rpx;
			border-radius: 4rpx;
		}
	}

	.cou9 {
		width: 690rpx;

		.cout {
			height: 190rpx;
			padding: 25rpx 30rpx 15rpx;
		}

		.cou9tl {
			width: 100rpx;
			height: 100rpx;
		}

		.qbyhq {
			width: 690rpx;
			height: 288rpx;
		}

		.dpimg {
			width: 30rpx;
			height: 30rpx;
		}

		.qqg {
			width: 123rpx;
			height: 54rpx;
			border-radius: 54rpx;
			background: linear-gradient(90deg, #FF315F, #FF4C21);
		}

		.jdt {
			width: 88rpx;
			height: 8rpx;
			border-radius: 10rpx;
			background: #FFB2B2;
		}

		.jd {
			border-radius: 10rpx;
			background: linear-gradient(90deg, #FF315F, #FF4C21);
		}

		.cou9b {
			border-radius: 60rpx 60rpx 0 0;
		}
	}

	.cou10 {
		.cout {
			padding: 15rpx 30rpx 15rpx;
		}

		.coub:before {
			background: transparent;
		}

		.ybk {
			top: 0;
			left: 0;
			height: 100%;
			width: 15rpx;
			background: linear-gradient(0deg, #F75214 20%, #F87D22 100%);
		}

		.cout::after {
			border-bottom: 1px dashed #FA5A16;
		}

		.num {
			top: 0;
			right: 0rpx;
			padding: 0 20rpx;
			height: 45rpx;
			font-size: 22rpx;
			line-height: 45rpx;
			border-bottom-left-radius: 20rpx;
			background: linear-gradient(#F75214, #F87D22);
		}
	}
</style>
