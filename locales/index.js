import VueI18n from 'vue-i18n'
import Vue from 'vue'

Vue.use(VueI18n)

// 自动加载语言文件
function loadLocaleMessages() {
	const locales = require.context(
		'./', // 搜索当前目录
		true, // 包含子目录
		/[A-Za-z0-9-_,\s]+\.js$/i // 匹配.js文件
	)

	const messages = {}

	locales.keys().forEach(key => {
		// 忽略index.js文件
		if (key === './index.js') return

		// 提取语言代码和模块名
		// 例如: './zh-TW/home.js' -> ['zh-TW', 'home']
		const matched = key.match(/\.\/([A-Za-z0-9-_]+)\/([A-Za-z0-9-_]+)\.js$/i)

		if (matched && matched.length > 2) {
			const locale = matched[1] // 语言代码 (zh-TW)
			const moduleName = matched[2] // 模块名 (home)

			// 初始化语言对象
			messages[locale] = messages[locale] || {}

			// 获取文件内容
			const fileModule = locales(key).default || locales(key)

			// 添加模块内容
			messages[locale][moduleName] = fileModule
		}
	})

	return messages
}

// 创建i18n实例
const i18n = new VueI18n({
	locale: uni.getStorageSync('language') || 'zh-TW',
	fallbackLocale: 'en-US',
	messages: loadLocaleMessages(),
	silentTranslationWarn: true,
	missing: (locale, key) => {
		if (process.env.NODE_ENV !== 'production') {
			console.warn(`[i18n] Missing translation for ${key} in ${locale}`)
		}
		return key
	}
})

// 添加切换语言方法
i18n.changeLocale = function(locale) {
  this.locale = locale
}

export default i18n