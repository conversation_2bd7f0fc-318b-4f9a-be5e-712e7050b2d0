export default{
	tabnev_all_good:'All products',
	evaluate:'Evaluate',
	merchant:'Merchant',
	monthly_sales:'Monthly sales',
	delivery_Schedule:'Delivery Schedule',
	minute:'minute',
	notice:'Notice',
	sign:'sign',
	merchant_rating:'Merchant rating',
	taste:'Taste',
	packages:'Package',
	Delivery_satisfaction:'Delivery satisfaction',
	all:'all',
	new:'new',
	pictures:'pictures',
	favorable_comment:'favorable comment',
	bad:'bad',
	view_food_safety_records:'View food safety records',
	merchant_services:'merchant services',
	cross_day_booking:'Cross day booking',
	pick_up_at_the_storez:'Pick up at the store',
	delivery:'delivery',
	alltoday: '24-hour operation',
	estimated_delivery_fee: 'Estimated delivery fee',
	product_details: 'Product Details',
	detail_text: 'Details',
	packing_charge: 'packing charge',
	empty_cart: 'empty cart',
	go_settle: 'Go settle',
	differ_from: 'differ from',
	start_sending: 'Start sending',
	sold_out: 'Sold Out',
	
	confirm_order:'confirm an order',
	takeout_delivery: 'Takeout delivery',
	pick_up_at_the_store:'Pick up at the store',
	send_it_out_immediately: 'Send it out immediately',
	appointment_for_delivery: 'Appointment for delivery',
	delivery_fee:'Delivery fee',
	discount:'U coins can be used as collateral',
	subtotal:'subtotal',
	discount_rules:'Discount Rules',
	quantity_of_tableware:'Quantity of tableware',
	submit_order_text:'submit order',
	remark:'Remark',
	remark_placeholder:'Please note your other requirements',
	selecte_placeholder:'not selected',
	
	selecte_time:'Select estimated arrival time/self pickup time',
	selecte_takeout_time:'Select estimated delivery time',
	
	in_store_dining:'In store dining',
	pack_and_take_away:'pack and take away',
	self_pickup_or_dining:'Self pickup/dining',
	reserved_phone_number:'Reserved phone number',
	
	agree_and_accept:'Agree and accept',
	agreement:'In store self pickup service agreement',

	select_invoice: 'Select the invoice',
	
	max_u_coin_usage: 'Maximum {amount} U Coins can be used',

	// New translations
	product_intro: 'Product Introduction',
	store_closed: 'Store is closed',
	store_paused: 'Business suspended',
	select_specs: 'Select specs',
	min_purchase: 'pieces minimum',
	like: 'Like',
	stock: 'Stock',
	limit_purchase: 'Limit {num} pieces',
	single_purchase: 'Single item only',
	sold_out_text: 'Sold out',
	required_category: 'Required',
	custom_category: 'Optional',
	invoice_info_incomplete: 'Invoice information is incomplete',
	select_category: 'Select category',

	classContent: 'Classified content',

	confirm_tip: 'Have you confirmed the cancellation of the order?',
	follow_tip: 'Are you sure you want to follow up on the order?',
	received_tip: 'Have you confirmed that you have received the goods?',
	delet_tip: 'Have you confirmed the deletion of the order?',

	process_tip: 'Please contact the store by phone for refund processing',
	refund_tip: 'Have you confirmed your application for a refund?'
}