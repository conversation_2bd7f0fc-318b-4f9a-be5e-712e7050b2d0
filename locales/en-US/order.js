export default{
	oreder_obligation_text:'To Pay',
	oreder_pending_text:'To Receive',
	oreder_refund_text:'Refund/After-sale',
	oreder_all_text:'All',
	waiting_for_payment:'Waiting for payment',
	remaining:'remaining',
	exceed:'If payment is not made within',
	tips:'Tips',
	tips_text:', the order will be automatically cancelled. Please make the payment as soon as possible!',
	expected:'be expected to',
	deliver:'deliver',
	cause:'cause',
	delivery:'Delivery',
	refund_tip_text:'The merchant will process your refund request within 24 hours',
	complete_tip_text:"Thank you for your trust in",
	complete_tip_text2:'Looking forward to your next visit!',
	pick_up_code:'Pick up code',
	self_pickup_time:'Self pickup time',
	business_address:'Business Address',
	cancelled_order_tip:'You cancelled the order',
	refund_amount:'refund amount',
	immediately_evaluate:'Immediately evaluate',
	amount_to:'amount to',
	shipping_info: 'Shipping Information',
	expected_time: 'expected time',
	immediate_delivery: 'Immediate delivery',
	shipping_address: 'Shipping address',
	distribution_service: 'distribution service',
	self_selected_info:'Retrieve information from oneself',
	self_selected_type:'Self selected type',
	order_info:'Ordering Information',
	order_number:'Order No.',
	order_time:'Order time',
	payment_method:'Payment method',
	unpaid:'Unpaid',
	over_now:"It's over now",
	order_total: 'Total {num} items',
	order_pickup_code: 'Pickup Code',
	order_again: 'Order Again',
	order_evaluate: 'Evaluate Now',
	order_evaluate_reward: 'Get {num} gold beans for evaluation',
	order_pay_now: 'Pay Now',
	order_confirm_receipt: 'Confirm Receipt',
	order_state: {
		waiting_payment: 'Waiting for Payment',
		paid: 'Paid',
		accepted: 'Order Accepted',
		delivering: 'Delivering',
		waiting_pickup: 'Waiting for Pickup',
		completed: 'Completed',
		evaluated: 'Evaluated',
		cancelled: 'Cancelled',
		rejected: 'Order Cancelled',
		refunding: 'Processing Refund',
		refunded: 'Refund Successful',
		refund_rejected: 'Refund Rejected'
	},
	state: [
	  '', 'Waiting for Payment', 'Paid', 'Order Accepted', 'Delivering', 'Completed', 'Reviewed', 'Cancelled', 'Cancel Order',
	  'Pending Store Processing', 'Refunded', 'Refund Denied'
	],
	ztState: [
	  '', 'Waiting for Payment', 'Paid', 'Order Accepted', 'Awaiting Pickup', 'Completed', 'Reviewed', 'Cancelled', 'Cancel Order',
	  'Pending Store Processing', 'Refunded', 'Refund Denied'
	],
	self_pickup:'Self-pickup',
	delivery:'Delivery',
	internal_use:'For internal use',
	stateArr: [
		'', 'Waiting for Payment', 'Order placed, waiting for store to accept',
		'Store has accepted the order', 'Order is being delivered',
		'Order completed', 'Reviewed', 'Order cancelled', 'Store rejected the order', 'Refund in process, waiting for store', 'Refund approved', 'Refund rejected'
	],
	ztStateArr: [
		'', 'Waiting for Payment', 'Order placed, waiting for store to accept',
		'Store has accepted the order, preparing', 'Awaiting pickup',
		'Order completed', 'Reviewed', 'Order cancelled', 'Store rejected the order', 'Refund in process, waiting for store', 'Order cancelled', 'Refund rejected'
	],
	pay_now: 'Pay Now',
	order_again: 'Order Again',
	reminder: 'Remind',
	complete_order: 'Complete Order',
	cancel_order: 'Cancel Order',
	confirm_receipt: 'Confirm Receipt',
	store: 'Store',
	supplier: 'Union Shopping',
}