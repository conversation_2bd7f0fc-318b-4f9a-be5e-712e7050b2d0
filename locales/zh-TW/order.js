export default {
	oreder_obligation_text: '待付款',
	oreder_pending_text: '待取貨',
	oreder_refund_text: '退款/售後',
	oreder_all_text: '全部',
	order_number: '訂單編號',
	order_total: '共{num}件',
	order_pickup_code: '取貨碼',
	order_again: '再來一單',
	order_evaluate: '立即評價',
	order_evaluate_reward: '評價得{num}金豆',
	order_pay_now: '立即付款',
	order_confirm_receipt: '確認取貨',
	order_state: {
		waiting_payment: '等待付款',
		paid: '已付款',
		accepted: '已接單',
		delivering: '外送中',
		waiting_pickup: '待取貨',
		completed: '已完成',
		evaluated: '已評價',
		cancelled: '已取消',
		rejected: '取消訂單',
		refunding: '待商店處理',
		refunded: '退款成功',
		refund_rejected: '退款已拒絕'
	},
	waiting_for_payment: '等待付款',
	remaining: '剩餘',
	exceed: '超過',
	tips: '溫馨提示',
	tips_text: '分鐘尚未付款，訂單將自動取消哦，請您儘快付款！',
	expected: '預計',
	deliver: '送達',
	cause: '由',
	delivery: '外送',
	refund_tip_text: '商店將在24小時內處理您的退款申請',
	complete_tip_text: '感謝您對',
	complete_tip_text2: '的信任，期待下次光臨！',
	pick_up_code: '取貨碼',
	self_pickup_time: '自取時間',
	business_address: '營業地址',
	cancelled_order_tip: '您取消了訂單',
	refund_amount: '退款金額',
	immediately_evaluate: '立即評價',
	amount_to: '總計',
	shipping_info: '外送資訊',
	expected_time: '預計時間',
	immediate_delivery: '立即外送',
	shipping_address: '外送地址',
	distribution_service: '外送服務',
	self_selected_info: '自取資訊',
	self_selected_type: '類型',
	order_info: '訂單資訊',
	order_time: '下單時間',
	payment_method: '付款方式',
	unpaid: '尚未付款',
	over_now: '到底了',
	state: [
	  '', '等待付款', '已付款', '已接單', '外送中', '已完成', '已評價', '已取消', '取消訂單',
	  '待商店處理', '退款成功', '退款已拒絕'
	],
	ztState: [
	  '', '等待付款', '已付款', '已接單', '待取貨', '已完成', '已評價', '已取消', '取消訂單',
	  '待商店處理', '退款成功', '退款已拒絕'
	],
	self_pickup:'自取',
	delivery:'寄送',
	internal_use:'內用',
	stateArr: [
		'', '等待付款', '下單成功，等待商店接單', '商店已接單', '訂單外送中',
		'訂單已完成', '已評價', '訂單已取消', '商店已拒單', '退款中，等待商店處理',
		'退款已通過', '退款已拒絕'
	],
	ztStateArr: [
		'', '等待付款', '下單成功，等待商店接單', '商店已接單，準備中', '待取貨',
		'訂單已完成', '已評價', '訂單已取消', '商店已拒單', '退款中，等待商店處理',
		'訂單已取消', '退款已拒絕'
	],
	pay_now: '立即付款',
	order_again: '再來一單',
	reminder: '催單',
	complete_order: '完成訂單',
	cancel_order: '取消訂單',
	confirm_receipt: '確認取貨',
	store: '商店',
	supplier: '優力購物'
}