import Vue from 'vue'
import Vuex from 'vuex'
import util from '../common/we7/util';
import api from '../common/api';
import utils from '@/common/utils.js'
Vue.use(Vuex)

import dndc from './dndc'
import address from './address';

const store = new Vuex.Store({
	state: {
		canOrder: {
			singleIds: [],
			mustIds: []
		}, //设置单点不送 和必选商品的id
		systemInfo: {
			pxToRpxRate: null
		},
		layout: { //拖拽式数据
			index: {},
			custom: {},
			personalcenter: {},
		},
		config: {
			orderSet: {
				autoClose: undefined
			}, //订单配置
			recharge: undefined, //充值配置
			payConfig: undefined, //支付配置
			currency: undefined, //外卖设置
			storeSet: undefined, //商户设置
			payVipset: undefined,
			payOpen: undefined
		},
		system: {},
		user: {
			// balance:0,
		},
		shopGoodsInfo: {
			discount: {},
			shopData: {
				businessState: {}
			},
			categorySet: {
				delColor: '#F45439',
				addColor: '#ffffff',
				addColor2: '#F45439'
			}
		}, //购物车配置信息
		isIpx: false,
		isLogin: false, // 默认未登录
		// 登录为对接时 isLogin:true
		// isLogin: true,
		carList: [], //本地购物车
		scarList: {
			out: {
				data: []
			},
			fast: {
				data: []
			},
			ins: {
				data: []
			},
		},
		storeInfo: {},
	},
	mutations: {
		setConfig(state, data) {
			Vue.set(state.config, data.name, data.data)
			// state.config[data.name]=data.data
		},
		setCanOrder(state, data) {
			// console.log(data)
			state.canOrder = {}
			state.canOrder = data
		},
		setShopGoodsInfo(state, data) {
			state.shopGoodsInfo = {}
			state.shopGoodsInfo = data
		},
		setSystemInfo(state, data) {
			data.pxToRpxRate = (750 / data.windowWidth).toFixed(2)
			data.MainPageHeight = data.windowHeight * data.pxToRpxRate - 140
			state.systemInfo = data
		},
		setLayout(state, {
			params,
			data
		}) { //拖拽数据
			state.layout[params.page] = data
		},
		setSystem(state, data) {
			state.system = data
		},
		setUser(state, data) {
			uni.setStorageSync('userId', data.id)
			// state.user = Object.assign(state.user,data)
			Vue.set(state, 'user', data)
			if (data.session_key) {
				getApp().globalData.session_key = data.session_key
			}
			// 根据user数据是否存在来设置登录状态
			state.isLogin = !!data && Object.keys(data).length > 0
		},
		setScarList(state, data) { //购物车商品
			// console.log('setScarList', data, state.scarList)
			if (!data.key) {
				state.scarList['out'] = data.data
			} else {
				state.scarList[data.key] = data.data
			}
		},
		setStoreInfo(state, data) {
			state.storeInfo = {}
			state.storeInfo = data
		},
	},
	getters: {

	},
	actions: {
		async getConfig({
			commit,
			state
		}, params) {
			if (state.config[params.name] !== undefined) return
			let {
				data
			} = await util.request({
				'url': params.api,
				method: params.method || 'GET',
				// mask: 1,
				data: params.data
			})
			let config = {
				data: data,
				name: params.name
			}
			commit('setConfig', config)
		},
		setPayInfo({
			commit,
			state
		}, params) {
			uni.setStorageSync('payInfo', params)
		},
		async refreshUser({
			commit,
			state
		}, params = {
			now: 1
		}) {
			// console.log(params)
			// return
			let {
				data
			} = await util.request({
				'url': api.xgyh,
				method: 'GET'
			})
			if (params.now) {
				data && commit('setUser', data)
			} else {
				setTimeout(() => {
					data && commit('setUser', data)
				}, 200);
			}
			return data
		},
		//获取用户信息的同时判断是否登录
		async getLoginInfo({
			commit,
			state
		}, params = {}) {
			if (state.user.id) {
				return
			} else {
				return await new Promise(async (resolve, reject) => {
					// #ifndef H5
					// util.showLoading()
					uni.login({
						// #ifdef MP-ALIPAY
						scopes: 'auth_user', //支付宝小程序需设置授权类型
						// #endif
						success: async (lres) => {
							// console.log('uni.login', lres)
							let res = await util.request({
								'url': api.login,
								data: {
									code: lres.code,
									inviteId: params.inviteId || '',
									type: params.type || '',
								}
							})
							if (res) {
								// console.timeEnd('login')
								// 判断是否登录
								commit('setUser', res.data)
								resolve()
							} else {
								reject()
								util.modal('请检查小程序秘钥等相关配置')
							}
						},
						fail: (err) => {
							console.log('接口调用失败，将无法正常使用开放接口等服务', err)
							reject(err)
						}
					})
					// #endif
					// // #ifdef H5
					// let link = window.location.href;
					// let params = utils.getUrlParams('https://bkycms.com/addons/yb_o2o/template/mobile/index.html?acid=6#/yb_o2o/index/index?i=85'); // 地址解析
					// console.log('%c params ', 'color: white; background-color: #95B46A', params)
					// commit('setUser', {
					// 	userName: 'h5测试',
					// 	userId: '1'
					// })
					// resolve()
					// if (api.platform == 'weChat') {
					// 	// console.log('%c api.platform ', 'color: white; background-color: #95B46A', api.platform)
					// 	if (!getApp().globalData.siteInfo.isDev) {
					// 		util.showLoading()
					// 		let [error, res] = await uni.request({
					// 			'url': `${getApp().globalData.siteInfo.siteroot}?i=${getApp().globalData.siteInfo.uniacid}&c=entry&do=login&m=yb_o2o`,
					// 			'data': {
					// 				inviteId: params.inviteId || '',
					// 			},
					// 			'method': 'GET',
					// 			'header': {
					// 				'content-type': 'application/x-www-form-urlencoded',
					// 				"appType": api.platform,
					// 			}
					// 		});
					// 		if (res.data.errno == 0) {
					// 			uni.hideLoading();
					// 			commit('setUser', res.data.data)
					// 			resolve()
					// 		} else {
					// 			util.message(res.data.message || res.data, 3, 2000)
					// 		}
					// 		console.log('%c login data ', 'color: white; background-color: #34aaff', res.data)
					// 	} else {
					// 		let {
					// 			data
					// 		} = await util.request({
					// 			'url': api.login,
					// 			mask: 1,
					// 			data: {
					// 				inviteId: params.inviteId || '',
					// 			}
					// 		})
					// 		commit('setUser', data)
					// 		resolve()
					// 	}
					// } else {
					// 	commit('setUser', {
					// 		id: "1",
					// 		userId: '1',
					// 		openId: "oDeY75LHzFVjU9zIJWNlsmFmTQrk",
					// 		userName: "xx",
					// 		userTel: "13823515936",
					// 		portrait: "https://wx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTIo7dEQyibJqmcKjX9iaFo2zMQTxVLXOZZXFC7CRu9d3vod0ic7Ria7NQaMkojdkOEwcibRn4IicB5bmtyQ/132"
					// 	})
					// 	resolve()
					// 	console.log('%c api.platform ', 'color: white; background-color: #95B46A', api.platform)
					// }
					// // #endif
				})
			}

		},
		async setSystemInfo({
			commit
		}) {
			var systemInfo = uni.getSystemInfoSync()
			const statusBarHeight = systemInfo.statusBarHeight
			systemInfo.statusNavBarHeight = statusBarHeight
			systemInfo.menuButtonTop = 0
			systemInfo.menuButtonHeight = 0
			// 如果是小程序，获取右上角胶囊的尺寸信息，避免导航栏右侧内容与胶囊重叠(支付宝小程序非本API，尚未兼容)
			// #ifdef MP-WEIXIN || MP-BAIDU || MP-TOUTIAO || MP-QQ
			const {
				left,
				top,
				height,
				width
			} = uni.getMenuButtonBoundingClientRect()
			// 计算导航栏的高度
			// 此高度基于右上角菜单在导航栏位置垂直居中计算得到
			// const navBarHeight = height + (top - statusBarHeight) * 2;
			// 计算状态栏与导航栏的总高度
			// const statusNavBarHeight = statusBarHeight + navBarHeight;
			systemInfo.statusNavBarHeight = (height + 2 * top - statusBarHeight)
			systemInfo.menuButtonLeft = left
			systemInfo.menuButtonTop = top
			systemInfo.menuButtonHeight = height
			systemInfo.menuButtonWidth = width
			// #endif
			// #ifdef APP-PLUS
			systemInfo.menuButtonTop = systemInfo.safeArea.top
			systemInfo.statusNavBarHeight = statusBarHeight * 2
			// #endif
			commit('setSystemInfo', systemInfo)
		},
		async getSystem({
			commit,
			state
		}, params = {}) {
			// console.log('state', state, params)
			if (!state.system.color || params.get) {
				let res = {};
				state.isIpx = util.getSb().model.search('iPhone X') != -1 || util.getSb().model.search(
						'iPhone 1') != -1 || util
					.getSb().model.search('iPhone1') != -1
				//console.log(state.isIpx)
				res = await util.request({
					'url': api.config,
					data: {
						ident: 'system'
					}
				})
				res.data.style = res.data.style || {}
				res.data.color = res.data.style.color || '#FF5F2F'
				res.data.fontColor = res.data.style.fontColor || '#333333'
				// res.data.pm = api.platform
				commit("setSystem", res.data);
				params.setNB && util.setNB(res.data.color, params.nofc)
			} else {
				params.setNB && util.setNB(state.system.color, params.nofc)
			}
			// // #ifndef MP-TOUTIAO
			// // #endif
		},
		//拖拽数据
		async getLayout({
			commit,
			state
		}, params = {
			page: 'index',
			id: '1',
		}) {
			let res = await util.request({
				'url': api.layout,
				data: params
			})
			if (res) {
				commit('setLayout', {
					params,
					data: res.data
				})
			}
		},
		async checkBindTel({
			commit,
			rootState
		}, params) {
			return new Promise((reslove, reject) => {
				if (!rootState.user.userTel) {
					uni.showModal({
						title: '提示',
						content: '请先绑定手机号',
						confirmText: '前往绑定',
						cancelText: '暂不绑定',
						success: res => {
							if (res.confirm) {
								uni.navigateTo({
									url: '/yb_o2o/my/login/index',
								});
							}
						}
					});
				} else {
					reslove()
				}
			})
		},
		async getMycar({
			commit,
			state
		}, params) {
			let res = await util.request({
				'url': api.wdgwc,
				mask: params.mask,
				data: params
			})
			res && commit('setScarList', {
				key: params.key,
				data: res.data
			})
			// console.log('index.js,getMycar', params, state.scarList)
		},
		//数据库car
		async clearMycar({
			commit,
			state
		}, params) {
			let res = await util.request({
				'url': api.clearCart,
				method: 'POST',
				mask: 1,
				data: params
			})
			res && commit('setScarList', {
				key: params.key,
				data: {}
			})
		},
		async supdCar({
			dispatch,
			commit,
			state
		}, params) {
			let res = await util.request({
				'url': api.saveShopCar,
				ct: 1,
				method: 'POST',
				// mask: 1,
				data: params
			})
			// console.log('index.js,SaveShopCar', res)
			if (res) {
				commit('setScarList', {
					key: params.key,
					data: res.data
				})
				return +res.count
				// dispatch('getMycar', {
				// 	userId: params.userId,
				// 	item: params.item,
				// 	storeId: params.storeId,
				// 	tableId: params.tableId,
				// })
				// console.log(this)
			}
			// console.log('index.js,supdCar', params)
		},
	},
	modules: {
		dndc,
		address
	}
})

export default store