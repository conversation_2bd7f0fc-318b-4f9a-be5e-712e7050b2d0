export default {
	namespaced: true,
	state: {
		tempFormData: {
			id: '',
			address: '',
			details: '',
			label: '',
			userName: '',
			userTel: '',
			sex: 1,
			lat: '',
			lng: ''
		}
	},
	actions: {
		/**
		 * 保存表单数据到临时存储
		 * @param {Object} context 
		 * @param {Object} formData 表单数据
		 */
		setAddressForm({
			commit
		}, data) {
			commit('UPDATE_TEMP_FORM', data)
		},
	},
	mutations: {
		/**
		 * 更新临时表单数据
		 * @param {Object} state 
		 * @param {Object} data 表单数据对象
		 */
		UPDATE_TEMP_FORM(state, data) {
			state.tempFormData = {
				...state.tempFormData,
				...data
			};
		},
	}
}