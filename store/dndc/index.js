import util from '../../common/we7/util.js';
import api from '../../common/api.js';

export default {
	namespaced: true,
	state: {
		dndcConfig: {},
		ldxConfig: {},
		smConfig: {},
		addInfo: null,
		cityInfo: {},
		latLng: {},
		// regionId:uni.getStorageSync('setRegionId')?uni.getStorageSync('setRegionId'):{},
		regionId: {},
		myAdData: [], //个人中心广告
		mySwitch: {}, //开关
		fxsInfo: {},
		fxsSq: {},
	},
	mutations: {
		setDndcConfig(state, data) {
			state.dndcConfig = data
		},
		setLdxConfig(state, data) {
			state.ldxConfig = data || {}
		},
		setSmConfig(state, data) {
			state.smConfig = data
		},
		setAddInfo(state, data) {
			state.addInfo = data
		},
		setCityInfo(state, data) {
			state.cityInfo = data
		},
		setLatLng(state, data) {
			state.latLng = data
			// console.log('dndc setLatLng', data)
		},
		setRegionId(state, data) {
			state.regionId = data
			console.log('dndc setRegionId', data)
		},
		setMyAdData(state, data) {
			state.myAdData = data
		},
		setMySwitch(state, data) {
			state.mySwitch = data
		},
		setFsxInfo(state, data) {
			state.fxsInfo = data
		},
		setFsxSq(state, data) {
			state.fxsSq = data
		},
	},
	actions: {
		/**
		 * @description 调用各个平台API获取定位信息经纬度
		 * @param {object} {latitude,longitude,...args}
		 * @constructor  
		 */
		//定位信息
		async getAddInfo({
			commit,
			state
		}, params) {
			let add = {
				maddress: '',
				city: '',
			}
			let {
				data
			} = await util.request({
				'url': api.zbtdz,
				method: 'POST',
				data: {
					lat: JSON.parse(uni.getStorageSync('locationInfo')).latitude,
					lng: JSON.parse(uni.getStorageSync('locationInfo')).longitude,
				},
				is_login: 0
			})
			add.maddress = JSON.parse(uni.getStorageSync('locationInfo')).maddress || data.result.formatted_addresses && data.result
				.formatted_addresses.recommend ||
				data.result.address
			add.city = data.result.address_component.city
			commit('setLatLng', JSON.parse(uni.getStorageSync('locationInfo')))
			commit('setAddInfo', add)
			commit('setCityInfo', {
				cityName: add.city,
				cityId: 123
			})
		},
		async getRegionId({
			commit,
			state
		}, params) {
			const savedLocation = JSON.parse(uni.getStorageSync('locationInfo'));
			let {
				data
			} = await util.request({
				'url': api.regionList,
				method: 'GET',
				data: {
					lat: savedLocation.latitude,
					lng: savedLocation.longitude,
				},
				is_login: 0
			})
			if (data && data.length) {
				commit('setRegionId', data[0])
				uni.setStorageSync('setRegionId', data[0])
			}
		},
		//设置
		async getSwitchConfig({
			commit,
			state
		}, params) {
			if (!state.mySwitch.hasOwnProperty('jfName')) {
				let res = await util.request({
					'url': api.Getswitch,
					is_login: 0
				})
				res && commit('setMySwitch', res.data)
			}
		},
		//设置
		async getDndcConfig({
			commit,
			state
		}, params) {
			if (!state.dndcConfig.location) {
				let res = await util.request({
					'url': api.config,
					data: {
						ident: 'instoreset'
					},
					is_login: 0
				})
				res && commit('setDndcConfig', res.data)
			}
		},
		//老带新
		async getLdxConfig({
			commit,
			state
		}, params) {
			if (!state.ldxConfig.location) {
				let res = await util.request({
					'url': api.config,
					data: {
						ident: params
					},
					is_login: 0
				})
				res && commit('setLdxConfig', res.data)
			}
		},
		//模板ids
		async getSmConfig({
			commit,
			state
		}, params) {
			if (!state.smConfig.saveOrder) {
				let res = await util.request({
					'url': api.wmmbxx,
					mask: 1,
					is_login: 0
				})
				res && commit('setSmConfig', res.data)
				return
			}
		},
		//
		async getMyAd({
			commit,
			state
		}, params) {
			if (!state.myAdData.length) {
				let res = await util.request({
					'url': api.PlatformAdList,
					data: {
						location: 6,
						type: 1,
					},
					is_login: 0
				})
				res && commit('setMyAdData', res.data)
			}
		},
		//
		async getFxzx({
			commit,
			state
		}, params) {
			let res = await util.request({
				'url': api.config,
				data: {
					ident: 'distributionSet'
				},
				is_login: 0
			})
			if (res) {
				// res.data.createdAt = res.data.createdAt.substring(0, 16)
				commit('setFsxInfo', res.data)
			} else {
				setTimeout(() => {
					params.that.go({
						t: 5,
						url: '/yb_o2ov2/index/my-index'
					})
				}, 1500)
			}
		},
		async getFxSq({
			commit,
			state
		}, params) {
			let res = await util.request({
				'url': api.distributionAD,
				mask: 1,
				is_login: 0
			})
			if (res) {
				commit('setFsxSq', res.data)
			} else {
				setTimeout(() => {
					params.that.go({
						t: 5,
						url: '/yb_o2ov2/index/my-index'
					})
				}, 1500)
			}
		},
	}
}